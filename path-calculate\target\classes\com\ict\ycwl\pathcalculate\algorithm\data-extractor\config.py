"""
数据库配置文件
用于配置数据库连接信息和提取参数
"""

# 数据库连接配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '16281628',  # 请修改为实际密码
    'database': 'ycdb',
    'charset': 'utf8mb4'
}

# 数据提取配置
EXTRACTION_CONFIG = {
    # 输出目录配置
    'output_base_dir': '../data',
    
    # 版本配置
    'default_version': 'v1.0',
    
    # 数据规模配置
    'data_scales': {
        'small': {
            'max_accumulations': 50,
            'max_transit_depots': 5,
            'max_teams': 3,
            'description': '小规模测试数据'
        },
        'medium': {
            'max_accumulations': 200,
            'max_transit_depots': 10,
            'max_teams': 6,
            'description': '中等规模测试数据'
        },
        'large': {
            'max_accumulations': 500,
            'max_transit_depots': 20,
            'max_teams': 10,
            'description': '大规模测试数据'
        },
        'full': {
            'max_accumulations': None,  # 不限制
            'max_transit_depots': None,
            'max_teams': None,
            'description': '全量数据'
        }
    },
    
    # 数据质量过滤配置
    'quality_filters': {
        # 坐标有效性检查
        'coordinate_bounds': {
            'longitude_min': 113.0,
            'longitude_max': 116.0,
            'latitude_min': 23.0,
            'latitude_max': 26.0
        },
        
        # 排除无效数据
        'exclude_deleted': True,
        'exclude_invalid_coordinates': True,
        
        # 默认配送时间（分钟）
        'default_delivery_time': 15.0,
        'default_route_count': 10
    }
}

# SQL查询配置
SQL_QUERIES = {
    # 班组查询 - 基础查询，不依赖其他表
    'teams': """
        SELECT 
            group_id as team_id,
            group_name as team_name
        FROM `group`
        ORDER BY group_id
        LIMIT %(limit)s
    """
    # 注意：其他查询已迁移到代码中动态构建，以确保关系一致性
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'data_extraction.log'
} 