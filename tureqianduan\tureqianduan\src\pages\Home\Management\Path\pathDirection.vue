<template>
    <el-dialog
      v-model="vis"
      title="路径指引"
      class="transform"
      align-center
      :close-on-click-modal="false"
    >
      <el-carousel width="400px" height="600px" arrow="always">
        <el-carousel-item v-for="item in 4" :key="item +'6'">
          <el-image style="height: 600px;" :src="`public/0${item}.jpg`" fit="contain"></el-image>
        </el-carousel-item>
      </el-carousel>
    </el-dialog>
  </template>
  
<script setup lang="ts">
const vis = ref<boolean>(false)

defineExpose({
  vis
});

</script>
  
<style lang="less" scoped>
.transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
}
</style>
  