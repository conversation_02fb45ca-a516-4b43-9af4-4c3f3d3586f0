<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.WorkParameterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ict.ycwl.pathcalculate.pojo.WorkParameter">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="max_work_hours" property="maxWorkHours" />
        <result column="flexible_range" property="flexibleRange" />
        <result column="optimal_work_hours" property="optimalWorkHours" />
        <result column="stop_threshold" property="stopThreshold" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, max_work_hours, flexible_range, optimal_work_hours, stop_threshold, create_time, update_time, is_deleted
    </sql>

    <!-- 根据站点名称查询工作参数 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM work_parameter
        WHERE name = #{name} AND is_deleted = 0
    </select>

    <!-- 获取全局参数（韶关市） -->
    <select id="selectGlobalParameter" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM work_parameter
        WHERE name = '韶关市' AND is_deleted = 0
    </select>

</mapper>
