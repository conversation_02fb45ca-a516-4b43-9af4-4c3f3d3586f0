package com.ict.ycwl.pathcalculate.utils;

import com.ict.ycwl.pathcalculate.utils.dbDataSourceUtils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * 历史版本功能数据库初始化工具
 * 用于修复历史版本功能无法使用的问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-11
 */
@Slf4j
@Component
public class VersionDatabaseInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${jjking.dbPath:}")
    private String dbPath;

    // 需要检查的核心表
    private static final List<String> CORE_TABLES = Arrays.asList(
        "version", "route", "accumulation", "store", "transit_depot", 
        "team", "route_detail", "system_parameter"
    );

    @Override
    public void run(String... args) throws Exception {
        log.info("开始检查历史版本功能数据库配置...");
        
        try {
            // 1. 检查masterDatasource.txt文件
            checkMasterDatasourceFile();
            
            // 2. 检查主数据库版本表
            checkMainVersionTable();
            
            // 3. 检查slave数据库连接
            checkSlaveDatabases();
            
            log.info("历史版本功能数据库检查完成");
            
        } catch (Exception e) {
            log.error("历史版本功能数据库检查失败", e);
        }
    }

    /**
     * 检查masterDatasource.txt文件
     */
    private void checkMasterDatasourceFile() {
        try {
            if (dbPath == null || dbPath.isEmpty()) {
                log.warn("jjking.dbPath 配置为空，请检查配置文件");
                return;
            }

            File file = new File(dbPath);
            if (!file.exists()) {
                log.warn("masterDatasource.txt 文件不存在，正在创建: {}", dbPath);
                createMasterDatasourceFile();
            } else {
                String content = FileUtil.readSingleLine(dbPath);
                log.info("当前数据源配置: {}", content);
                
                if (content == null || content.trim().isEmpty()) {
                    log.warn("masterDatasource.txt 文件内容为空，正在初始化");
                    writeMasterDatasourceFile("master");
                }
            }
        } catch (Exception e) {
            log.error("检查masterDatasource.txt文件失败", e);
        }
    }

    /**
     * 创建masterDatasource.txt文件
     */
    private void createMasterDatasourceFile() {
        try {
            File file = new File(dbPath);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            writeMasterDatasourceFile("master");
            log.info("masterDatasource.txt 文件创建成功: {}", dbPath);
            
        } catch (Exception e) {
            log.error("创建masterDatasource.txt文件失败", e);
        }
    }

    /**
     * 写入masterDatasource.txt文件内容
     */
    private void writeMasterDatasourceFile(String content) throws IOException {
        try (FileWriter writer = new FileWriter(dbPath)) {
            writer.write(content);
            writer.flush();
        }
    }

    /**
     * 检查主数据库版本表
     */
    private void checkMainVersionTable() {
        try {
            // 检查version表是否存在
            if (!tableExists("version")) {
                log.warn("主数据库缺少version表，请执行初始化SQL脚本");
                return;
            }

            // 检查版本数据
            String sql = "SELECT COUNT(*) FROM version WHERE is_show = 1";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            
            if (count == null || count == 0) {
                log.warn("version表中没有可显示的版本数据，正在初始化...");
                initializeVersionData();
            } else {
                log.info("version表检查通过，共有 {} 个可显示版本", count);
            }

        } catch (Exception e) {
            log.error("检查主数据库版本表失败", e);
        }
    }

    /**
     * 初始化版本数据
     */
    private void initializeVersionData() {
        try {
            String sql = "INSERT IGNORE INTO version (version_id, version_name, version_db, version_info, is_show) VALUES " +
                        "(1, '主版', 'master', '系统主版本，包含当前生产数据', 1), " +
                        "(2, '版本1', 'slave1', '历史版本1，用于备份和测试', 0), " +
                        "(3, '版本2', 'slave2', '历史版本2，用于备份和测试', 0), " +
                        "(4, '版本3', 'slave3', '历史版本3，用于备份和测试', 0)";
            
            int rows = jdbcTemplate.update(sql);
            log.info("初始化版本数据完成，插入 {} 条记录", rows);
            
        } catch (Exception e) {
            log.error("初始化版本数据失败", e);
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(String tableName) {
        try (Connection connection = jdbcTemplate.getDataSource().getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                return rs.next();
            }
        } catch (SQLException e) {
            log.error("检查表是否存在失败: {}", tableName, e);
            return false;
        }
    }

    /**
     * 检查slave数据库连接
     */
    private void checkSlaveDatabases() {
        List<String> slaveDbs = Arrays.asList("ycwl_slave1", "ycwl_slave2", "ycwl_slave3");
        
        for (String dbName : slaveDbs) {
            try {
                // 尝试查询slave数据库
                String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ?";
                Integer tableCount = jdbcTemplate.queryForObject(sql, Integer.class, dbName);
                
                if (tableCount == null || tableCount == 0) {
                    log.warn("数据库 {} 中没有表，可能需要初始化", dbName);
                } else {
                    log.info("数据库 {} 检查通过，共有 {} 个表", dbName, tableCount);
                }
                
            } catch (Exception e) {
                log.error("检查数据库 {} 失败", dbName, e);
            }
        }
    }

    /**
     * 手动触发初始化（用于调试）
     */
    public void manualInitialize() {
        try {
            run();
        } catch (Exception e) {
            log.error("手动初始化失败", e);
        }
    }
}
