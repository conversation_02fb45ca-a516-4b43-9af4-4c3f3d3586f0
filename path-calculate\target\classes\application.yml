# ===== 原始配置（已注释保留） =====
#spring:
#  application:
#    # 应用名称
#    name: pathcalculate
#  datasource:
#    url: *******************************************************************************************************&
#    username: root
#    password: 123
#    driver-class-name: com.mysql.jdbc.Driver

# ===== 临时本地数据库配置（确保另存为新版本功能正常工作） =====
# 注意：这是临时配置，用于解决数据库连接问题
# 一旦Nacos配置问题解决，可以注释掉此部分，恢复使用Nacos配置
spring:
  application:
    name: pathcalculate
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: *************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave1:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave2:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave3:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      initial-size: 15
      min-idle: 15
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: ""
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      connection-properties: false

# ===== 自定义配置 =====
jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt

# ===== 新算法配置 =====
ycwl:
  algorithm:
    # 是否启用新算法
    enable-new-algorithm: true

    # 算法超时时间（毫秒）
    timeout-ms: 300000

    # 是否启用算法降级（新算法失败时使用旧算法）
    enable-fallback: true

    # 最大重试次数
    max-retry-count: 3

    # 算法执行模式：SYNC（同步）或 ASYNC（异步）
    execution-mode: SYNC

    # 数据验证开关
    enable-data-validation: true

    # 结果缓存开关
    enable-result-cache: false

    # 算法参数配置
    params:
      # 最大路线数量
      max-route-count: 50

      # 最小聚集区数量（少于此数量不执行算法）
      min-accumulation-count: 2

      # 最大工作时间（小时）
      max-work-time-hours: 8.0

      # 平均行驶速度（km/h）
      average-speed: 30.0

      # 配送时间系数
      delivery-time-factor: 1.0

      # 是否考虑交通拥堵
      consider-traffic: true

      # 是否优化路线顺序
      optimize-route-order: true

      # 路线平衡系数
      route-balance-factor: 0.8

# 聚类二次优化配置
algorithm:
  clustering:
    post-optimization:
      enabled: true

# 日志配置 - 屏蔽第三方库的DEBUG日志
logging:
  level:
    # 第三方库日志控制
    com.graphhopper.jsprit: WARN
    org.optaplanner: WARN
    org.drools: WARN
    # 算法详细调试日志
    com.ict.ycwl.pathcalculate.algorithm: INFO
