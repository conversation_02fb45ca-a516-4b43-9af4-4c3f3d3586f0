package com.ict.ycwl.clustercalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("accumulation")
public class Accumulation {

    @TableId(type = IdType.ASSIGN_ID)
    private Long accumulationId;

    private String leaderName;

    private String accumulationName;

    private String leaderPhone;

    private Double longitude;

    private Double latitude;

    private String areaName;

    private Timestamp createTime;

    private Timestamp updateTime;

    private Boolean isDelete;

    private String accumulationAddress;

    private Long routeId;

    private Long transitDepotId;

    private Long areaId;
}
