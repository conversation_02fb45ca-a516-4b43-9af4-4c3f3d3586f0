<template>
  <el-dialog
    v-model="uploadVisible"
    title="导入表格"
    class="transform"
    width="50%"
    align-center
    :close-on-click-modal="false"
    @close="cancelDialog"
  >
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      action=""
      :limit="1"
      :file-list="fileList"
      :http-request="uploadFile"
      :multiple="false"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :auto-upload="false"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">拖拽文件到此处<em>点击上传</em></div>
      <div class="el-upload__text" :style="{ width: '100%' }">
        <span style="color: red"
          >* 一次只能导入一个文件,仅支持xls, xlsx和csv格式</span
        >
      </div>
      <template #tip>
        <div class="download">
          <el-button
            type="primary"
            :icon="Download"
            class="button"
            @click="downloadNullLocationForm"
            >点击此处下载空格表格</el-button
          >
        </div>
      </template>
    </el-upload>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog" type="primary">取消</el-button>
        <el-button type="primary" @click="confirmDialog"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="tableVisible"
    title="上传文件"
    class="transform"
    width="50%"
    align-center
    :close-on-click-modal="false"
    @close="cancelUploadVis"
  >
    <el-table
      ref="tableRef"
      :data="uploadList"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{
        height: '1vh',
        'text-align': 'center',
      }"
      size="small"
      :row-style="{ height: '4vh' }"
      style="font-size: 1vw"
    >
      <el-table-column label="文件名" min-width="1%" prop="fileName" />
      <el-table-column label="大小" min-width="1%" prop="fileSize" />
      <el-table-column
        v-if="uploadList[0].fileName"
        label="状态"
        min-width="1%"
      >
        <el-progress
          :text-inside="true"
          :stroke-width="26"
          :percentage="percentage"
        />
      </el-table-column>
      <el-table-column
        v-if="uploadList[0].fileName"
        label="操作"
        min-width="1%"
      >
        <template #default>
          <el-button
            size="small"
            type="primary"
            @click="deleteLogData"
            :icon="CloseBold"
          >
            删除
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="reUpload"
            :icon="RefreshRight"
          >
            重传
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup lang="ts">
  import {
    CloseBold,
    RefreshRight,
    UploadFilled,
    Download,
  } from "@element-plus/icons-vue";
  import { useLocationStore } from "@/store/pos";
  import {
    genFileId,
    UploadInstance,
    UploadRawFile,
    UploadUserFile,
  } from "element-plus";

  const fileList = ref<UploadUserFile[]>([]);
  const fileType = ref(["csv", "xls", "xlsx"]);
  const uploadList = ref<any>([
    {
      fileName: "",
      fileSize: "",
    },
  ]);
  const store = useLocationStore();
  const uploadVisible = ref<boolean>(false);
  const tableVisible = ref<boolean>(false);
  const uploadRef = ref<UploadInstance>();
  const percentage = ref<number>(0);
  const controller = new AbortController();

  defineExpose({
    uploadVisible,
  });

  function cancelDialog() {
    uploadRef.value!.clearFiles();
    uploadVisible.value = false;
  }
  function confirmDialog() {
    uploadRef.value?.submit();
    uploadVisible.value = false;
  }

  async function downloadNullLocationForm() {
    store.downloadNullForm({ code: 1 }).then((res: any) => {
      let a = document.createElement("a");
      a.download = "选址空白表格模版.xlsx";
      a.style.display = "none";
      let url = URL.createObjectURL(res);
      a.href = url;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url); // 销毁
      document.body.removeChild(a);
    });
  }

  function cancelUploadVis() {
    uploadRef.value!.clearFiles();
  }

  //删除文件记录
  function deleteLogData() {
    uploadList.value = [{}];
    if (percentage.value < 100) {
      controller.abort();
    }
  }

  //重新上传
  function reUpload() {
    if (percentage.value == 100) {
      ElMessage.error("已上传成功,请勿重复上传");
    } else {
      uploadRef.value?.submit();
    }
  }

  //上传文件之前
  function beforeUpload(file: any) {
    if (file.type != "" || file.type != null || file.type != undefined) {
      const FileExt = file.name.replace(/.+\./, "").toLowerCase();
      //计算文件的大小
      const isLt5M = file.size / 1024 / 1024 < 20; //这里做文件大小限制
      //如果大于50M
      if (!isLt5M) {
        ElMessage.error("上传文件大小不能超过 20MB!");
        return false;
      }
      //如果文件类型不在允许上传的范围内
      if (fileType.value.includes(FileExt)) {
        fileList.value[0] = file;
        uploadList.value = [
          {
            fileName: fileList.value[0].name,
            fileSize: (fileList.value[0].size / 1024).toFixed(2) + "kb",
          },
        ];
        return true;
      } else {
        ElMessage.error("上传文件格式不正确!");
        return false;
      }
    }
  }

  //超出文件个数的回调
  function handleExceed(files: any) {
    uploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    uploadRef.value!.handleStart(file);
    fileList.value[0] = file;
  }

  //上传文件的事件
  function uploadFile(item: any) {
    const config = {
      signal: controller.signal,
      onUploadProgress: (progressEvent: any) => {
        percentage.value =
          Number(
            ((progressEvent.loaded / progressEvent.total) * 100).toFixed(1)
          ) - 1;
      },
    };
    let formData: FormData = new FormData();
    formData.append("File", item.file);
    formData.append("Authorization", localStorage.getItem("token"));
    store.uploadFile(formData, config).then((res: any) => {
      if (res.message === "系统异常") {
        ElMessage.error("系统异常");
        uploadRef.value!.clearFiles();
        return;
      }
      if (res.includes("导入失败")) {
        ElMessage.error("导入失败");
        uploadRef.value!.clearFiles();
        return;
      }
      if (res.includes("导入成功")) {
        percentage.value = 100;
        ElMessage({
          message: "上传成功",
          type: "success",
        });
        //更新日志
        tableVisible.value = true;
        uploadVisible.value = false;
      }
    });
  }
</script>

<style lang="less" scoped>
  .transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
  }

  :deep(.el-upload-dragger) {
    height: 30vh;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
</style>
