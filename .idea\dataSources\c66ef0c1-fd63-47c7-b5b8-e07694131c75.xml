<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="ycdb@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1"/>
    <collation id="2" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="4" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="6" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="8" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="10" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="11" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="12" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="13" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="14" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="15" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="16" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="17" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="18" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="19" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="20" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="21" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="22" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="23" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="24" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="25" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="27" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="29" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="31" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="33" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="35" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="39" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="45" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="47" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="48" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="49" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="50" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="52" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="54" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="56" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="58" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="59" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="60" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="61" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="62" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="63" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="64" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="65" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="66" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="67" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="68" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="69" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="70" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="71" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="72" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="73" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="74" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="75" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="76" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="77" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="78" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="79" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="80" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="81" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="82" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="83" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="85" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="86" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="87" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="111" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="112" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="113" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="114" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="115" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="116" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="118" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="119" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="120" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="121" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="122" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="123" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="124" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="125" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="126" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="127" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="128" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="129" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="130" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="131" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="132" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="133" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="134" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="135" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="136" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="137" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="138" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="139" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="140" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="141" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="142" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="143" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="144" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="145" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="146" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="147" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="148" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="149" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="150" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="151" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="152" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="153" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="154" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="155" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="156" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="157" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="158" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="159" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="160" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="161" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="162" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="163" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="164" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="165" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="166" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="167" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="168" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="169" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="170" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="171" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="172" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="173" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="174" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="175" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="176" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="177" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="178" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="179" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="180" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="181" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="182" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="183" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="184" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="185" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="186" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="187" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="188" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="189" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="190" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="191" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="192" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="193" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="194" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="195" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="196" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="197" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="198" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="199" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="200" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="201" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="202" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="203" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="204" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="205" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="206" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="207" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="208" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="209" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="210" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="211" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="212" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="213" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="214" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="215" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="216" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="217" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="218" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="219" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="220" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="221" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="222" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="223" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <schema id="224" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="mysql">
      <CollationName>latin1_swedish_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="nacos_config">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="228" parent="1" name="sys">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="229" parent="1" name="ycdb">
      <Current>1</Current>
      <IntrospectionTimestamp>2025-08-10.04:06:22</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-08-09.12:06:22</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="230" parent="1" name="ycwl_slave1">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="231" parent="1" name="ycwl_slave2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="232" parent="1" name="ycwl_slave3">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="233" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <user id="234" parent="1" name="mysql.session">
      <Host>localhost</Host>
    </user>
    <user id="235" parent="1" name="mysql.sys">
      <Host>localhost</Host>
    </user>
    <user id="236" parent="1" name="root"/>
    <routine id="237" parent="229" name="CalculateDeliveryDistance">
      <Definer>root@localhost</Definer>
      <RoutineKind>procedure</RoutineKind>
      <SourceTextLength>1052</SourceTextLength>
    </routine>
    <routine id="238" parent="229" name="setLocationType">
      <Definer>root@localhost</Definer>
      <RoutineKind>procedure</RoutineKind>
      <SourceTextLength>1923</SourceTextLength>
    </routine>
    <table id="239" parent="229" name="accumulation">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="240" parent="229" name="accumulation_back">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="241" parent="229" name="area">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="242" parent="229" name="car">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="243" parent="229" name="car_back">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="244" parent="229" name="centerdistance">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="245" parent="229" name="delivery_area">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="246" parent="229" name="delivery_type">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="247" parent="229" name="dist">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="248" parent="229" name="error_point">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="249" parent="229" name="error_point_back">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="250" parent="229" name="feedback">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="251" parent="229" name="feedback_file">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="252" parent="229" name="feedback_reply">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="253" parent="229" name="feedback_reply_file">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="254" parent="229" name="file_import_logs">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="255" parent="229" name="gear">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="256" parent="229" name="group">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="257" parent="229" name="group_areas">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="258" parent="229" name="operation">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="259" parent="229" name="pickup_user">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="260" parent="229" name="pickup_user_import">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="261" parent="229" name="pickup_user_parameter">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="262" parent="229" name="point_distance">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="263" parent="229" name="role">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="264" parent="229" name="role_operation">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="265" parent="229" name="route">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="266" parent="229" name="route_accumulation">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="267" parent="229" name="route_back">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="268" parent="229" name="route_copy_522">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="269" parent="229" name="route_detail">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="270" parent="229" name="route_user">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="271" parent="229" name="scheduling">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="272" parent="229" name="scheduling_user">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="273" parent="229" name="second_transit">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="274" parent="229" name="shaoguan_boundary_point">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="275" parent="229" name="site_selection">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="276" parent="229" name="site_store">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="277" parent="229" name="store">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="278" parent="229" name="store_back1">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="279" parent="229" name="store_back2">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="280" parent="229" name="store_time">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="281" parent="229" name="store_two">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="282" parent="229" name="system_parameter">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="283" parent="229" name="team">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="284" parent="229" name="transit_delivery">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="285" parent="229" name="transit_depot">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="286" parent="229" name="transit_depot_back">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="287" parent="229" name="transit_depot_car">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="288" parent="229" name="travel_time">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="289" parent="229" name="unloading_time">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="290" parent="229" name="user">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="291" parent="229" name="user_group">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="292" parent="229" name="version">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <column id="293" parent="239" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="294" parent="239" name="leader_name">
      <Comment>聚集区负责人名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="295" parent="239" name="accumulation_name">
      <Comment>聚集区名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>3</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="296" parent="239" name="leader_phone">
      <Comment>聚集区负责人联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="297" parent="239" name="longitude">
      <Comment>聚集区经度</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="298" parent="239" name="latitude">
      <Comment>聚集区纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="299" parent="239" name="area_name">
      <Comment>所属大区</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="300" parent="239" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="301" parent="239" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <column id="302" parent="239" name="is_delete">
      <Comment>是否软删除（0：否；1：是）</Comment>
      <DasType>tinyint(1) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="303" parent="239" name="accumulation_address">
      <Comment>聚集区地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="304" parent="239" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="305" parent="239" name="transit_depot_id">
      <Comment>所属中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="306" parent="239" name="area_id">
      <Comment>大区Id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="307" parent="239" name="PRIMARY">
      <ColNames>accumulation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="308" parent="239" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="309" parent="240" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="310" parent="240" name="leader_name">
      <Comment>聚集区负责人名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="311" parent="240" name="accumulation_name">
      <Comment>聚集区名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="312" parent="240" name="leader_phone">
      <Comment>聚集区负责人联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="313" parent="240" name="longitude">
      <Comment>聚集区经度</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="314" parent="240" name="latitude">
      <Comment>聚集区纬度</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="315" parent="240" name="PRIMARY">
      <ColNames>accumulation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="316" parent="240" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="317" parent="241" name="area_id">
      <AutoIncrement>11</AutoIncrement>
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="318" parent="241" name="area_name">
      <Comment>大区名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <index id="319" parent="241" name="PRIMARY">
      <ColNames>area_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="320" parent="241" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="321" parent="242" name="car_id">
      <AutoIncrement>42</AutoIncrement>
      <Comment>车辆id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="322" parent="242" name="license_plate_number">
      <Comment>车牌号(七位)</Comment>
      <DasType>char(10)|0s</DasType>
      <Position>2</Position>
      <CollationName>ujis_japanese_ci</CollationName>
    </column>
    <column id="323" parent="242" name="max_load">
      <Comment>最大载重（吨）</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="324" parent="242" name="max_distance">
      <Comment>最大行驶距离（米）</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="325" parent="242" name="integral">
      <Comment>积分</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="326" parent="242" name="status">
      <Comment>状态（0：异常；1：正常）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="327" parent="242" name="delivery_time">
      <Comment>最长可工作时长（单位：米）[ 暂定字段 ]</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="328" parent="242" name="area_id">
      <Comment>所属大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="329" parent="242" name="transit_depot_id">
      <Comment>中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="330" parent="242" name="car_driver_id">
      <Comment>驾驶人id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="331" parent="242" name="is_delete">
      <Comment>0保留1删除</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="332" parent="242" name="actual_load">
      <Comment>实际载货量</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="333" parent="242" name="actual_time">
      <Comment>实际工作时间</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="334" parent="242" name="week">
      <Comment>星期</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
      <CollationName>ujis_japanese_ci</CollationName>
    </column>
    <column id="335" parent="242" name="date">
      <Comment>日期</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="336" parent="242" name="delivery_area_id">
      <Comment>配送域id</Comment>
      <DasType>int(20)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="337" parent="242" name="route_name">
      <Comment>路线</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>17</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="338" parent="242" name="is_fact">
      <Comment>0车辆基本信息，1实情</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>18</Position>
    </column>
    <index id="339" parent="242" name="PRIMARY">
      <ColNames>car_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="340" parent="242" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="341" parent="243" name="car_id">
      <Comment>车辆id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="342" parent="243" name="license_plate_number">
      <Comment>车牌号(七位)</Comment>
      <DasType>char(10)|0s</DasType>
      <Position>2</Position>
      <CollationName>ujis_japanese_ci</CollationName>
    </column>
    <column id="343" parent="243" name="max_load">
      <Comment>最大载重（吨）</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="344" parent="243" name="max_distance">
      <Comment>最大行驶距离（米）</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="345" parent="243" name="integral">
      <Comment>积分</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="346" parent="243" name="status">
      <Comment>状态（0：异常；1：正常）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="347" parent="243" name="delivery_time">
      <Comment>最长可工作时长（单位：米）[ 暂定字段 ]</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="348" parent="243" name="area_id">
      <Comment>所属大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="349" parent="243" name="transit_depot_id">
      <Comment>中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="350" parent="243" name="PRIMARY">
      <ColNames>car_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="351" parent="243" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="352" parent="244" name="cenStore_id">
      <AutoIncrement>12</AutoIncrement>
      <Comment>商铺归属中心点id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="353" parent="244" name="cenStore_name">
      <Comment>中心点名称，用于计算商铺是城镇还是乡村</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="354" parent="244" name="lng">
      <Comment>中心点经度</Comment>
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="355" parent="244" name="lat">
      <Comment>中心点纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="356" parent="244" name="radius">
      <Comment>半径单位m</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="357" parent="244" name="PRIMARY">
      <ColNames>cenStore_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="358" parent="244" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="359" parent="245" name="delivery_area_id">
      <AutoIncrement>12</AutoIncrement>
      <Comment>配送域id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="360" parent="245" name="delivery_area_name">
      <Comment>配送域名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="361" parent="245" name="team_id">
      <Comment>所属班组id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="362" parent="245" name="area_id">
      <Comment>所属行政区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="363" parent="245" name="transit_depot_id">
      <Comment>中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="364" parent="245" name="delivery_type_id">
      <Comment>配送类型id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="365" parent="245" name="route_number">
      <Comment>路径数</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="366" parent="245" name="car_number">
      <Comment>车辆数</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="367" parent="245" name="is_delete">
      <Comment>0保留，1删除</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="368" parent="245" name="PRIMARY">
      <ColNames>delivery_area_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="369" parent="245" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="370" parent="246" name="delivery_type_id">
      <Comment>配送类型id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="371" parent="246" name="delivery_name">
      <Comment>配送类型名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <index id="372" parent="246" name="PRIMARY">
      <ColNames>delivery_type_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="373" parent="246" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="374" parent="247" name="id">
      <AutoIncrement>129886</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="375" parent="247" name="origin">
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="376" parent="247" name="destination">
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="377" parent="247" name="dist">
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="378" parent="247" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="379" parent="247" name="idx_origin_dest">
      <ColNames>origin
destination</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="380" parent="247" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="381" parent="247" name="idx_origin_dest">
      <UnderlyingIndexName>idx_origin_dest</UnderlyingIndexName>
    </key>
    <column id="382" parent="248" name="error_point_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>错误点id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="383" parent="248" name="current_store_longitude">
      <Comment>当前商铺经度</Comment>
      <DasType>double|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="384" parent="248" name="current_store_latitude">
      <Comment>当前商铺纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="385" parent="248" name="pairing_store_longitude">
      <Comment>配对商铺经度(聚集区、打卡点的经纬度)</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="386" parent="248" name="pairing_store_latitude">
      <Comment>配对商铺纬度（聚集区打卡点的经纬度）</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="387" parent="248" name="is_delete">
      <Comment>是否软删除（0：否；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="388" parent="248" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
    </column>
    <column id="389" parent="248" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="390" parent="248" name="PRIMARY">
      <ColNames>error_point_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="391" parent="248" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="392" parent="249" name="error_point_id">
      <Comment>错误点id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="393" parent="249" name="current_store_longitude">
      <Comment>当前商铺经度</Comment>
      <DasType>double|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="394" parent="249" name="current_store_latitude">
      <Comment>当前商铺纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="395" parent="249" name="pairing_store_longitude">
      <Comment>配对商铺经度</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="396" parent="249" name="pairing_store_latitude">
      <Comment>配对商铺纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="397" parent="249" name="is_delete">
      <Comment>是否软删除（0：否；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="398" parent="249" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="399" parent="249" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <column id="400" parent="249" name="is_exist">
      <Comment>是否存在这种情况（0：否；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="401" parent="249" name="PRIMARY">
      <ColNames>error_point_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="402" parent="249" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="403" parent="250" name="feedback_id">
      <AutoIncrement>1927915818520674307</AutoIncrement>
      <Comment>反馈id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="404" parent="250" name="customer_code">
      <Comment>客户编码</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="405" parent="250" name="route_id">
      <Comment>送货路径id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="406" parent="250" name="route_name">
      <Comment>送货路径</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="407" parent="250" name="order_date">
      <Comment>订单日期</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="408" parent="250" name="delivery_work_number">
      <Comment>送货员工号</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="409" parent="250" name="delivery_name">
      <Comment>送货员</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="410" parent="250" name="customer_manager_name">
      <Comment>客户专员</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="411" parent="250" name="manager_work_number">
      <Comment>客户专员工号</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="412" parent="250" name="create_by">
      <Comment>创建者id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="413" parent="250" name="feedback_information">
      <Comment>反馈异常信息</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="414" parent="250" name="feedback_type">
      <Comment>反馈类型（1：物流反馈；2：营销反馈）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="415" parent="250" name="feedback_status">
      <Comment>是否处理（0：未处理；1：处理中；2：已处理；3：无需处理）</Comment>
      <DasType>int(1)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="416" parent="250" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="417" parent="250" name="complete_time">
      <Comment>完成时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="418" parent="250" name="area_name">
      <Comment>大区名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="419" parent="250" name="update_time">
      <Comment>最近更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="420" parent="250" name="update_by">
      <Comment>最近更新人id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>18</Position>
    </column>
    <index id="421" parent="250" name="PRIMARY">
      <ColNames>feedback_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="422" parent="250" name="customer_code">
      <ColNames>customer_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="423" parent="250" name="create_by">
      <ColNames>create_by</ColNames>
      <Type>btree</Type>
    </index>
    <key id="424" parent="250" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="425" parent="251" name="feedback_file_id">
      <AutoIncrement>1927915818558423042</AutoIncrement>
      <Comment>异常反馈信息文件id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="426" parent="251" name="feedback_file_path">
      <Comment>异常反馈信息文件路径</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="427" parent="251" name="feedback_file_real_path">
      <Comment>异常反馈信息文件真实路径</Comment>
      <DasType>varchar(125)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="428" parent="251" name="feedback_id">
      <Comment>异常反馈信息id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="429" parent="251" name="PRIMARY">
      <ColNames>feedback_file_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="430" parent="251" name="feedback_file_ibfk_1">
      <ColNames>feedback_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="431" parent="251" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="432" parent="252" name="reply_id">
      <AutoIncrement>1900177217546694659</AutoIncrement>
      <Comment>回复id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="433" parent="252" name="reply_content">
      <Comment>回复内容</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="434" parent="252" name="create_by">
      <Comment>创建者id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="435" parent="252" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="436" parent="252" name="reply_type">
      <Comment>回复类型（1：送货部；2：营销部）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="437" parent="252" name="feedback_id">
      <Comment>反馈信息id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="438" parent="252" name="PRIMARY">
      <ColNames>reply_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="439" parent="252" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="440" parent="253" name="reply_file_id">
      <AutoIncrement>1900177217592832003</AutoIncrement>
      <Comment>回复信息文件id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="441" parent="253" name="reply_file_path">
      <Comment>回复信息文件路径</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="442" parent="253" name="reply_file_real_path">
      <Comment>回复信息文件真实路径</Comment>
      <DasType>varchar(125)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="443" parent="253" name="reply_id">
      <Comment>回复id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="444" parent="253" name="PRIMARY">
      <ColNames>reply_file_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="445" parent="253" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="446" parent="254" name="file_id">
      <AutoIncrement>222</AutoIncrement>
      <Comment>文件id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="447" parent="254" name="file_name">
      <Comment>文件名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="448" parent="254" name="file_size">
      <Comment>文件大小单位kb</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="449" parent="254" name="import_time">
      <Comment>导入时间</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="450" parent="254" name="user_name">
      <Comment>用户</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="451" parent="254" name="status">
      <Comment>导入状态</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="452" parent="254" name="store_or_car">
      <Comment>0表示该表格是商铺表，1表示该表格是车辆实情表</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="453" parent="254" name="PRIMARY">
      <ColNames>file_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="454" parent="254" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="455" parent="255" name="gear_id">
      <Comment>档位id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="456" parent="255" name="gear_name">
      <Comment>具体档位</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="457" parent="255" name="cargo_weight">
      <Comment>档位对应的载货量</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="458" parent="255" name="PRIMARY">
      <ColNames>gear_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="459" parent="255" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="460" parent="256" name="group_id">
      <Comment>班组id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="461" parent="256" name="group_name">
      <Comment>班组名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="462" parent="256" name="area_id">
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="463" parent="256" name="PRIMARY">
      <ColNames>group_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="464" parent="256" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="465" parent="257" name="group_id">
      <Comment>班组Id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="466" parent="257" name="area_id">
      <Comment>大区Id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="467" parent="257" name="PRIMARY">
      <ColNames>area_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="468" parent="257" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="469" parent="258" name="operation_id">
      <Comment>权限点id</Comment>
      <DasType>bigint(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="470" parent="258" name="operation_name">
      <Comment>权限点名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="471" parent="258" name="operation_state">
      <Comment>权限点描述</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="472" parent="258" name="PRIMARY">
      <ColNames>operation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="473" parent="258" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <trigger id="474" parent="258" name="after_insert_operation">
      <Definer>root@localhost</Definer>
      <Events>I</Events>
      <SourceTextLength>1045</SourceTextLength>
      <Turn>after-row</Turn>
    </trigger>
    <trigger id="475" parent="258" name="delete_operation">
      <Definer>root@localhost</Definer>
      <Events>D</Events>
      <SourceTextLength>91</SourceTextLength>
      <Turn>after-row</Turn>
    </trigger>
    <column id="476" parent="259" name="id">
      <AutoIncrement>2471550</AutoIncrement>
      <Comment>取货户id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="477" parent="259" name="customer_code">
      <Comment>客户编码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="478" parent="259" name="contact_name">
      <Comment>客户名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="479" parent="259" name="store_name">
      <Comment>商店名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="480" parent="259" name="customer_manager_name">
      <Comment>负责人</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="481" parent="259" name="contact_phone">
      <Comment>订货电话</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="482" parent="259" name="store_address">
      <Comment>商铺地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="483" parent="259" name="road_grade">
      <Comment>道路等级0城区1乡镇</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="484" parent="259" name="gear">
      <Comment>档位</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="485" parent="259" name="delivery_distance">
      <Comment>配送距离</Comment>
      <DasType>double|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="486" parent="259" name="pickup_containers">
      <Comment>取货柜地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="487" parent="259" name="type">
      <Comment>取货柜类型</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="488" parent="259" name="weights">
      <Comment>权值</Comment>
      <DasType>double|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="489" parent="259" name="locks">
      <Comment>1加锁0不加锁</Comment>
      <DasType>int(1) unsigned zerofill|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="490" parent="259" name="color">
      <Comment>1普通商户未分配，2普通商户分配，3定点取货户未分配，4定点取货户已分配</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="491" parent="259" name="longitude">
      <Comment>经度</Comment>
      <DasType>double|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="492" parent="259" name="latitude">
      <Comment>纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="493" parent="259" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>18</Position>
    </column>
    <index id="494" parent="259" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="495" parent="259" name="customer_code">
      <ColNames>customer_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="496" parent="259" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="497" parent="259" name="customer_code">
      <UnderlyingIndexName>customer_code</UnderlyingIndexName>
    </key>
    <column id="498" parent="260" name="id">
      <AutoIncrement>1578536</AutoIncrement>
      <Comment>表格记录</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="499" parent="260" name="customer_code">
      <Comment>客户编码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="500" parent="260" name="contact_name">
      <Comment>客户名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="501" parent="260" name="store_name">
      <Comment>商店名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="502" parent="260" name="customer_manager_name">
      <Comment>负责人</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="503" parent="260" name="store_address">
      <Comment>商铺地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="504" parent="260" name="pickup_type">
      <Comment>取货柜类型</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="505" parent="260" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="506" parent="260" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="507" parent="261" name="id">
      <Comment>主键id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="508" parent="261" name="gear">
      <Comment>客户档位</Comment>
      <DasType>double|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="509" parent="261" name="road_grade">
      <Comment>道路等级</Comment>
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="510" parent="261" name="avg_distance">
      <Comment>平均送货距离</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="511" parent="261" name="level_param">
      <Comment>定级参数</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="512" parent="261" name="exclude_gear">
      <Comment>大于等于这个档位的取货户不参与计算</Comment>
      <DasType>int(3)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="513" parent="261" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="514" parent="261" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="515" parent="262" name="point_distance_id">
      <Comment>路段id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="516" parent="262" name="distance">
      <Comment>路段距离</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="517" parent="262" name="polyline">
      <Comment>路段坐标点串</Comment>
      <DasType>longtext|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="518" parent="262" name="origin">
      <Comment>路段起始点</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="519" parent="262" name="destination">
      <Comment>路路段终点</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="520" parent="262" name="is_delete">
      <Comment>是否软删除（0：否；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="521" parent="262" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="522" parent="262" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <column id="523" parent="262" name="transit_depot_id">
      <Comment>所属中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="524" parent="262" name="type">
      <Comment>路段类型（ordinary：普通；bridge：桥）</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="525" parent="262" name="PRIMARY">
      <ColNames>point_distance_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="526" parent="262" name="uniq_origin_destination">
      <ColNames>origin
destination</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="527" parent="262" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="528" parent="262" name="uniq_origin_destination">
      <UnderlyingIndexName>uniq_origin_destination</UnderlyingIndexName>
    </key>
    <column id="529" parent="263" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="530" parent="263" name="role_name">
      <Comment>角色名字</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="531" parent="263" name="role_state">
      <Comment>角色描述</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="532" parent="263" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="533" parent="263" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <trigger id="534" parent="263" name="after_role_insert">
      <Definer>root@localhost</Definer>
      <Events>I</Events>
      <SourceTextLength>128</SourceTextLength>
      <Turn>after-row</Turn>
    </trigger>
    <trigger id="535" parent="263" name="before_role_delete">
      <Definer>root@localhost</Definer>
      <Events>D</Events>
      <SourceTextLength>69</SourceTextLength>
      <Turn>before-row</Turn>
    </trigger>
    <column id="536" parent="264" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="537" parent="264" name="operation_id">
      <Comment>权限点id</Comment>
      <DasType>bigint(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="538" parent="264" name="status">
      <Comment>角色权限状态：默认0为未开启权限点   1为开启权限点</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="539" parent="264" name="PRIMARY">
      <ColNames>role_id
operation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="540" parent="264" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="541" parent="265" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="542" parent="265" name="route_name">
      <Comment>路线名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="543" parent="265" name="distance">
      <Comment>路线距离</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="544" parent="265" name="transit_depot_id">
      <Comment>中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="545" parent="265" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="546" parent="265" name="polyline">
      <Comment>路线坐标点串</Comment>
      <DasType>longtext|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="547" parent="265" name="is_delete">
      <Comment>是否软删除（0：否；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="548" parent="265" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="549" parent="265" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <column id="550" parent="265" name="cargo_weight">
      <Comment>载货量</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="551" parent="265" name="version_number">
      <Comment>版本号</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="552" parent="265" name="convex">
      <Comment>凸包坐标点串</Comment>
      <DasType>longtext|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="553" parent="265" name="work_time">
      <Comment>工作时长</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="554" parent="265" name="PRIMARY">
      <ColNames>route_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="555" parent="265" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="556" parent="266" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="557" parent="266" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <foreign-key id="558" parent="266" name="fk_route_accumulation_route">
      <ColNames>route_id</ColNames>
      <RefColNames>route_id</RefColNames>
      <RefTableName>route_back</RefTableName>
    </foreign-key>
    <foreign-key id="559" parent="266" name="fk_route_accumulation_accumulation">
      <ColNames>accumulation_id</ColNames>
      <RefColNames>accumulation_id</RefColNames>
      <RefTableName>accumulation_back</RefTableName>
    </foreign-key>
    <index id="560" parent="266" name="PRIMARY">
      <ColNames>route_id
accumulation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="561" parent="266" name="fk_route_accumulation_accumulation">
      <ColNames>accumulation_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="562" parent="266" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="563" parent="267" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="564" parent="267" name="route_name">
      <Comment>路线名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="565" parent="267" name="distance">
      <Comment>路线距离</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="566" parent="267" name="transit_depot_id">
      <Comment>中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="567" parent="267" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="568" parent="267" name="PRIMARY">
      <ColNames>route_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="569" parent="267" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="570" parent="268" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="571" parent="268" name="route_name">
      <Comment>路线名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="572" parent="268" name="distance">
      <Comment>路线距离</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="573" parent="268" name="transit_depot_id">
      <Comment>中转站id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="574" parent="268" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="575" parent="268" name="polyline">
      <Comment>路线坐标点串</Comment>
      <DasType>longtext|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="576" parent="268" name="is_delete">
      <Comment>是否软删除（0：否；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="577" parent="268" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="578" parent="268" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <column id="579" parent="268" name="cargo_weight">
      <Comment>载货量</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="580" parent="268" name="version_number">
      <Comment>版本号</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="581" parent="268" name="convex">
      <Comment>凸包坐标点串</Comment>
      <DasType>longtext|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="582" parent="268" name="work_time">
      <Comment>工作时长</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="583" parent="268" name="PRIMARY">
      <ColNames>route_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="584" parent="268" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="585" parent="269" name="id">
      <AutoIncrement>4275</AutoIncrement>
      <Comment>id</Comment>
      <DasType>bigint(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="586" parent="269" name="accumulation_count">
      <Comment>打卡点/聚集区时长</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="587" parent="269" name="city_count">
      <Comment>城区商铺个数</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="588" parent="269" name="country_count">
      <Comment>乡镇商铺个数</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="589" parent="269" name="loading_time">
      <Comment>装车时长</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="590" parent="269" name="transit_time">
      <Comment>途中时长</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="591" parent="269" name="delivery_time">
      <Comment>卸货配送时长</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="592" parent="269" name="total_time">
      <Comment>总时长</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="593" parent="269" name="route_id">
      <Comment>路线Id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="594" parent="269" name="freeewat_dist">
      <Comment>高速公路行驶里程m</Comment>
      <DasType>double|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="595" parent="269" name="uraban_roads_dist">
      <Comment>城区公路行驶里程m</Comment>
      <DasType>double|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="596" parent="269" name="township_roads_dist">
      <Comment>乡镇公路行驶里程m</Comment>
      <DasType>double|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="597" parent="269" name="second_transit_time">
      <Comment>二次中转站时长分钟</Comment>
      <DasType>double|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="598" parent="269" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="599" parent="269" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="600" parent="270" name="user_id">
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="601" parent="270" name="route_id">
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <foreign-key id="602" parent="270" name="fk_route_user_user">
      <ColNames>user_id</ColNames>
      <RefColNames>user_id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="603" parent="270" name="fk_route_user_route">
      <ColNames>route_id</ColNames>
      <RefColNames>route_id</RefColNames>
      <RefTableName>route_back</RefTableName>
    </foreign-key>
    <index id="604" parent="270" name="PRIMARY">
      <ColNames>route_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="605" parent="270" name="fk_route_user_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="606" parent="270" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="607" parent="271" name="scheduling_id">
      <Comment>排班表id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="608" parent="271" name="day">
      <Comment>星期几（1,2,3,4,5）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="609" parent="271" name="car_id">
      <Comment>车辆id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="610" parent="271" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <foreign-key id="611" parent="271" name="scheduling_ibfk_1">
      <ColNames>car_id</ColNames>
      <RefColNames>car_id</RefColNames>
      <RefTableName>car_back</RefTableName>
    </foreign-key>
    <foreign-key id="612" parent="271" name="scheduling_ibfk_2">
      <ColNames>route_id</ColNames>
      <RefColNames>route_id</RefColNames>
      <RefTableName>route_back</RefTableName>
    </foreign-key>
    <index id="613" parent="271" name="PRIMARY">
      <ColNames>scheduling_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="614" parent="271" name="scheduling_id">
      <ColNames>scheduling_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="615" parent="271" name="car_id">
      <ColNames>car_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="616" parent="271" name="route_id">
      <ColNames>route_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="617" parent="271" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="618" parent="272" name="scheduling_id">
      <Comment>排班表id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="619" parent="272" name="user_id">
      <Comment>配送员id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <foreign-key id="620" parent="272" name="scheduling_user_ibfk_1">
      <ColNames>scheduling_id</ColNames>
      <RefColNames>scheduling_id</RefColNames>
      <RefTableName>scheduling</RefTableName>
    </foreign-key>
    <foreign-key id="621" parent="272" name="scheduling_user_ibfk_2">
      <ColNames>user_id</ColNames>
      <RefColNames>user_id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="622" parent="272" name="PRIMARY">
      <ColNames>scheduling_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="623" parent="272" name="user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="624" parent="272" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="625" parent="273" name="id">
      <AutoIncrement>78</AutoIncrement>
      <Comment>二次中转单条记录id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="626" parent="273" name="second_transit_id">
      <Comment>二次中转站id</Comment>
      <DasType>bigint(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="627" parent="273" name="transit_time">
      <Comment>二次中转时长</Comment>
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="628" parent="273" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="629" parent="273" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="630" parent="274" name="jingwei">
      <Comment>韶关市边缘经纬度</Comment>
      <DasType>longtext|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="631" parent="275" name="id">
      <AutoIncrement>63</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="632" parent="275" name="pickup_name">
      <Comment>取货地名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="633" parent="275" name="pickup_address">
      <Comment>详细地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="634" parent="275" name="longitude">
      <Comment>经度</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="635" parent="275" name="latitude">
      <Comment>纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="636" parent="275" name="type">
      <Comment>取货地类型</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="637" parent="275" name="status">
      <Comment>状态(1禁用2启用未分配商户3启用已分配商户）</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="638" parent="275" name="city">
      <Comment>市</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="639" parent="275" name="district">
      <Comment>区/县/县级市</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="640" parent="275" name="town">
      <Comment>镇</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="641" parent="275" name="village">
      <Comment>村/村委会</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="642" parent="275" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="643" parent="275" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="644" parent="276" name="id">
      <AutoIncrement>99738</AutoIncrement>
      <Comment>选址and商户表id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="645" parent="276" name="site_selection_id">
      <Comment>选址id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="646" parent="276" name="store_id">
      <Comment>商户id</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="647" parent="276" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="648" parent="276" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="649" parent="277" name="store_id">
      <AutoIncrement>11438</AutoIncrement>
      <Comment>店铺id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="650" parent="277" name="customer_code">
      <Comment>客户编码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="651" parent="277" name="store_name">
      <Comment>店铺名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="652" parent="277" name="store_address">
      <Comment>店铺经营地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="653" parent="277" name="longitude">
      <Comment>店铺经度</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="654" parent="277" name="latitude">
      <Comment>店铺纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="655" parent="277" name="type">
      <Comment>商圈类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="656" parent="277" name="order_cycle">
      <Comment>订货周期</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="657" parent="277" name="district">
      <Comment>店铺所属行政区</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="658" parent="277" name="area_name">
      <Comment>店铺所属大区</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="659" parent="277" name="contact_name">
      <Comment>店铺联系人名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="660" parent="277" name="contact_phone">
      <Comment>店铺联系人电话号码</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="661" parent="277" name="status">
      <Comment>状态（0：异常；1：正常）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>13</Position>
      <CollationName>latin1_swedish_ci</CollationName>
    </column>
    <column id="662" parent="277" name="customer_manager_id">
      <Comment>客户专员id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="663" parent="277" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="664" parent="277" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="665" parent="277" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="666" parent="277" name="route_name">
      <Comment>路线名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="667" parent="277" name="customer_manager_name">
      <Comment>客户专员名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="668" parent="277" name="is_delete">
      <Comment>是否软删除（0：不是；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="669" parent="277" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>21</Position>
    </column>
    <column id="670" parent="277" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>22</Position>
    </column>
    <column id="671" parent="277" name="gear">
      <Comment>客户档位</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="672" parent="277" name="create_by">
      <Comment>创建人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="673" parent="277" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>25</Position>
    </column>
    <column id="674" parent="277" name="group_id">
      <Comment>班组Id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>26</Position>
    </column>
    <column id="675" parent="277" name="location_type">
      <Comment>0：城区；1：乡镇</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>27</Position>
    </column>
    <column id="676" parent="277" name="delivery_area">
      <DasType>varchar(255)|0s</DasType>
      <Position>28</Position>
    </column>
    <column id="677" parent="277" name="is_special">
      <Comment>是否是特殊点0不是1是</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>29</Position>
    </column>
    <column id="678" parent="277" name="remark">
      <Comment>特殊点备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>30</Position>
    </column>
    <column id="679" parent="277" name="special_type">
      <Comment>特殊点类型</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>31</Position>
    </column>
    <column id="680" parent="277" name="head">
      <DasType>varchar(255)|0s</DasType>
      <Position>32</Position>
    </column>
    <index id="681" parent="277" name="PRIMARY">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="682" parent="277" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="683" parent="278" name="store_id">
      <Comment>店铺id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="684" parent="278" name="customer_code">
      <Comment>客户编码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="685" parent="278" name="store_name">
      <Comment>店铺名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>3</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="686" parent="278" name="store_address">
      <Comment>店铺经营地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="687" parent="278" name="longitude">
      <Comment>店铺经度</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="688" parent="278" name="latitude">
      <Comment>店铺纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="689" parent="278" name="type">
      <Comment>商圈类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="690" parent="278" name="order_cycle">
      <Comment>订货周期</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="691" parent="278" name="district">
      <Comment>店铺所属行政区</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="692" parent="278" name="area_name">
      <Comment>店铺所属大区</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>10</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="693" parent="278" name="contact_name">
      <Comment>店铺联系人名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="694" parent="278" name="contact_phone">
      <Comment>店铺联系人电话号码</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="695" parent="278" name="status">
      <Comment>状态（0：异常；1：正常）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="696" parent="278" name="customer_manager_id">
      <Comment>客户专员id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="697" parent="278" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="698" parent="278" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="699" parent="278" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="700" parent="278" name="route_name">
      <Comment>路线名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>18</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="701" parent="278" name="customer_manager_name">
      <Comment>客户专员名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>19</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="702" parent="278" name="is_delete">
      <Comment>是否软删除（0：不是；1：是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>20</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="703" parent="278" name="creat_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>21</Position>
    </column>
    <column id="704" parent="278" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>22</Position>
    </column>
    <foreign-key id="705" parent="278" name="store_back1_ibfk_1">
      <ColNames>customer_manager_id</ColNames>
      <RefColNames>user_id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="706" parent="278" name="PRIMARY">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="707" parent="278" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="708" parent="278" name="customer_code">
      <ColNames>customer_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="709" parent="278" name="customer_manager_id">
      <ColNames>customer_manager_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="710" parent="278" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="711" parent="279" name="store_id">
      <Comment>店铺id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="712" parent="279" name="customer_code">
      <Comment>客户编码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="713" parent="279" name="store_name">
      <Comment>店铺名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="714" parent="279" name="store_address">
      <Comment>店铺经营地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="715" parent="279" name="longitude">
      <Comment>店铺经度</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="716" parent="279" name="latitude">
      <Comment>店铺纬度</Comment>
      <DasType>double|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="717" parent="279" name="type">
      <Comment>商圈类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="718" parent="279" name="order_cycle">
      <Comment>订货周期</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="719" parent="279" name="district">
      <Comment>店铺所属行政区</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="720" parent="279" name="area_name">
      <Comment>店铺所属大区</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="721" parent="279" name="contact_name">
      <Comment>店铺联系人名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="722" parent="279" name="contact_phone">
      <Comment>店铺联系人电话号码</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="723" parent="279" name="status">
      <Comment>状态（0：异常；1：正常）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>13</Position>
      <CollationName>latin1_swedish_ci</CollationName>
    </column>
    <column id="724" parent="279" name="customer_manager_id">
      <Comment>客户专员id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="725" parent="279" name="accumulation_id">
      <Comment>聚集区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="726" parent="279" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="727" parent="279" name="route_id">
      <Comment>路线id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="728" parent="279" name="route_name">
      <Comment>路线名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="729" parent="279" name="customer_manager_name">
      <Comment>客户专员名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="730" parent="279" name="is_delete">
      <Comment>是否软删除（0：不是；1：是）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="731" parent="279" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>21</Position>
    </column>
    <column id="732" parent="279" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>22</Position>
    </column>
    <index id="733" parent="279" name="PRIMARY">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="734" parent="279" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="735" parent="280" name="id">
      <AutoIncrement>1671</AutoIncrement>
      <DasType>bigint(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="736" parent="280" name="longitude">
      <Comment> 经度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="737" parent="280" name="latitude">
      <Comment>纬度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="738" parent="280" name="time">
      <Comment>时间</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="739" parent="280" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="740" parent="280" name="ind_longitude_latitude">
      <ColNames>longitude
latitude</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="741" parent="280" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="742" parent="280" name="ind_longitude_latitude">
      <UnderlyingIndexName>ind_longitude_latitude</UnderlyingIndexName>
    </key>
    <column id="743" parent="281" name="store_id">
      <Comment>商铺id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="744" parent="281" name="head">
      <Comment>负责人</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="745" parent="281" name="spare_phone">
      <Comment>备用收货电话</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="746" parent="281" name="receiving_phone">
      <Comment>收货电话</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="747" parent="281" name="resale_cycle">
      <Comment>返销周期</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="748" parent="281" name="PRIMARY">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="749" parent="281" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="750" parent="282" name="id">
      <Comment>系统参数记录id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="751" parent="282" name="accumulation_intensity">
      <Comment>聚集区密集度系数</Comment>
      <DasType>double|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="752" parent="282" name="shore_unload_city_time">
      <Comment>商铺平均卸货时长(小时)城区</Comment>
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="753" parent="282" name="shore_unload_township_time">
      <Comment>商铺平均卸货时长(小时)乡村</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="754" parent="282" name="freeway">
      <Comment>车辆时速(千米每时)-高速公路</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="755" parent="282" name="urban_roads">
      <Comment>车辆时速(千米每时)-城区公路</Comment>
      <DasType>double|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="756" parent="282" name="township_roads">
      <Comment>车辆时速(千米每时)-乡镇公路</Comment>
      <DasType>double|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="757" parent="282" name="loading_time">
      <Comment>装车时长</Comment>
      <DasType>double|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="758" parent="282" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="759" parent="282" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="760" parent="283" name="team_id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>班组id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="761" parent="283" name="team_name">
      <Comment>班组名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="762" parent="283" name="delivery_area_name">
      <Comment>配送域名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="763" parent="283" name="transit_depot_name">
      <Comment>中转站名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="764" parent="283" name="car_sum">
      <Comment>车辆总数</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="765" parent="283" name="route_sum">
      <Comment>路线总数</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="766" parent="283" name="is_delete">
      <Comment>0否，1删除</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="767" parent="283" name="PRIMARY">
      <ColNames>team_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="768" parent="283" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="769" parent="284" name="transit_delivery_id">
      <AutoIncrement>20</AutoIncrement>
      <Comment>记录id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="770" parent="284" name="transit_depot_id">
      <Comment>对接点id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="771" parent="284" name="delivery_area_id">
      <Comment>配送域id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="772" parent="284" name="PRIMARY">
      <ColNames>transit_delivery_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="773" parent="284" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="774" parent="285" name="transit_depot_id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>中转场id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="775" parent="285" name="transit_depot_name">
      <Comment>中转场名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="776" parent="285" name="status">
      <Comment>中转场启用状态(0：禁用；1：启用)</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="777" parent="285" name="longitude">
      <Comment>中转场经度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="778" parent="285" name="latitude">
      <Comment>中转场纬度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="779" parent="285" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="780" parent="285" name="group_id">
      <Comment>班组id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="781" parent="285" name="is_delete">
      <Comment>是否进行软删除，1删除</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="782" parent="285" name="PRIMARY">
      <ColNames>transit_depot_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="783" parent="285" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="784" parent="286" name="transit_depot_id">
      <Comment>中转场id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="785" parent="286" name="transit_depot_name">
      <Comment>中转场名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="786" parent="286" name="status">
      <Comment>中转场启用状态(0：禁用；1：启用)</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="787" parent="286" name="longitude">
      <Comment>中转场经度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="788" parent="286" name="latitude">
      <Comment>中转场纬度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="789" parent="286" name="area_id">
      <Comment>大区id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="790" parent="286" name="PRIMARY">
      <ColNames>transit_depot_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="791" parent="286" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="792" parent="287" name="transit_depot_id">
      <Comment>中转场id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="793" parent="287" name="car_id">
      <Comment>车辆id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <foreign-key id="794" parent="287" name="transit_depot_car_ibfk_1">
      <ColNames>transit_depot_id</ColNames>
      <RefColNames>transit_depot_id</RefColNames>
      <RefTableName>transit_depot_back</RefTableName>
    </foreign-key>
    <foreign-key id="795" parent="287" name="transit_depot_car_ibfk_2">
      <ColNames>car_id</ColNames>
      <RefColNames>car_id</RefColNames>
      <RefTableName>car_back</RefTableName>
    </foreign-key>
    <index id="796" parent="287" name="PRIMARY">
      <ColNames>transit_depot_id
car_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="797" parent="287" name="car_id">
      <ColNames>car_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="798" parent="287" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="799" parent="288" name="longitude_start">
      <Comment>起始打卡点经度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="800" parent="288" name="latitude_start">
      <Comment>起点打卡点纬度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="801" parent="288" name="longitude_end">
      <Comment>终点打卡点经度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="802" parent="288" name="latitude_end">
      <Comment>终点打卡点纬度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="803" parent="288" name="travel_time">
      <Comment>行驶时长</Comment>
      <DasType>double|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="804" parent="289" name="acc_id">
      <Comment>打卡点id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="805" parent="289" name="acclongitude">
      <Comment>打卡点经度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="806" parent="289" name="acclatitude">
      <Comment>打卡点纬度</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="807" parent="289" name="unloading_time">
      <Comment>卸货时长</Comment>
      <DasType>double|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="808" parent="289" name="PRIMARY">
      <ColNames>acc_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="809" parent="289" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="810" parent="290" name="user_id">
      <Comment>主键</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="811" parent="290" name="login_name">
      <Comment>登录账号</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="812" parent="290" name="user_name">
      <Comment>用户名</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="813" parent="290" name="work_number">
      <Comment>工号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="814" parent="290" name="sex">
      <Comment>性别</Comment>
      <DasType>varchar(4)|0s</DasType>
      <Position>5</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="815" parent="290" name="position">
      <Comment>职位</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>6</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="816" parent="290" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="817" parent="290" name="department">
      <Comment>部门</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="818" parent="290" name="phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="819" parent="290" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="820" parent="290" name="status">
      <Comment>状态（0：禁用；1：启用）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="821" parent="290" name="avatar_path">
      <Comment>头像路径</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="822" parent="290" name="create_by">
      <Comment>创建者id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="823" parent="290" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="824" parent="290" name="update_by">
      <Comment>最近一次修改者id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="825" parent="290" name="update_time">
      <Comment>最近一次修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="826" parent="290" name="sign_time">
      <Comment>入职时间</Comment>
      <DasType>date|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="827" parent="290" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="828" parent="290" name="rank">
      <Comment>职级</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>19</Position>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <foreign-key id="829" parent="290" name="fk_user_role_role">
      <ColNames>role_id</ColNames>
      <RefColNames>role_id</RefColNames>
      <RefTableName>role</RefTableName>
    </foreign-key>
    <index id="830" parent="290" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="831" parent="290" name="fk_user_role_role">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="832" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="833" parent="291" name="group_id">
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="834" parent="291" name="user_id">
      <DasType>bigint(20)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="835" parent="291" name="is_leader">
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="836" parent="291" name="PRIMARY">
      <ColNames>group_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="837" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="838" parent="292" name="version_id">
      <Comment>版本id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="839" parent="292" name="version_name">
      <Comment>版本名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="840" parent="292" name="version_db">
      <Comment>实际对应版本</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="841" parent="292" name="version_info">
      <Comment>版本备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="842" parent="292" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="843" parent="292" name="is_show">
      <Comment>1显示在列表中，启用，0不显示在列表中，被删除</Comment>
      <DasType>int(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="844" parent="292" name="PRIMARY">
      <ColNames>version_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="845" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <trigger id="846" parent="292" name="update_updateTime_before_update">
      <Definer>root@localhost</Definer>
      <Events>U</Events>
      <SourceTextLength>42</SourceTextLength>
      <Turn>before-row</Turn>
    </trigger>
  </database-model>
</dataSource>