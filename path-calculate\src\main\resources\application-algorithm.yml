# 算法配置文件
# 用于配置路径规划算法的各种参数

ycwl:
  algorithm:
    # 是否启用新算法
    enable-new-algorithm: true
    
    # 算法超时时间（毫秒）
    timeout-ms: 300000
    
    # 是否启用算法降级（新算法失败时使用旧算法）
    enable-fallback: true
    
    # 最大重试次数
    max-retry-count: 3
    
    # 算法执行模式：SYNC（同步）或 ASYNC（异步）
    execution-mode: SYNC
    
    # 数据验证开关
    enable-data-validation: true
    
    # 结果缓存开关
    enable-result-cache: false
    
    # 缓存过期时间（秒）
    cache-expire-seconds: 3600
    
    # 日志级别：DEBUG, INFO, WARN, ERROR
    log-level: INFO
    
    # 算法参数配置
    params:
      # 最大路线数量
      max-route-count: 50
      
      # 最小聚集区数量（少于此数量不执行算法）
      min-accumulation-count: 2
      
      # 最大工作时间（小时）
      max-work-time-hours: 8.0
      
      # 平均行驶速度（km/h）
      average-speed: 30.0
      
      # 配送时间系数
      delivery-time-factor: 1.0
      
      # 是否考虑交通拥堵
      consider-traffic: true
      
      # 是否优化路线顺序
      optimize-route-order: true
      
      # 路线平衡系数
      route-balance-factor: 0.8

# 日志配置
logging:
  level:
    com.ict.ycwl.pathcalculate.algorithm: DEBUG
    com.ict.ycwl.pathcalculate.service.adapter: DEBUG
    com.ict.ycwl.pathcalculate.controller.NewAlgorithmController: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 算法性能监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      simple:
        enabled: true
