2025-08-20 00:06:41.518 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-20 00:06:41.621 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-20 00:06:41.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.647 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-20 00:06:41.647 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-20 00:06:41.650 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-20 00:06:41.651 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-20 00:06:41.651 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-20 00:06:41.652 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-20 00:06:41.785 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.786 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-20 00:06:41.788 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.788 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.800 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-20 00:06:41.811 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-20 00:06:41.812 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-20 00:06:41.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.846 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.846 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.846 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.847 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.847 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.847 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.847 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.847 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.851 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.852 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.852 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.853 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.864 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.870 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.870 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.870 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.871 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.871 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.871 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.875 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:41.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.875 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.876 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.879 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.879 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.879 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.881 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:41.881 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:41.881 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:41.884 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.885 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.885 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.885 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.885 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.889 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.889 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.889 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.889 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.889 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.916 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.919 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.924 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.927 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.927 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.927 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.928 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.932 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:41.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.932 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.934 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.934 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.938 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.938 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.938 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.938 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:41.938 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:41.938 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:41.940 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.940 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.940 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.940 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.940 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.944 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.945 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.948 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.949 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.949 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.949 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.949 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.953 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.953 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.958 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:41.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.962 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.965 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.965 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.965 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.965 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.970 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:41.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:41.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:41.971 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:41.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:41.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:41.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:41.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:41.974 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:41.974 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:41.974 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:41.975 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:41.975 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:41.975 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:41.978 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-20 00:06:42.305 ERROR c.i.y.p.algorithm.core.ORToolsBootstrap - ❌ 分步加载失败: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
2025-08-20 00:06:42.305 WARN  c.i.y.p.algorithm.core.ORToolsBootstrap - 常规初始化失败，尝试高级恢复...
2025-08-20 00:06:42.305 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始高级JNI恢复尝试...
2025-08-20 00:06:42.309 INFO  c.i.y.p.algorithm.core.AdvancedJNIRecovery - 开始高级JNI恢复...
2025-08-20 00:06:42.521 WARN  c.i.y.p.algorithm.core.AdvancedJNIRecovery - ❌ 所有恢复策略都失败了
2025-08-20 00:06:42.521 WARN  c.i.y.p.algorithm.core.ORToolsBootstrap - ❌ 高级JNI恢复失败
2025-08-20 00:06:42.521 WARN  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - ⚠️  OR-Tools不可用，将使用Java实现的备用算法
2025-08-20 00:06:42.521 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - ORTools状态: OR-Tools状态: initialized=true, available=false
2025-08-20 00:06:42.524 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-20 00:06:42.525 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-20 00:06:42.525 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-20 00:06:42.525 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-20 00:06:42.525 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-20 00:06:42.526 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-20 00:06:42.688 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-20 00:06:42.689 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-20 00:06:42.690 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-20 00:06:42.690 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-20 00:06:42.690 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-20 00:06:42.690 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-20 00:06:42.694 ERROR c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ❌ [反射初始化] OR-Tools反射初始化失败: InvocationTargetException - null
2025-08-20 00:06:42.694 WARN  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ⚠️ [反射OR-Tools] OR-Tools反射初始化失败，将使用Java实现的备用算法
2025-08-20 00:06:42.697 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.697 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.697 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.697 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.697 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.699 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.699 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.699 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.699 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.699 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.699 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-20 00:06:42.702 WARN  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools JNI库加载失败
2025-08-20 00:06:42.703 WARN  c.i.y.p.algorithm.core.RobustORToolsTSP - ❌ OR-Tools不可用，将使用Java实现的备用算法
2025-08-20 00:06:42.704 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.704 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.704 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.704 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.704 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.707 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.707 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.707 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.707 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.712 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.712 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.712 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.712 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.712 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.715 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.716 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.716 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.716 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.721 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.721 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.721 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.723 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.723 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.723 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.723 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.723 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.726 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.726 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.726 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.726 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.726 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.726 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.726 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.726 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.729 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.729 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.729 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.730 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.730 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.734 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.734 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.738 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.739 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.739 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.739 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.741 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.741 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.741 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.741 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.741 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.741 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.743 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.746 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.747 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.749 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.749 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.749 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.749 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.749 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.749 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.753 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.755 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.755 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.755 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.756 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.757 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.759 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.763 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.764 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.764 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.764 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.764 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.764 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.784 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-20 00:06:42.785 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-20 00:06:42.785 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-20 00:06:42.806 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-20 00:06:42.807 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-20 00:06:42.807 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-20 00:06:42.807 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-20 00:06:42.807 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-20 00:06:42.807 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.811 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.811 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.811 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.811 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.813 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.814 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.814 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.814 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.814 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.816 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.817 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.817 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.817 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.818 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.818 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.818 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.818 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.818 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.818 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.818 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.820 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.820 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.821 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.822 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.822 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.822 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.822 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.822 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.822 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.823 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.823 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.823 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.825 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.825 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.826 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.827 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:06:42.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:06:42.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:06:42.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:06:42.829 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:06:42.829 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:06:42.829 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:06:42.829 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:06:42.829 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:06:42.829 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:06:42.832 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-20 00:09:30.533 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Apache Commons Math v3.6.1
2025-08-20 00:09:30.535 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Builtin Heuristic v1.0.0
2025-08-20 00:09:30.535 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-20 00:09:30.660 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-20 00:09:30.686 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.686 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.687 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-20 00:09:30.687 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-20 00:09:30.691 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-20 00:09:30.691 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-20 00:09:30.691 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-20 00:09:30.692 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-20 00:09:30.817 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.818 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-20 00:09:30.819 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.819 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.836 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-20 00:09:30.851 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-20 00:09:30.851 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-20 00:09:30.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.870 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.874 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.902 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.903 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.903 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.903 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.903 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.904 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.904 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.904 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.904 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.904 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.905 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.905 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.907 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.908 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.908 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.908 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.908 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.910 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.910 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.910 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.910 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.910 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.910 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.914 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.916 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.924 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.926 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.929 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.930 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.930 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.930 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.930 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.932 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.932 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.932 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.933 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.934 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.934 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.934 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.934 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.936 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:30.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.936 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.936 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.937 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.938 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.938 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.938 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.938 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.938 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.938 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.939 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.939 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.939 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:30.941 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:30.941 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:30.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.942 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.943 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.945 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.945 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.945 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.945 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.946 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.946 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.946 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.946 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.946 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.971 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.971 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.975 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.976 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.977 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.977 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.977 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.977 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.977 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.983 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.983 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.983 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.985 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.985 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.985 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.987 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.988 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.988 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.988 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.988 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:30.988 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:30.988 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:30.988 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:30.990 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:30.990 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.990 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.990 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.990 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.990 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.990 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.993 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:30.995 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:30.995 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:30.995 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:30.995 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:30.995 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:30.995 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:30.995 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:30.995 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:30.996 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:30.996 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:30.998 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.000 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.000 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.000 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.000 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.000 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.000 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.001 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.001 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.001 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.001 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.001 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.003 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.004 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.004 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.004 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.004 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.004 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.007 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.008 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.013 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.014 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.014 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.014 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.014 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.014 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.019 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.019 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.019 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.019 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.020 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.020 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.020 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.020 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.020 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.020 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.021 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.024 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.025 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.025 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.025 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.025 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.030 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.031 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.033 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.034 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.034 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.035 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.035 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.035 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.035 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.035 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.035 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.036 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.036 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.036 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.036 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.036 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.036 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.038 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.040 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.040 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.040 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.040 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.040 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.040 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.040 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.041 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.041 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.041 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.044 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.044 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.045 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.045 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.045 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.045 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.045 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.045 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.045 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.045 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.045 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.045 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.049 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-20 00:09:31.060 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - ✅ OR-Tools类预检查通过
2025-08-20 00:09:31.060 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 执行深度JNI修复...
2025-08-20 00:09:31.260 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - ✅ 系统库路径已重置
2025-08-20 00:09:31.260 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 检测到URLClassLoader
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - ✅ ClassLoader缓存清理完成
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - ✅ JNI系统属性设置完成
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - ✅ 深度JNI修复完成
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 尝试强制类重置...
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 类 com.google.ortools.constraintsolver.mainJNI 重置失败（可能是正常的）: com.google.ortools.constraintsolver.mainJNI
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 类 com.google.ortools.constraintsolver.RoutingModel 重置失败（可能是正常的）: com.google.ortools.constraintsolver.RoutingModel
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 类 com.google.ortools.constraintsolver.RoutingIndexManager 重置失败（可能是正常的）: com.google.ortools.constraintsolver.RoutingIndexManager
2025-08-20 00:09:31.354 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 开始分步加载OR-Tools...
2025-08-20 00:09:31.355 DEBUG c.i.y.p.algorithm.core.ORToolsBootstrap - 步骤1: 加载原生库
2025-08-20 00:09:31.375 ERROR c.i.y.p.algorithm.core.ORToolsBootstrap - ❌ 分步加载失败: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
2025-08-20 00:09:31.375 WARN  c.i.y.p.algorithm.core.ORToolsBootstrap - 常规初始化失败，尝试高级恢复...
2025-08-20 00:09:31.375 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始高级JNI恢复尝试...
2025-08-20 00:09:31.379 INFO  c.i.y.p.algorithm.core.AdvancedJNIRecovery - 开始高级JNI恢复...
2025-08-20 00:09:31.379 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 尝试Unsafe恢复...
2025-08-20 00:09:31.379 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 目标类不存在，这可能是正常的
2025-08-20 00:09:31.379 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 尝试ClassLoader恢复...
2025-08-20 00:09:31.380 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - ✅ URLClassPath缓存已清理
2025-08-20 00:09:31.555 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 验证类初始化...
2025-08-20 00:09:31.558 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 类初始化验证失败: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
2025-08-20 00:09:31.558 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 尝试JVM重置...
2025-08-20 00:09:31.559 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 重置系统属性: java.library.path = C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\Sun\Java\bin;C:\Windows\system32...
2025-08-20 00:09:31.559 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 重置系统属性: java.class.path = target/app.jar
2025-08-20 00:09:31.559 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 重置系统属性: sun.boot.library.path = C:\Program Files\Java\jre1.8.0_251\bin
2025-08-20 00:09:31.559 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - ✅ Native库路径已重置
2025-08-20 00:09:31.600 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - ✅ 缓存清理完成
2025-08-20 00:09:31.600 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - ✅ 类路径重新扫描完成
2025-08-20 00:09:31.600 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 验证类初始化...
2025-08-20 00:09:31.603 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 类初始化验证失败: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
2025-08-20 00:09:31.603 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 尝试类重新定义...
2025-08-20 00:09:31.603 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - Java版本: 1.8.0_251
2025-08-20 00:09:31.603 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 验证类初始化...
2025-08-20 00:09:31.607 DEBUG c.i.y.p.algorithm.core.AdvancedJNIRecovery - 类初始化验证失败: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
2025-08-20 00:09:31.607 WARN  c.i.y.p.algorithm.core.AdvancedJNIRecovery - ❌ 所有恢复策略都失败了
2025-08-20 00:09:31.607 WARN  c.i.y.p.algorithm.core.ORToolsBootstrap - ❌ 高级JNI恢复失败
2025-08-20 00:09:31.607 WARN  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - ⚠️  OR-Tools不可用，将使用Java实现的备用算法
2025-08-20 00:09:31.607 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - ORTools状态: OR-Tools状态: initialized=true, available=false
2025-08-20 00:09:31.610 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-20 00:09:31.610 DEBUG c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [污染检测] com.google.ortools.constraintsolver.RoutingModel - 状态: 已加载
2025-08-20 00:09:31.611 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-20 00:09:31.611 DEBUG c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [污染检测] com.google.ortools.constraintsolver.RoutingIndexManager - 状态: 已加载
2025-08-20 00:09:31.611 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-20 00:09:31.611 DEBUG c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [污染检测] com.google.ortools.Loader - 状态: 已加载
2025-08-20 00:09:31.611 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-20 00:09:31.611 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-20 00:09:31.613 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-20 00:09:31.613 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 修复环境...
2025-08-20 00:09:31.613 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ 环境修复完成
2025-08-20 00:09:31.613 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 修复库路径...
2025-08-20 00:09:31.613 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ 库路径修复完成
2025-08-20 00:09:31.613 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 修复系统属性...
2025-08-20 00:09:31.614 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ 系统属性修复完成
2025-08-20 00:09:31.614 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 清理JVM状态...
2025-08-20 00:09:31.771 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JVM状态清理完成
2025-08-20 00:09:31.771 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 验证修复效果...
2025-08-20 00:09:31.774 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 发现305个OR-Tools临时文件
2025-08-20 00:09:31.775 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - 基本类加载能力正常
2025-08-20 00:09:31.775 DEBUG c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ 验证通过
2025-08-20 00:09:31.775 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-20 00:09:31.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-20 00:09:31.776 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-20 00:09:31.776 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-20 00:09:31.776 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-20 00:09:31.776 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-20 00:09:31.776 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-20 00:09:31.776 DEBUG c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 📦 [反射初始化] 加载Loader类
2025-08-20 00:09:31.776 DEBUG c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔒 [安全加载] 开始安全加载OR-Tools类: com.google.ortools.Loader
2025-08-20 00:09:31.776 DEBUG c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [安全加载] OR-Tools类加载成功: com.google.ortools.Loader
2025-08-20 00:09:31.777 DEBUG c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔗 [反射初始化] 测试JNI库加载
2025-08-20 00:09:31.779 ERROR c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ❌ [反射初始化] OR-Tools反射初始化失败: InvocationTargetException - null
2025-08-20 00:09:31.780 DEBUG c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 反射初始化异常详情:
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at com.ict.ycwl.pathcalculate.algorithm.core.ReflectiveORToolsTSP.safelyInitializeORToolsReflection(ReflectiveORToolsTSP.java:93)
	at com.ict.ycwl.pathcalculate.algorithm.core.ReflectiveORToolsTSP.<init>(ReflectiveORToolsTSP.java:55)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(Unknown Source)
	at java.lang.reflect.Constructor.newInstance(Unknown Source)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1315)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.ict.ycwl.pathcalculate.PathCalculateApplication.main(PathCalculateApplication.java:14)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:107)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
Caused by: java.lang.RuntimeException: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
	at com.google.ortools.Loader.loadNativeLibraries(Loader.java:127)
	... 39 common frames omitted
Caused by: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
	at com.sun.nio.zipfs.ZipPath.getAttributes(ZipPath.java:666)
	at com.sun.nio.zipfs.ZipFileSystemProvider.readAttributes(ZipFileSystemProvider.java:294)
	at java.nio.file.Files.readAttributes(Unknown Source)
	at java.nio.file.FileTreeWalker.getAttributes(Unknown Source)
	at java.nio.file.FileTreeWalker.visit(Unknown Source)
	at java.nio.file.FileTreeWalker.walk(Unknown Source)
	at java.nio.file.Files.walkFileTree(Unknown Source)
	at java.nio.file.Files.walkFileTree(Unknown Source)
	at com.google.ortools.Loader.lambda$unpackNativeResources$0(Loader.java:68)
	at com.google.ortools.Loader.unpackNativeResources(Loader.java:97)
	at com.google.ortools.Loader.loadNativeLibraries(Loader.java:119)
	... 39 common frames omitted
2025-08-20 00:09:31.781 WARN  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ⚠️ [反射OR-Tools] OR-Tools反射初始化失败，将使用Java实现的备用算法
2025-08-20 00:09:31.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.784 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.785 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.785 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.786 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.786 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.786 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.786 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.787 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.787 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.787 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.787 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.787 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-20 00:09:31.791 DEBUG c.i.y.p.algorithm.core.RobustORToolsTSP - JNI库加载异常: java.nio.file.NoSuchFileException: /BOOT-INF/lib/ortools-win32-x86-64-9.8.3296.jar!/ortools-win32-x86-64/
2025-08-20 00:09:31.792 WARN  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools JNI库加载失败
2025-08-20 00:09:31.792 WARN  c.i.y.p.algorithm.core.RobustORToolsTSP - ❌ OR-Tools不可用，将使用Java实现的备用算法
2025-08-20 00:09:31.794 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.795 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.795 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.795 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.795 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.795 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.796 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.796 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.796 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.797 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.797 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.797 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.802 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.802 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.802 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.803 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.803 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.803 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.804 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.804 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.804 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.804 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.804 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.804 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.804 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.805 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.805 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.805 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.805 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.807 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.808 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.808 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.809 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.809 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.810 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.810 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.810 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.811 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.811 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.811 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.811 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.811 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.811 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.811 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.811 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.814 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.814 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.814 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.815 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.815 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.815 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.816 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.816 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.816 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.816 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.816 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.816 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.816 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.816 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.816 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.817 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.817 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.817 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.817 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.817 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.817 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.819 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.820 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.820 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.820 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.821 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.821 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.821 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.822 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.822 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.822 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.822 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.822 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.822 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.822 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.822 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.822 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.824 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.824 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.824 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.825 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.826 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.826 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.826 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.826 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.826 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.826 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.826 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.826 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.827 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.827 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.827 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.828 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.828 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.829 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.829 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.829 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.831 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.832 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.832 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.832 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.832 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.832 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.832 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.832 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.832 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.832 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.832 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.832 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.835 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.835 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.835 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.835 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.835 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.835 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.837 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.838 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.838 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.838 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.838 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.838 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.838 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.839 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.839 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.840 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.840 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.840 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.842 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.842 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.842 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.843 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.844 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.844 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.844 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.844 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.844 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.844 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.844 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.844 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.846 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-20 00:09:31.867 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-20 00:09:31.867 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-20 00:09:31.868 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-20 00:09:31.889 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-20 00:09:31.890 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.891 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.892 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.892 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.893 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.894 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.894 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.894 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.894 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.894 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.894 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.894 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.896 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.896 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.896 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.896 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.896 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.896 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.896 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.900 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.901 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.901 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.901 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.902 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.902 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.902 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.904 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.905 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.905 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.905 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.905 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.905 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.905 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.905 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.906 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.906 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.907 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.909 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.909 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.909 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.909 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.909 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.910 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.910 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.911 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.911 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.912 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.912 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.912 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.913 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.915 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.916 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.917 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.917 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.917 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.917 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.917 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.917 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.917 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.917 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.918 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.918 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.921 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.923 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.923 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.923 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.923 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.923 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.923 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.923 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.924 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.927 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.929 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.929 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-20 00:09:31.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-20 00:09:31.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-20 00:09:31.930 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-20 00:09:31.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-20 00:09:31.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-20 00:09:31.930 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingIndexManager创建成功
2025-08-20 00:09:31.933 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] RoutingModel创建成功
2025-08-20 00:09:31.934 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [基本测试] 基本求解测试完成，解状态: 有解
2025-08-20 00:09:31.934 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-20 00:09:31.934 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-20 00:09:31.934 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-20 00:09:31.934 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - 🔍 [OR-Tools可用性] 手动加载: true, 初始化: true, 最终结果: true
2025-08-20 00:09:31.934 DEBUG c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [OR-Tools可用] 手动加载成功且初始化完成
2025-08-20 00:09:31.935 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-20 00:09:31.935 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-20 00:09:31.935 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-20 00:09:31.935 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-20 00:09:31.935 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-20 00:09:31.937 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
