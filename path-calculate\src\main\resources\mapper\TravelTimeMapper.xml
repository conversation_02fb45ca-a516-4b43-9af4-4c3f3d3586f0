<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper">

    <!-- 获取总记录数 -->
    <select id="getTotalCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM travel_time
    </select>

    <!-- 根据偏移量获取样本数据 -->
    <select id="getSampleByOffset" resultType="com.ict.ycwl.pathcalculate.pojo.TravelTime">
        SELECT longitude_start, latitude_start, longitude_end, latitude_end, travel_time
        FROM travel_time
        WHERE longitude_start != '0' AND latitude_start != '0'
        AND longitude_end != '0' AND latitude_end != '0'
        AND travel_time > 0
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
