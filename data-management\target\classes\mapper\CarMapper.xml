<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.CarMapper">
    <update id="deleteById">
        update car
        set is_delete=1
        where car_id = #{carId}
    </update>
    <update id="myUpdateById">
        UPDATE car SET `license_plate_number`=#{licensePlateNumber}, `max_load`=#{maxLoad}, `status`=#{status}, `transit_depot_id`=#{transitDepotId}, `car_driver_id`=#{carDriverId}, `is_delete`=#{isDelete}, `delivery_area_id`=#{deliveryAreaId} WHERE `car_id`=#{carId}
    </update>
    <select id="mySelectCarList" resultType="com.ict.datamanagement.domain.vo.carVO.CarVO">
        select t1.car_id,t1.license_plate_number,t2.user_name,t2.phone,t1.max_load,t1.`status`,t3.delivery_area_name,t4.team_name  FROM
        car AS t1 left join `user` AS t2 ON t1.car_driver_id=t2.user_id left join delivery_area as t3 on
        t1.delivery_area_id=t3.delivery_area_id left join team as t4 on t3.team_id = t4.team_id
        <where>
            <if test="licensePlateNumber != null and licensePlateNumber.trim() != ''">
                AND license_plate_number=#{licensePlateNumber}
            </if>
            <if test="teamName != null and teamName.trim() != ''">
                AND t4.team_name=#{teamName}
            </if>
            <if test="carDriver!= null and carDriver.trim() != ''">
                AND user_name=#{carDriver}
            </if>
            <if test="status!= null and status.trim() != ''">
                AND t1.`status`=#{status}
            </if>
            <if test="maxLoad!= null and maxLoad.trim() != ''">
                AND max_load=#{maxLoad}
            </if>
            AND t1.is_delete=0
            AND is_fact=0
            AND t3.is_delete=0
        </where>
    </select>
    <select id="selectLicensePlateNumber" resultType="java.lang.String">
        select DISTINCT license_plate_number
        from car
        where is_delete = 0
    </select>
    <select id="selectMaxLoad" resultType="java.lang.String">
        select DISTINCT max_load
        from car
        where is_delete = 0
    </select>
    <select id="getCarActualVOList" resultType="com.ict.datamanagement.domain.vo.carVO.CarActualVO">
        select
        t1.car_id,t1.license_plate_number,t3.user_name as carDriverName,t1.actual_load,t1.actual_time,t1.route_name,t1.`week`,t1.date,t4.team_name
        ,t2.delivery_area_name FROM car AS t1 left JOIN delivery_area AS t2 ON t1.delivery_area_id=t2.delivery_area_id left JOIN `user` AS t3 ON
        t1.car_driver_id=t3.user_id left JOIN team AS t4 ON t2.team_id=t4.team_id
        <where>
            and t1.is_delete=0
            <if test="licensePlateNumber!= null and licensePlateNumber.trim() != ''">
                and t1.license_plate_number=#{licensePlateNumber}
            </if>
            <if test="teamName!= null and teamName.trim() != ''">
                and t4.team_name=#{teamName}
            </if>
            <if test="carDriverName!= null and carDriverName.trim() != ''">
               and t3.user_name=#{carDriverName}
            </if>
            <if test="date!= null and date.trim() != ''">
                and t1.date=#{date}
            </if>
            and t1.is_fact=1
        </where>
    </select>
    <select id="selectMaxLoadBylicensePlateNumber" resultType="java.lang.String">
        select max_load from car where license_plate_number=#{licensePlateNumber}
    </select>
    <select id="selectCarDriver" resultType="com.ict.datamanagement.domain.entity.CarDriver">
        select user_id,user_name,phone from user where position="物流部经办人员"
    </select>
    <select id="selectLicensePlateNumberAndId"
            resultType="com.ict.datamanagement.domain.dto.car.LicensePlateNumbersDTO">
        select car_id,license_plate_number,max_load from car
    </select>
    <select id="checkLicensePlateNumber" resultType="java.lang.Integer">
        select COUNT(*) from car where license_plate_number=#{licensePlateNumber} and is_delete=0
    </select>
    <select id="selectCarDriverNumber" resultType="java.lang.Integer">
        select COUNT(*) from `user` where user_name=#{carDriverName}
    </select>
    <select id="selectCarDriverIdByName" resultType="java.lang.Long">
        select user_id from `user` where user_name=#{carDriverName} and `position`="物流部经办人员"
    </select>
    <select id="checkLicensePlateNumberAndDataAndRoute" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (SELECT license_plate_number
              FROM car
              WHERE license_plate_number = #{licensePlateNumber}
                AND `date` = #{date}
                AND is_delete = 0
              UNION
              SELECT license_plate_number
              FROM car
              WHERE license_plate_number = #{licensePlateNumber}
                AND route_name = #{route}
                AND is_delete = 0) AS combined
    </select>
    <select id="selectCountByLicensePlateNumberAndDate" resultType="java.lang.Integer">
        select COUNT(*) from car where license_plate_number = #{licensePlateNumber} and `date`=#{date} and is_fact=1 and is_delete=0
    </select>
    <select id="selectCountByLicensePlateNumberAndRoute" resultType="java.lang.Integer">
        select COUNT(*) from car where license_plate_number = #{licensePlateNumber} and route_name=#{routeName} and is_fact=1 and is_delete=0
    </select>
    <select id="selectCarByLicensePlateNumber" resultType="com.ict.datamanagement.domain.entity.Car">
        select * from car where license_plate_number = #{licensePlateNumber} and is_fact=0 and is_delete=0
    </select>

    <select id="selectCountByArea" resultType="int">
        select COUNT(*) from car c JOIN delivery_area d ON c.delivery_area_id=d.delivery_area_id where d.delivery_area_name=#{s} and c.`status`=1 and c.is_fact=0
    </select>
</mapper>