<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.TransitDeliveryMapper">
    <delete id="myDeleteById">
        delete from transit_delivery where transit_depot_id=#{transitDepotId}
    </delete>
    <delete id="myDeleteByDeliveryId">
        delete from transit_delivery where delivery_area_id=#{deliveryAreaId} and transit_depot_id = #{oldTransitDepotId}
    </delete>
    <delete id="deleteByTransitDepotId">
        delete from transit_delivery where transit_depot_id=#{transitDepotId}
    </delete>
    <delete id="deleteByDeliveryId">
        delete from transit_delivery where delivery_area_id=#{deliveryAreaId}
    </delete>

    <select id="MyselectList" resultType="com.ict.datamanagement.domain.entity.TransitDelivery">
        select * from transit_delivery where transit_depot_id=#{transitDepotId}
    </select>
    <select id="mySelectCount" resultType="java.lang.Integer">
        select COUNT(*) from transit_delivery where transit_depot_id=#{transitDepotId} and delivery_area_id=#{deliveryAreaId}
    </select>
    <select id="selectByDeliveryId" resultType="java.lang.Long">
        select transit_depot_id from transit_delivery where delivery_area_id=#{deliveryAreaId}
    </select>
</mapper>
