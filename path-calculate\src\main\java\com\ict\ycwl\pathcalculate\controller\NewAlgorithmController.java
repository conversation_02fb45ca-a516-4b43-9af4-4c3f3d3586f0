package com.ict.ycwl.pathcalculate.controller;

import com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtils;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.config.AlgorithmConfig;
import com.ict.ycwl.pathcalculate.pojo.ResultRoute;
import com.ict.ycwl.pathcalculate.service.adapter.DatabaseToAlgorithmAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新算法测试控制器
 * 用于测试和对接PathPlanningUtils新算法
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-11
 */
@Slf4j
@RestController
@RequestMapping("/api/new-algorithm")
public class NewAlgorithmController {

    @Autowired
    private DatabaseToAlgorithmAdapter databaseToAlgorithmAdapter;

    @Autowired
    private PathPlanningUtils pathPlanningUtils;

    @Autowired
    private AlgorithmConfig algorithmConfig;

    /**
     * 测试新算法的数据加载功能
     * GET /api/new-algorithm/test-data-loading
     */
    @GetMapping("/test-data-loading")
    public Map<String, Object> testDataLoading() {
        log.info("开始测试数据加载功能");
        Map<String, Object> result = new HashMap<>();
        
        try {
            PathPlanningRequest request = databaseToAlgorithmAdapter.loadDataFromDatabase();
            
            result.put("success", true);
            result.put("message", "数据加载成功");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("accumulationCount", request.getAccumulations().size());
            dataMap.put("transitDepotCount", request.getTransitDepots().size());
            dataMap.put("teamCount", request.getTeams().size());
            dataMap.put("timeMatrixSize", request.getTimeMatrix().size());
            result.put("data", dataMap);
            
            log.info("数据加载测试成功: 聚集区={}, 中转站={}, 班组={}, 时间矩阵={}", 
                    request.getAccumulations().size(),
                    request.getTransitDepots().size(),
                    request.getTeams().size(),
                    request.getTimeMatrix().size());
            
        } catch (Exception e) {
            log.error("数据加载测试失败", e);
            result.put("success", false);
            result.put("message", "数据加载失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 测试新算法的完整执行流程
     * POST /api/new-algorithm/test-full-execution
     */
    @PostMapping("/test-full-execution")
    public Map<String, Object> testFullExecution(@RequestParam(defaultValue = "test-api-key") String apiKey) {
        log.info("开始测试新算法完整执行流程");
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 数据加载
            log.info("步骤1: 加载数据");
            PathPlanningRequest request = databaseToAlgorithmAdapter.loadDataFromDatabase();
            log.info("数据加载完成: 聚集区={}, 中转站={}, 班组={}", 
                    request.getAccumulations().size(),
                    request.getTransitDepots().size(),
                    request.getTeams().size());
            
            // 2. 执行算法
            log.info("步骤2: 执行算法");
            long startTime = System.currentTimeMillis();
            PathPlanningResult algorithmResult = pathPlanningUtils.calculateWithSpring(request);
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 3. 检查结果
            if (!algorithmResult.isSuccess()) {
                log.error("算法执行失败: {}", algorithmResult.getErrorMessage());
                result.put("success", false);
                result.put("message", "算法执行失败: " + algorithmResult.getErrorMessage());
                return result;
            }
            
            log.info("算法执行成功: 路线数={}, 执行时间={}ms", 
                    algorithmResult.getRoutes().size(), executionTime);
            
            // 4. 结果转换
            log.info("步骤3: 转换结果格式");
            List<ResultRoute> resultRoutes = databaseToAlgorithmAdapter.convertAlgorithmResult(algorithmResult, apiKey);
            log.info("结果转换完成: 最终路线数={}", resultRoutes.size());
            
            // 5. 构建返回结果
            result.put("success", true);
            result.put("message", "新算法执行成功");
            Map<String, Object> inputDataMap = new HashMap<>();
            inputDataMap.put("accumulationCount", request.getAccumulations().size());
            inputDataMap.put("transitDepotCount", request.getTransitDepots().size());
            inputDataMap.put("teamCount", request.getTeams().size());
            inputDataMap.put("timeMatrixSize", request.getTimeMatrix().size());

            Map<String, Object> algorithmResultMap = new HashMap<>();
            algorithmResultMap.put("success", algorithmResult.isSuccess());
            algorithmResultMap.put("routeCount", algorithmResult.getRoutes().size());
            algorithmResultMap.put("executionTime", executionTime);
            algorithmResultMap.put("algorithmExecutionTime", algorithmResult.getExecutionTime());

            Map<String, Object> finalResultMap = new HashMap<>();
            finalResultMap.put("resultRouteCount", resultRoutes.size());

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("inputData", inputDataMap);
            dataMap.put("algorithmResult", algorithmResultMap);
            dataMap.put("finalResult", finalResultMap);
            result.put("data", dataMap);
            
            // 6. 添加路线详情（仅前3条用于展示）
            if (!resultRoutes.isEmpty()) {
                int displayCount = Math.min(3, resultRoutes.size());
                result.put("sampleRoutes", resultRoutes.subList(0, displayCount));
            }
            
        } catch (Exception e) {
            log.error("新算法执行测试失败", e);
            result.put("success", false);
            result.put("message", "执行失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            result.put("stackTrace", e.getStackTrace());
        }
        
        return result;
    }

    /**
     * 获取算法配置信息
     * GET /api/new-algorithm/config
     */
    @GetMapping("/config")
    public Map<String, Object> getAlgorithmConfig() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("success", true);
        result.put("config", algorithmConfig);
        result.put("description", algorithmConfig.getAlgorithmDescription());
        result.put("isValid", algorithmConfig.isValid());
        
        return result;
    }

    /**
     * 更新算法配置
     * POST /api/new-algorithm/config
     */
    @PostMapping("/config")
    public Map<String, Object> updateAlgorithmConfig(@RequestBody Map<String, Object> configUpdates) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以根据需要更新配置
            // 注意：实际项目中应该有更完善的配置更新机制
            if (configUpdates.containsKey("enableNewAlgorithm")) {
                algorithmConfig.setEnableNewAlgorithm((Boolean) configUpdates.get("enableNewAlgorithm"));
            }
            if (configUpdates.containsKey("enableFallback")) {
                algorithmConfig.setEnableFallback((Boolean) configUpdates.get("enableFallback"));
            }
            if (configUpdates.containsKey("timeoutMs")) {
                algorithmConfig.setTimeoutMs(((Number) configUpdates.get("timeoutMs")).longValue());
            }
            
            result.put("success", true);
            result.put("message", "配置更新成功");
            result.put("config", algorithmConfig);
            
        } catch (Exception e) {
            log.error("配置更新失败", e);
            result.put("success", false);
            result.put("message", "配置更新失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 比较新旧算法的执行结果
     * POST /api/new-algorithm/compare
     */
    @PostMapping("/compare")
    public Map<String, Object> compareAlgorithms(@RequestParam(defaultValue = "test-api-key") String apiKey) {
        log.info("开始比较新旧算法执行结果");
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 执行新算法
            long newAlgorithmStart = System.currentTimeMillis();
            PathPlanningRequest request = databaseToAlgorithmAdapter.loadDataFromDatabase();
            PathPlanningResult newResult = pathPlanningUtils.calculateWithSpring(request);
            List<ResultRoute> newRoutes = databaseToAlgorithmAdapter.convertAlgorithmResult(newResult, apiKey);
            long newAlgorithmTime = System.currentTimeMillis() - newAlgorithmStart;
            
            // 注意：这里无法直接调用旧算法，因为需要修改CalculateServiceImpl
            // 实际项目中可以通过其他方式获取旧算法结果进行比较
            
            result.put("success", true);
            result.put("message", "算法比较完成");
            Map<String, Object> newAlgorithmMap = new HashMap<>();
            newAlgorithmMap.put("success", newResult.isSuccess());
            newAlgorithmMap.put("routeCount", newRoutes.size());
            newAlgorithmMap.put("executionTime", newAlgorithmTime);
            newAlgorithmMap.put("algorithmExecutionTime", newResult.getExecutionTime());
            result.put("newAlgorithm", newAlgorithmMap);
            result.put("note", "旧算法比较功能需要进一步集成");
            
        } catch (Exception e) {
            log.error("算法比较失败", e);
            result.put("success", false);
            result.put("message", "比较失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 健康检查接口
     * GET /api/new-algorithm/health
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查各个组件是否正常
            boolean adapterOk = databaseToAlgorithmAdapter != null;
            boolean algorithmOk = pathPlanningUtils != null;
            boolean configOk = algorithmConfig != null && algorithmConfig.isValid();
            
            boolean allOk = adapterOk && algorithmOk && configOk;
            
            result.put("success", allOk);
            result.put("status", allOk ? "healthy" : "unhealthy");
            Map<String, Object> componentsMap = new HashMap<>();
            componentsMap.put("databaseAdapter", adapterOk ? "ok" : "error");
            componentsMap.put("pathPlanningUtils", algorithmOk ? "ok" : "error");
            componentsMap.put("algorithmConfig", configOk ? "ok" : "error");
            result.put("components", componentsMap);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
            result.put("success", false);
            result.put("status", "error");
            result.put("message", e.getMessage());
        }
        
        return result;
    }
}
