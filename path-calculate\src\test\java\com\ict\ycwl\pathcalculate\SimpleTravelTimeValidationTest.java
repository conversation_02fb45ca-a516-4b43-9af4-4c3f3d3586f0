package com.ict.ycwl.pathcalculate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestTemplate;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 简化版Travel Time数据验证测试
 * 不依赖Spring Boot上下文，直接使用JDBC连接数据库
 * 
 * 使用前请：
 * 1. 配置数据库连接信息
 * 2. 配置高德API Key
 * 3. 调整测试参数
 */
@Slf4j
public class SimpleTravelTimeValidationTest {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 数据库连接配置 - 请根据实际情况修改
    private static final String DB_URL = "****************************************************************************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "aA13717028793#";
    
    // 高德API配置
    private static final String AMAP_API_KEY = "a123fae9da370c45984c58720bf3ac7c"; // 请替换为实际的高德API Key
    private static final String AMAP_DIRECTION_URL = "https://restapi.amap.com/v3/direction/driving";
    
    // 测试配置
    private static final int SAMPLE_SIZE = 1000; // 减少到20条，避免API配额问题
    private static final int REQUEST_DELAY_MS = 300; // 增加到300ms间隔
    private static final double TOLERANCE_PERCENTAGE = 10.0; // 允许误差20%
    
    @Test
    public void testTravelTimeAccuracy() {
        log.info("🧪 开始Travel Time数据准确性验证测试（简化版）");
        log.info("📊 配置信息: 抽样{}条, 请求间隔{}ms, 允许误差{}%", 
                SAMPLE_SIZE, REQUEST_DELAY_MS, TOLERANCE_PERCENTAGE);
        
        if ("your_amap_api_key_here".equals(AMAP_API_KEY)) {
            log.error("❌ 请先配置高德API Key！");
            return;
        }
        
        try {
            // 1. 从数据库随机抽取样本
            List<TravelTimeData> samples = getSampleTravelTimes();
            log.info("✅ 成功抽取{}条样本数据", samples.size());
            
            if (samples.isEmpty()) {
                log.warn("⚠️ 没有获取到有效样本数据");
                return;
            }
            
            // 2. 验证每个样本
            List<ValidationResult> results = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;
            
            for (int i = 0; i < samples.size(); i++) {
                TravelTimeData sample = samples.get(i);
                log.info("🔍 验证第{}/{}条: ({},{}) -> ({},{})", 
                        i + 1, samples.size(),
                        sample.getLongitudeStart(), sample.getLatitudeStart(),
                        sample.getLongitudeEnd(), sample.getLatitudeEnd());
                
                try {
                    ValidationResult result = validateSingleRoute(sample);
                    results.add(result);
                    
                    if (result.isValid()) {
                        successCount++;
                        log.info("✅ 验证通过: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}%",
                                result.getOsrmTime(), result.getAmapTime(), result.getErrorPercentage());
                    } else {
                        log.warn("⚠️ 验证失败: OSRM={:.1f}分钟, 高德={:.1f}分钟, 误差={:.1f}% (超过{}%)",
                                result.getOsrmTime(), result.getAmapTime(), result.getErrorPercentage(), TOLERANCE_PERCENTAGE);
                    }
                    
                    // 控制请求频率
                    Thread.sleep(REQUEST_DELAY_MS);
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("❌ 验证出错: {}", e.getMessage());
                    
                    ValidationResult errorResult = new ValidationResult();
                    errorResult.setTravelTimeData(sample);
                    errorResult.setError(true);
                    errorResult.setErrorMessage(e.getMessage());
                    results.add(errorResult);
                }
            }
            
            // 3. 生成验证报告
            generateValidationReport(results, successCount, errorCount);
            
        } catch (Exception e) {
            log.error("❌ 测试执行失败", e);
        }
    }
    
    /**
     * 从数据库随机抽取样本数据
     */
    private List<TravelTimeData> getSampleTravelTimes() {
        log.info("🎯 开始从数据库抽取样本数据...");
        
        List<TravelTimeData> samples = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD)) {
            // 获取总记录数
            String countSql = "SELECT COUNT(*) FROM travel_time WHERE longitude_start != '0' AND latitude_start != '0' AND longitude_end != '0' AND latitude_end != '0' AND travel_time > 0";
            long totalCount = 0;
            
            try (PreparedStatement countStmt = conn.prepareStatement(countSql);
                 ResultSet countRs = countStmt.executeQuery()) {
                if (countRs.next()) {
                    totalCount = countRs.getLong(1);
                }
            }
            
            log.info("📊 数据库有效记录数: {}", totalCount);
            
            if (totalCount == 0) {
                log.warn("⚠️ 数据库中没有有效的travel_time数据");
                return samples;
            }
            
            // 随机抽取样本
            Random random = new Random();
            Set<Long> selectedOffsets = new HashSet<>();
            
            // 生成不重复的随机偏移量
            while (selectedOffsets.size() < Math.min(SAMPLE_SIZE, (int) totalCount)) {
                long offset = (long) (random.nextDouble() * totalCount);
                selectedOffsets.add(offset);
            }
            
            // 根据偏移量获取样本
            String sampleSql = "SELECT longitude_start, latitude_start, longitude_end, latitude_end, travel_time " +
                              "FROM travel_time " +
                              "WHERE longitude_start != '0' AND latitude_start != '0' " +
                              "AND longitude_end != '0' AND latitude_end != '0' " +
                              "AND travel_time > 0 " +
                              "LIMIT ?, 1";
            
            try (PreparedStatement sampleStmt = conn.prepareStatement(sampleSql)) {
                for (Long offset : selectedOffsets) {
                    sampleStmt.setLong(1, offset);
                    
                    try (ResultSet rs = sampleStmt.executeQuery()) {
                        if (rs.next()) {
                            TravelTimeData sample = new TravelTimeData();
                            sample.setLongitudeStart(rs.getString("longitude_start"));
                            sample.setLatitudeStart(rs.getString("latitude_start"));
                            sample.setLongitudeEnd(rs.getString("longitude_end"));
                            sample.setLatitudeEnd(rs.getString("latitude_end"));
                            sample.setTravelTime(rs.getDouble("travel_time"));
                            
                            // 验证坐标有效性
                            if (isValidCoordinate(sample.getLongitudeStart(), sample.getLatitudeStart()) &&
                                isValidCoordinate(sample.getLongitudeEnd(), sample.getLatitudeEnd())) {
                                samples.add(sample);
                            }
                        }
                    }
                }
            }
            
        } catch (SQLException e) {
            log.error("❌ 数据库查询失败", e);
        }
        
        log.info("✅ 成功抽取{}条有效样本", samples.size());
        return samples;
    }
    
    /**
     * 验证单条路线数据
     */
    private ValidationResult validateSingleRoute(TravelTimeData travelTimeData) throws Exception {
        // 调用高德API获取实际行驶时间
        double amapTime = getAmapTravelTime(
                Double.parseDouble(travelTimeData.getLongitudeStart()), 
                Double.parseDouble(travelTimeData.getLatitudeStart()),
                Double.parseDouble(travelTimeData.getLongitudeEnd()), 
                Double.parseDouble(travelTimeData.getLatitudeEnd())
        );
        
        double osrmTime = travelTimeData.getTravelTime();
        double errorPercentage = Math.abs(amapTime - osrmTime) / amapTime * 100;
        
        ValidationResult result = new ValidationResult();
        result.setTravelTimeData(travelTimeData);
        result.setOsrmTime(osrmTime);
        result.setAmapTime(amapTime);
        result.setErrorPercentage(errorPercentage);
        result.setValid(errorPercentage <= TOLERANCE_PERCENTAGE);
        
        return result;
    }
    
    /**
     * 调用高德API获取行驶时间
     */
    private double getAmapTravelTime(double startLng, double startLat, double endLng, double endLat) throws Exception {
        String origin = String.format("%.6f,%.6f", startLng, startLat);
        String destination = String.format("%.6f,%.6f", endLng, endLat);
        
        String url = String.format("%s?key=%s&origin=%s&destination=%s&strategy=1&output=json",
                AMAP_DIRECTION_URL, AMAP_API_KEY, origin, destination);
        
        try {
            String responseBody = restTemplate.getForObject(url, String.class);
            
            if (responseBody == null) {
                throw new RuntimeException("高德API返回空响应");
            }
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            if (!"1".equals(jsonNode.get("status").asText())) {
                throw new RuntimeException("高德API返回错误: " + jsonNode.get("info").asText());
            }
            
            JsonNode route = jsonNode.get("route");
            if (route == null || !route.has("paths") || route.get("paths").size() == 0) {
                throw new RuntimeException("高德API未返回路径数据");
            }
            
            // 获取行驶时间（秒）并转换为分钟
            int durationSeconds = route.get("paths").get(0).get("duration").asInt();
            return durationSeconds / 60.0;
            
        } catch (Exception e) {
            throw new RuntimeException("调用高德API失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查坐标是否有效
     */
    private boolean isValidCoordinate(String lngStr, String latStr) {
        try {
            double lng = Double.parseDouble(lngStr);
            double lat = Double.parseDouble(latStr);
            // 中国境内大致坐标范围
            return lng >= 73.0 && lng <= 135.0 && lat >= 18.0 && lat <= 54.0 && lng != 0.0 && lat != 0.0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 生成验证报告
     */
    private void generateValidationReport(List<ValidationResult> results, int successCount, int errorCount) {
        log.info("📊 开始生成验证报告...");
        
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String reportFile = "target/test-results/simple_travel_time_validation_" + timestamp + ".txt";
            
            // 确保目录存在
            java.io.File dir = new java.io.File("target/test-results");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            try (FileWriter writer = new FileWriter(reportFile)) {
                writer.write("Travel Time数据验证报告（简化版）\n");
                writer.write("==================================================\n");
                writer.write("生成时间: " + LocalDateTime.now() + "\n");
                writer.write("抽样数量: " + results.size() + "\n");
                writer.write("验证通过: " + successCount + "\n");
                writer.write("验证失败: " + (results.size() - successCount - errorCount) + "\n");
                writer.write("请求错误: " + errorCount + "\n");
                writer.write("通过率: " + String.format("%.1f%%", successCount * 100.0 / results.size()) + "\n");
                writer.write("允许误差: " + TOLERANCE_PERCENTAGE + "%\n\n");
                
                // 统计信息
                double totalError = 0;
                int validResults = 0;
                double maxError = 0;
                double minError = Double.MAX_VALUE;
                
                writer.write("详细验证结果:\n");
                writer.write("序号\tOSRM时间\t高德时间\t误差%\t状态\t起点坐标\t终点坐标\n");
                
                for (int i = 0; i < results.size(); i++) {
                    ValidationResult result = results.get(i);
                    if (result.isError()) {
                        writer.write(String.format("%d\t-\t-\t-\t错误\t%s,%s\t%s,%s\t%s\n",
                                i + 1,
                                result.getTravelTimeData().getLongitudeStart(),
                                result.getTravelTimeData().getLatitudeStart(),
                                result.getTravelTimeData().getLongitudeEnd(),
                                result.getTravelTimeData().getLatitudeEnd(),
                                result.getErrorMessage()));
                    } else {
                        String status = result.isValid() ? "通过" : "失败";
                        writer.write(String.format("%d\t%.1f\t%.1f\t%.1f\t%s\t%s,%s\t%s,%s\n",
                                i + 1,
                                result.getOsrmTime(),
                                result.getAmapTime(),
                                result.getErrorPercentage(),
                                status,
                                result.getTravelTimeData().getLongitudeStart(),
                                result.getTravelTimeData().getLatitudeStart(),
                                result.getTravelTimeData().getLongitudeEnd(),
                                result.getTravelTimeData().getLatitudeEnd()));
                        
                        totalError += result.getErrorPercentage();
                        validResults++;
                        maxError = Math.max(maxError, result.getErrorPercentage());
                        minError = Math.min(minError, result.getErrorPercentage());
                    }
                }
                
                if (validResults > 0) {
                    writer.write("\n统计摘要:\n");
                    writer.write("平均误差: " + String.format("%.1f%%", totalError / validResults) + "\n");
                    writer.write("最大误差: " + String.format("%.1f%%", maxError) + "\n");
                    writer.write("最小误差: " + String.format("%.1f%%", minError) + "\n");
                }
                
                writer.write("\n结论:\n");
                double passRate = successCount * 100.0 / results.size();
                if (passRate >= 80) {
                    writer.write("✅ OSRM数据质量良好，通过率" + String.format("%.1f%%", passRate) + "\n");
                } else if (passRate >= 60) {
                    writer.write("⚠️ OSRM数据质量一般，通过率" + String.format("%.1f%%", passRate) + "，建议进一步检查\n");
                } else {
                    writer.write("❌ OSRM数据质量较差，通过率" + String.format("%.1f%%", passRate) + "，建议重新生成\n");
                }
            }
            
            log.info("✅ 验证报告已生成: {}", reportFile);
            log.info("📊 验证结果摘要: 总数{}, 通过{}, 失败{}, 错误{}, 通过率{:.1f}%",
                    results.size(), successCount, results.size() - successCount - errorCount, 
                    errorCount, successCount * 100.0 / results.size());
            
        } catch (IOException e) {
            log.error("❌ 生成报告失败", e);
        }
    }
    
    /**
     * 简化的Travel Time数据类
     */
    private static class TravelTimeData {
        private String longitudeStart;
        private String latitudeStart;
        private String longitudeEnd;
        private String latitudeEnd;
        private Double travelTime;
        
        // Getters and Setters
        public String getLongitudeStart() { return longitudeStart; }
        public void setLongitudeStart(String longitudeStart) { this.longitudeStart = longitudeStart; }
        
        public String getLatitudeStart() { return latitudeStart; }
        public void setLatitudeStart(String latitudeStart) { this.latitudeStart = latitudeStart; }
        
        public String getLongitudeEnd() { return longitudeEnd; }
        public void setLongitudeEnd(String longitudeEnd) { this.longitudeEnd = longitudeEnd; }
        
        public String getLatitudeEnd() { return latitudeEnd; }
        public void setLatitudeEnd(String latitudeEnd) { this.latitudeEnd = latitudeEnd; }
        
        public Double getTravelTime() { return travelTime; }
        public void setTravelTime(Double travelTime) { this.travelTime = travelTime; }
    }
    
    /**
     * 验证结果数据类
     */
    private static class ValidationResult {
        private TravelTimeData travelTimeData;
        private double osrmTime;
        private double amapTime;
        private double errorPercentage;
        private boolean valid;
        private boolean error;
        private String errorMessage;
        
        // Getters and Setters
        public TravelTimeData getTravelTimeData() { return travelTimeData; }
        public void setTravelTimeData(TravelTimeData travelTimeData) { this.travelTimeData = travelTimeData; }
        
        public double getOsrmTime() { return osrmTime; }
        public void setOsrmTime(double osrmTime) { this.osrmTime = osrmTime; }
        
        public double getAmapTime() { return amapTime; }
        public void setAmapTime(double amapTime) { this.amapTime = amapTime; }
        
        public double getErrorPercentage() { return errorPercentage; }
        public void setErrorPercentage(double errorPercentage) { this.errorPercentage = errorPercentage; }
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public boolean isError() { return error; }
        public void setError(boolean error) { this.error = error; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
