# 测试环境配置
spring:
  application:
    name: pathcalculate-test
  
  # 禁用 Nacos 配置，避免测试时连接外部服务
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
  
  # 数据库配置 - 使用内存数据库或禁用数据源
  datasource:
    # 禁用默认数据源自动配置，避免连接数据库
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect

# MyBatis配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true

# 自定义配置
jjking:
  dbPath: "/tmp/test-db-config.txt"
  MybatisFile: "test"
    
# 日志配置
logging:
  level:
    com.ict.ycwl.pathcalculate: DEBUG
    org.springframework: WARN
    org.hibernate: WARN
    # 屏蔽第三方库的DEBUG日志
    com.graphhopper.jsprit: WARN
    org.optaplanner: WARN
    org.drools: WARN