<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pickup.mapper.SiteSelectionMapper">

    <select id="selectGroupById" resultType="java.lang.String">
        SELECT GROUP_CONCAT(s.store_name) AS storeNames
        FROM site_selection AS ss
                 JOIN site_store AS si ON ss.id = si.site_selection_id
                 JOIN pickup_user AS s ON si.store_id = s.id where ss.id=#{id}
        GROUP BY ss.id;
    </select>

    <select id="selectByName" resultType="com.ict.ycwl.pickup.pojo.entity.SiteSelection">
        select * from site_selection where pickup_name=#{pickupContainers}
    </select>
</mapper>