<template>
  <div class="area">
    <div class="map">
      <div class="btn-box">
        <p>显示:</p>
        <el-select class="select" v-model="specialStatus">
          <el-option label="全部" value="0" />
          <el-option label="仅特殊点" value="1" />
        </el-select>
        <el-button
          v-if="hasOp('cluster-calculate:recalculate')"
          @click="CalculateBtnFunction"
          >重新计算</el-button
        >
        <el-button
          v-if="hasOp('cluster-calculate:adjust')"
          class="adjust"
          @click="routerChange"
          >聚集区微调</el-button
        >
        <el-button class="save" @click="save">保存结果</el-button>
        <el-button class="save" @click="update">更新地图数据</el-button>
      </div>
      <div
        id="container"
        v-loading="isMaoFinished"
        element-loading-text="地图数据加载中..."
        element-loading-background="rgba(0,23,49,0.8)"
      ></div>
    </div>
    <BorderBox9 :color="['#90ade8', '#90ade8']" backgroundColor="#001731">
      <div class="region">
        <el-scrollbar
          v-loading="isResultPointsFinished"
          element-loading-text="加载中..."
          element-loading-background="rgba(0,23,49,0.8)"
          height="75vh"
        >
          <el-collapse v-model="activeDistricts" accordion>
            <el-collapse-item
              v-for="(towns, district) in clusterStore.clusterDepotAll"
              :key="district"
              :title="district"
              :name="district"
            >
              <el-collapse v-model="activeTowns[district]" accordion>
                <el-collapse-item
                  v-for="(shops, town) in towns"
                  :key="town"
                  :title="town"
                  :name="town"
                >
                  <div
                    v-for="shop in shops"
                    :key="shop.accumulation"
                    :id="'shop-' + shop.accumulation"
                    class="regionCollapseItemContext"
                    @click="mapShow([shop.longitude, shop.latitude])"
                    :class="
                      longitude === shop.longitude && latitude === shop.latitude
                        ? 'active'
                        : ''
                    "
                  >
                    {{ shop.name }}
                  </div>
                </el-collapse-item>
              </el-collapse>
            </el-collapse-item>
          </el-collapse>
        </el-scrollbar>
      </div>
    </BorderBox9>
  </div>
</template>
<script lang="ts" setup>
  import { MAP_KEY, SECURITY_CODE } from "@/utils/getMapKey";
  import { useClusterStore } from "@/store/cluster";
  import { hasOp } from "@/op";
  import { DArrowLeft, DArrowRight } from "@element-plus/icons-vue";
  import { ElLoading } from "element-plus";
  import { useRouter } from "vue-router";
  import { BorderBox9 } from "@dataview/datav-vue3";
  import { modifyUserAgent } from "@/utils/modifyUserAgent";
  import { ref, reactive } from "vue";
  modifyUserAgent();
  window._AMapSecurityConfig = {
    securityJsCode: SECURITY_CODE,
  };
  const specialStatus = ref<string>("0");
  const router = useRouter();
  // 跳转
  function routerChange() {
    router.replace("/home/<USER>/AreaAdjust");
  }
  //聚集区Store
  const clusterStore = useClusterStore();

  //获取修改数据信息列表
  clusterStore.getInformationListAction();

  //获取聚集区错误点（只有数量、检查）
  clusterStore.getCheckErrorPointsAction();

  // 更新数据
  const isUpdate = ref<boolean>(false);
  function update() {
    if (clusterStore.MapResultPoints) {
      const Overlays = map.getAllOverlays("marker");
      map.remove(Overlays);
      isUpdate.value = true;
      getALLMapData();
    }
  }
  watch(specialStatus, (newValue) => {
    console.log(newValue);
    if (clusterStore.MapResultPoints) {
      const Overlays = map.getAllOverlays("marker");
      map.remove(Overlays);
      isUpdate.value = true;
      getALLMapData();
    }
  });
  // 保存结果
  function save() {
    if (clusterStore.ErrorPoints == undefined) {
      ElMessage.warning("数据处理中请稍后再试。");
    } else {
      const loading1 = ElLoading.service({
        lock: true,
        text: "保存中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (clusterStore.ErrorPoints === 0) {
        clusterStore.deleteClearInformationListAction().then(() => {
          clusterStore.getInformationListAction();
        });
        loading1.close();
        ElMessage.success("保存成功");
      } else {
        ElMessageBox({
          title: "注意!",
          message: "还有未保存的聚集区错误点，是否忽略并继续保存?",
          showCancelButton: true,
        })
          .then((action) => {
            console.log(action);
            if (action == "confirm") {
              clusterStore.deleteClearInformationListAction().then(() => {
                clusterStore.getInformationListAction();
              });
              loading1.close();
              ElMessage.success("保存成功");
            }
          })
          .catch(() => {
            loading1.close();
          });
      }
    }
  }
  //第二个折叠面板的参数
  const activeNames2 = ref();
  //定义是否加载完成的变量
  const isResultPointsFinished = ref<boolean>(true);
  //获取聚集区和商铺信息的方法

  //获取聚集区分区列表
  clusterStore.getClusterDepotAllAction().then(() => {
    isResultPointsFinished.value = false;
  });

  //计算接口
  const CalculateBtnFunction = () => {
    const loading = ElLoading.service({
      lock: true,
      text: "计算中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    clusterStore.postCalculateAllAction().then(() => {
      loading.close();
    });
  };
  // collapse 控制变量
  const activeDistricts = ref<string[]>([]);
  const activeTowns = reactive<{ [district: string]: string[] }>({});
  const longitude = ref<number>();
  const latitude = ref<number>();

  function findDistrictTownShopByLngLat(lng, lat) {
    const depotAll = clusterStore.clusterDepotAll;
    for (const district in depotAll) {
      const towns = depotAll[district];
      for (const town in towns) {
        const shops = towns[town];
        for (const shop of shops) {
          if (shop.longitude === lng && shop.latitude === lat) {
            return { district, town, shop };
          }
        }
      }
    }
    return null;
  }

  function mapShow(lnglat: [number, number]) {
    if (longitude.value != lnglat[0] && latitude.value != lnglat[1]) {
      longitude.value = lnglat[0];
      latitude.value = lnglat[1];
    }
    const index = clusterStore.MapResultPoints!.findIndex((item) => {
      if (item.lnglat[0] == lnglat[0] && item.lnglat[1] == lnglat[1]) {
        return true;
      }
    });
    if (index != -1) {
      map.setZoomAndCenter(12, lnglat);
    } else {
      ElMessage.error("该点不在地图范围内");
    }
    // 可加地图定位逻辑
  }
  //@ts-ignore
  import AMapLoader from "@amap/amap-jsapi-loader";
  import { mapIcon } from "@/utils/mapBluePoint";
  const isMaoFinished = ref<boolean>(true);
  let map: any = null;
  onMounted(() => {
    getALLMapData();
  });
  function getALLMapData() {
    AMapLoader.load({
      key: MAP_KEY, // 申请好的Web端开发者Key，首次调用 load 时必填
      version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: ["AMap.DistrictSearch"], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
    })
      .then((AMap: any) => {
        const district = new AMap.DistrictSearch({
          subdistrict: 1,
          extensions: "all",
          level: "province",
        });
        district.search("韶关市", function (_: any, result: any) {
          const bounds = result.districtList[0].boundaries;
          const mask = [];
          for (let i = 0; i < bounds.length; i++) {
            mask.push([bounds[i]]);
          }
          map = new AMap.Map("container", {
            // 设置地图容器id
            mask: mask, // 为Map实例制定掩模的路径,各图层将值显示路径范围内图像,3D模式下有效
            zoom: 9, // 设置当前显示级别
            expandZoomRange: true, // 开启显示范围设置
            zooms: [9, 18], //最小显示级别为7，最大显示级别为20
            center: [113.767587, 24.718014], // 设置地图中心点位置
            viewMode: "3D", // 特别注意,设置为3D则其他地区不显示
            zoomEnable: true, // 是否可以缩放地图
            resizeEnable: true,
          });

          for (let i = 0; i < bounds.length; i++) {
            const polyline = new AMap.Polyline({
              path: bounds[i], // polyline 路径，支持 lineString 和 MultiLineString
              strokeColor: "#3078AC", // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
              strokeWeight: 2, // 轮廓线宽度,默认为:2
              // map:map // 这种方式相当于: polyline.setMap(map);
            });
            polyline.setMap(map);
          }
          // 绑定点击事件
          if (clusterStore.MapResultPoints && !isUpdate.value) {
            mapPoints();
          } else {
            isMaoFinished.value = true;
            clusterStore.getMapResultPointsAction().then(() => {
              isMaoFinished.value = false;
              mapPoints();
            });
          }
          function mapPoints() {
            // 地图标点
            isMaoFinished.value = false;
            clusterStore.MapResultPoints!.forEach((item) => {
              // 根据是否有特殊商铺决定图标颜色
              const hasSpecialStores =
                item.specialStores && item.specialStores.length > 0;
              const oldIcon = new AMap.Icon({
                size: new AMap.Size(25, 25),
                image: hasSpecialStores ? mapIcon.red : mapIcon.orange,
                imageSize: new AMap.Size(25, 25), // 图标图片地址
              });
              const newIcon = new AMap.Icon({
                size: new AMap.Size(30, 30),
                image: mapIcon.blue,
                imageSize: new AMap.Size(30, 30), // 图标图片地址
              });
              if (item.state == "center") {
                // 当选择"仅特殊点"时，只显示有特殊商铺的打卡点
                if (specialStatus.value == "1") {
                  if (!item.specialStores || item.specialStores.length === 0) {
                    return;
                  }
                }
                // 将 Icon 实例添加到 marker 上:
                const marker = new AMap.Marker({
                  position: new AMap.LngLat(item.lnglat[0], item.lnglat[1]), //点标记的位置
                  offset: new AMap.Pixel(-7, -17), //偏移量
                  icon: oldIcon, //添加 Icon 实例
                  title: hasSpecialStores ? "特殊点" : "中心点",
                  zooms: [9, 18], //点标记显示的层级范围，超过范围不显示
                });

                // 创建信息窗口用于显示特殊商铺信息
                const infoWindow = new AMap.InfoWindow({
                  closeWhenClickMap: true, // 改回true，使用高德地图自带的关闭功能
                  offset: new AMap.Pixel(0, -30),
                  autoMove: true, // 自动调整位置避免超出地图边界
                  anchor: "bottom-center", // 设置锚点位置
                });

                // 根据是否有特殊商铺设置信息窗口内容
                if (item.specialStores && item.specialStores.length > 0) {
                  let specialStoresHtml = '<div class="special-stores-list">';
                  item.specialStores.forEach((store, index) => {
                    specialStoresHtml += `
                      <div class="special-store-item">
                        <div class="store-header">
                          <span class="store-index">${index + 1}</span>
                          <strong class="store-name">${store.storeName}</strong>
                        </div>
                        ${store.specialType ? `<div class="store-type"><span class="label">类型：</span>${store.specialType}</div>` : ""}
                        ${store.remark ? `<div class="store-remark"><span class="label">备注：</span>${store.remark}</div>` : ""}
                      </div>
                    `;
                  });
                  specialStoresHtml += "</div>";

                  infoWindow.setContent(`
                    <div class="info-card">
                      <div class="card-header">
                        <h3>🏪 特殊商铺信息</h3>
                        <span class="count-badge">${item.specialStores.length}个</span>
                      </div>
                      ${specialStoresHtml}
                    </div>
                  `);
                } else {
                  infoWindow.setContent(`
                    <div class="info-card">
                      <div class="card-header">
                        <h3>📍 打卡点</h3>
                      </div>
                      <div class="no-special-stores">
                        <p>该打卡点暂无特殊商铺</p>
                      </div>
                    </div>
                  `);
                }

                map!.add(marker);

                // 点击标记时显示信息窗口并执行其他逻辑
                marker.on("click", function () {
                  // 显示当前信息窗口
                  infoWindow.open(map, marker.getPosition());

                  longitude.value = item.lnglat[0];
                  latitude.value = item.lnglat[1];

                  // 查找区/镇/商铺
                  const result = findDistrictTownShopByLngLat(
                    item.lnglat[0],
                    item.lnglat[1]
                  );
                  if (result) {
                    activeDistricts.value = [result.district];
                    if (!activeTowns[result.district])
                      activeTowns[result.district] = [];
                    activeTowns[result.district] = [result.town];
                    setTimeout(() => {
                      const el = document.getElementById(
                        "shop-" + result.shop.accumulation
                      );
                      if (el) {
                        el.scrollIntoView({
                          behavior: "smooth",
                          block: "center",
                        });
                      }
                    }, 300);
                  }
                });

                // 鼠标悬停时显示信息窗口（可选，如果觉得干扰可以注释掉）
                // marker.on("mouseover", function (e) {
                //   infoWindow.open(map, marker.getPosition());
                // });

                // 鼠标离开时关闭信息窗口
                // marker.on("mouseout", function (e) {
                //   infoWindow.close();
                // });
                watch([longitude, latitude], (newValue) => {
                  if (
                    newValue[0] == item.lnglat[0] &&
                    newValue[1] == item.lnglat[1]
                  ) {
                    marker.setIcon(newIcon);
                  } else {
                    marker.setIcon(oldIcon);
                  }
                });
              }
            });
          }
        });
      })
      .catch((e: Error) => {
        console.log(e);
      });
  }

  onBeforeUnmount(() => {
    //销毁地图，并清空地图容器
    map!.destroy();
    //地图对象赋值为null
    map = null;
    //清除地图容器的 DOM 元素
    document.getElementById("container")!.remove(); //"container" 为指定 DOM 元素的id
  });
</script>
<style lang="scss" scoped>
  .area {
    :deep(.info-card) {
      padding: 12px;
      min-width: 180px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);

      h3 {
        margin: 0 0 8px;
        color: #1a73e8;
        font-size: 15px;
      }

      p {
        margin: 4px 0;
        color: #666;
        font-size: 13px;
      }
    }
    .el-button {
      font-size: 14px;
    }

    width: 100%;
    display: flex;

    .map {
      position: relative;
      flex-grow: 1;
      margin: 0 1.5vw;

      :deep(.amap-marker-label) {
        background-color: #3490f5;
        border: 0px;
        border-radius: 30%;
        position: relative;
      }

      // 信息窗口样式
      :deep(.amap-info-content) {
        .info-card {
          padding: 12px;
          min-width: 280px;
          max-width: 400px;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          border: 1px solid #e1e5e9;
          position: relative;
          z-index: 1000;

          .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            position: relative;

            h3 {
              margin: 0;
              color: #333;
              font-size: 14px;
              font-weight: bold;
            }

            .count-badge {
              background: #3490f5;
              color: white;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 11px;
              font-weight: bold;
            }
          }

          .no-special-stores {
            text-align: center;
            color: #999;
            font-size: 12px;
            padding: 10px 0;
          }

          .special-stores-list {
            max-height: 200px;
            overflow-y: auto;
            overflow-x: hidden; // 禁止横向滚动
            scrollbar-width: thin;
            scrollbar-color: #3490f5 #f1f1f1;

            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: #3490f5;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: #2980b9;
            }

            .special-store-item {
              background: #f8f9fa;
              padding: 8px;
              margin: 6px 0;
              border-radius: 6px;
              border-left: 3px solid #3490f5;
              transition: all 0.2s ease;
              cursor: pointer;

              &:hover {
                background: #e3f2fd;
                transform: translateX(2px);
                box-shadow: 0 2px 4px rgba(52, 144, 245, 0.2);
              }

              .store-header {
                display: flex;
                align-items: center;
                margin-bottom: 4px;

                .store-index {
                  background: #3490f5;
                  color: white;
                  width: 18px;
                  height: 18px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 10px;
                  font-weight: bold;
                  margin-right: 6px;
                  flex-shrink: 0;
                }

                .store-name {
                  color: #333;
                  font-size: 12px;
                  font-weight: bold;
                  line-height: 1.2;
                  word-break: break-all; // 允许单词换行
                  white-space: normal; // 允许换行
                }
              }

              .store-type,
              .store-remark {
                margin: 3px 0;
                font-size: 11px;
                line-height: 1.3;
                color: #666; // 统一文字颜色
                word-break: break-all; // 允许单词换行
                white-space: normal; // 允许换行

                .label {
                  color: #666;
                  font-weight: 500;
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }

      #container {
        padding: 0px;
        margin: 0px;
        width: 100%;
        height: 100%;
        margin: 0.5vh 0;
      }

      .btn-box {
        p {
          font-size: 20px;
          color: #bce4ff;
          margin-right: 10px;
        }

        .select {
          width: 10vw;
          margin-right: 1vw;
        }

        display: flex;
        align-items: center;
        width: 100%;
        justify-content: center;
      }
    }

    .aside {
      width: 20vw;
      transition: width 0.3s ease;
    }

    .expanded {
      width: 3vw;
    }

    .dv-border-box-9 {
      height: 80vh;
      width: 20vw;
      box-shadow: 10px 10px 5px 5px rgb(0, 0, 0, 0.4);

      .arrow {
        position: absolute;
        right: -8%;
        top: 26%;
        width: 2.5vw;
        height: 6vh;
        background-color: #001731;
        cursor: pointer;
      }

      .areaCollapse {
        margin-left: 1vw;
        padding-top: 2vh;
        width: 85%;

        .el-collapse {
          .el-collapse-item {
            ::v-deep(.el-collapse-item__header::before) {
              content: "";
              display: inline-block;
              width: 15px;
              height: 15px;
              background-color: #e0c340;
              border-radius: 50%;
              margin-right: 10px;
            }

            ::v-deep(.el-collapse-item__header::after) {
              margin-left: 10px;
              margin-right: -15px;
              width: 33px;
              height: 33px;
              background: #e0c340;
              transform: rotate(45deg);
              content: "";
              display: inline-block;
            }
          }
        }

        .item {
          position: absolute;
          z-index: 10;
          margin-top: 20px;
          margin-left: 16.4vw;
        }

        li {
          list-style: disc;
          padding: 5px 10px;
        }

        li:before {
          content: "";
          display: inline-block;
          width: 8px;
          height: 8px;
          background-color: #b5a55f;
          border-radius: 50%;
          margin-bottom: 4px;
          margin-right: 10px;
        }
      }

      .region {
        margin-left: 1.4vw;
        margin-top: 3vh;
        width: 85%;

        .el-collapse {
          --el-collapse-header-bg-color: #97c7e7;
          --el-collapse-header-text-color: #353777;
          --el-collapse-header-font-size: 18px;
          --el-collapse-content-bg-color: #001731;
          --el-collapse-content-font-size: 18px;
          --el-collapse-content-text-color: #a4c5e5;
          --el-collapse-border-color: #001731;
        }

        .el-collapse-item {
          padding-left: 30px;
          position: relative;

          .active {
            background-color: rgb(2, 119, 168);
            padding: 0 0.5vw;
          }

          ::v-deep(.el-collapse-item__content) {
            padding: 0;
          }

          .regionCollapseItemContext {
            margin: 10px 0;
            cursor: pointer;
          }

          .regionCollapseItemContext:before {
            content: "";
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #90ade8;
            border-radius: 50%;
            margin-bottom: 4px;
            margin-right: 10px;
          }
        }

        .el-collapse-item:before {
          position: absolute;
          left: 5px;
          top: 0;
          border-right: 25px solid #97c7e7;
          border-top: 24px solid transparent;
          border-bottom: 24px solid transparent;
          content: "";
          width: 0;
          height: 0;
        }
      }

      .btn {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 8%;
      }
    }
  }
</style>
