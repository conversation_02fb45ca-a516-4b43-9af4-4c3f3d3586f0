package com.ict.ycwl.clustercalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.clustercalculate.pojo.Accumulation;
import com.ict.ycwl.clustercalculate.pojo.LngAndLat;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface AccumulationMapper extends BaseMapper<Accumulation> {

    /**
     * 根据经纬度查询聚集区id
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 返回id
     */
    Long selectIdByLonAndLat(double longitude, double latitude);

    /**
     * 查询是否有数据
     *
     * @param keyword 大区名
     * @return 数据量
     */
    int selectAll(String keyword);

    /**
     * 将以前的聚集区数据软删除
     *
     * @param keyword 大区名
     */
    void updateIsDelete(String keyword);

    /**
     * 查询该大区的簇心数组
     *
     * @param keyword 大区名
     * @return 该大区的簇心数组
     */
    List<LngAndLat> selectAllByAreaName(String keyword);

    int myDelete(String areaName);
}
