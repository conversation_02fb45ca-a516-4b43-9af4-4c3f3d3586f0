<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.clustercalculate.mapper.RouteMapper">

    <update id="MyUpdateWorkTime" >
        UPDATE route
        SET work_time = CAST(work_time AS DECIMAL(10,2)) + #{time}
        WHERE route_id = #{routeId}
    </update>

    <select id="MyUpdateWorkTimeJ" resultType="int">
        UPDATE route
        SET work_time = CAST(work_time AS DECIMAL(10,2)) + #{time}
        WHERE route_id = #{routeId}
    </select>
</mapper>