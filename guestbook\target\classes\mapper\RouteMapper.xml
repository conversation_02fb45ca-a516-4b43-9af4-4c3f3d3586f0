<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.RouteMapper">

    <select id="selectAllForCondition" resultType="com.ict.ycwl.guestbook.api.vo.ConditionRouteVo">
        SELECT route_id, route_name
        FROM route
    </select>

</mapper>