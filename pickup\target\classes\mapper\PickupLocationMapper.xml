<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pickup.mapper.PickupLocationMapper">

    <select id="checkPickupName" resultType="int">
        select COUNT(*) from site_selection where pickup_name=#{pickupName}
    </select>

    <select id="selectByName" resultType="com.ict.ycwl.pickup.pojo.entity.SiteSelection">
        select * from site_selection where pickup_name=#{pickupContainers}
    </select>

    <select id="queryStatusById" resultType="int">
        select COUNT(*) from site_store where site_selection_id=#{id}
    </select>

    <select id="checkPickupAddress" resultType="int">
        select COUNT(*) from site_selection where pickup_address=#{pickupAddress}
    </select>

    <select id="selectCountByLongAndLati" resultType="int">
        select COUNT(*) from site_selection where longitude=#{longitude} and latitude=#{latitude}
    </select>
</mapper>