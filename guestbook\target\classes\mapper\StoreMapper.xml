<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.StoreMapper">
    
    <sql id="selectSingleColumn">
        contact_name,customer_manager_id,customer_manager_name,area_id,area_name,route_id,route_name
    </sql>

    <select id="selectAllForCondition" resultType="com.ict.ycwl.guestbook.api.vo.ConditionStoreVo">
        SELECT customer_code, contact_name
        FROM store
        WHERE `status` = '1'
    </select>

    <select id="selectSingleForCondition" parameterType="String" resultType="Store">
        SELECT <include refid="selectSingleColumn"></include>
        FROM store WHERE customer_code = #{customerCode}
    </select>

</mapper>