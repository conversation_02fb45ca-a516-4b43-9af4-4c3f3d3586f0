package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.pojo.WorkParameter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工作参数服务测试类
 * 
 * <AUTHOR>
 * @since 2024-08-14
 */
@Slf4j
@SpringBootTest
public class WorkParameterServiceTest {

    @Autowired
    private WorkParameterService workParameterService;

    @Test
    public void testGetAllParameters() {
        log.info("=== 测试获取所有工作参数配置 ===");
        List<WorkParameter> parameters = workParameterService.getAllParameters();
        log.info("获取到 {} 条工作参数配置", parameters.size());
        
        for (WorkParameter parameter : parameters) {
            log.info("参数配置: {} - 最大工作时间: {}h, 最优工作时间: {}h, 灵活范围: {}h, 停止阈值: {}", 
                parameter.getName(), 
                parameter.getMaxWorkHours(), 
                parameter.getOptimalWorkHours(),
                parameter.getFlexibleRange(),
                parameter.getStopThreshold());
        }
    }

    @Test
    public void testGetParameterByName() {
        log.info("=== 测试根据站点名称获取工作参数 ===");
        
        // 测试获取韶关市（全局参数）
        WorkParameter globalParam = workParameterService.getParameterByName("韶关市");
        log.info("韶关市参数: {}", globalParam);
        
        // 测试获取班组一物流配送中心
        WorkParameter teamParam = workParameterService.getParameterByName("班组一物流配送中心");
        log.info("班组一物流配送中心参数: {}", teamParam);
        
        // 测试获取不存在的站点（应该返回全局参数）
        WorkParameter notExistParam = workParameterService.getParameterByName("不存在的站点");
        log.info("不存在站点的参数（应该是全局参数）: {}", notExistParam);
    }

    @Test
    public void testUpdateParameter() {
        log.info("=== 测试更新工作参数 ===");
        
        // 获取韶关市参数
        WorkParameter parameter = workParameterService.getParameterByName("韶关市");
        log.info("更新前: {}", parameter);
        
        // 修改参数
        parameter.setMaxWorkHours(new BigDecimal("8.50"));
        parameter.setOptimalWorkHours(new BigDecimal("7.00"));
        parameter.setFlexibleRange(new BigDecimal("0.60"));
        parameter.setStopThreshold(new BigDecimal("0.90"));
        
        // 更新
        boolean success = workParameterService.updateParameter(parameter);
        log.info("更新结果: {}", success);
        
        // 验证更新结果
        WorkParameter updatedParameter = workParameterService.getParameterByName("韶关市");
        log.info("更新后: {}", updatedParameter);
    }

    @Test
    public void testGetGlobalParameter() {
        log.info("=== 测试获取全局参数 ===");
        WorkParameter globalParameter = workParameterService.getGlobalParameter();
        log.info("全局参数: {}", globalParameter);
    }
}
