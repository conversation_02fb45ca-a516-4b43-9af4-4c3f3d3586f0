# 工作参数配置功能说明

## 功能概述

本功能为粤北卷烟物流管理系统的path-calculate模块新增了工作参数配置管理功能，支持对不同站点的工作参数进行个性化配置和管理。

## 数据库设计

### 表结构：work_parameter

```sql
CREATE TABLE IF NOT EXISTS `work_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '站点名称（对应team表的transit_depot_name，韶关市为全局参数）',
  `max_work_hours` decimal(5,2) NOT NULL DEFAULT 8.00 COMMENT '最大工作时间（小时）',
  `flexible_range` decimal(5,2) NOT NULL DEFAULT 0.50 COMMENT '灵活范围（小时）',
  `optimal_work_hours` decimal(5,2) NOT NULL DEFAULT 6.50 COMMENT '最优工作时间（小时）',
  `stop_threshold` decimal(5,2) NOT NULL DEFAULT 0.85 COMMENT '停止阈值',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除（0：未删除，1：已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`) COMMENT '站点名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作参数配置表';
```

### 初始数据

系统预置了7条记录，包括：
- 韶关市（全局参数）
- 新丰县中转站
- 坪石镇中转站
- 翁源县中转站
- 马市烟叶工作站
- 班组一物流配送中心
- 班组二物流配送中心

## API接口

### 1. 获取所有工作参数配置
- **接口路径**: `GET /path/workParameter/list`
- **功能**: 获取所有工作参数配置列表
- **返回**: 工作参数配置列表

### 2. 根据站点名称获取工作参数
- **接口路径**: `GET /path/workParameter/getByName`
- **参数**: `name` - 站点名称
- **功能**: 根据站点名称获取对应的工作参数，如果不存在则返回全局参数
- **返回**: 工作参数配置对象

### 3. 更新工作参数
- **接口路径**: `POST /path/workParameter/update`
- **参数**: WorkParameter对象（JSON格式）
- **功能**: 更新单个工作参数配置
- **返回**: 操作结果

### 4. 批量更新工作参数
- **接口路径**: `POST /path/workParameter/batchUpdate`
- **参数**: WorkParameter对象列表（JSON格式）
- **功能**: 批量更新多个工作参数配置
- **返回**: 操作结果

### 5. 获取全局工作参数
- **接口路径**: `GET /path/workParameter/global`
- **功能**: 获取全局工作参数（韶关市）
- **返回**: 全局工作参数配置

## 独立的工作参数管理接口

除了在PathController中集成的接口外，还提供了独立的WorkParameterController：

- **基础路径**: `/workParameter`
- **功能**: 提供完整的工作参数CRUD操作
- **接口列表**:
  - `GET /workParameter/list` - 获取所有参数
  - `GET /workParameter/getByName` - 根据名称获取参数
  - `POST /workParameter/update` - 更新参数
  - `POST /workParameter/batchUpdate` - 批量更新参数
  - `GET /workParameter/global` - 获取全局参数

## 数据模型

### WorkParameter实体类

```java
public class WorkParameter {
    private Long id;                    // 主键ID
    private String name;                // 站点名称
    private BigDecimal maxWorkHours;    // 最大工作时间（小时）
    private BigDecimal flexibleRange;   // 灵活范围（小时）
    private BigDecimal optimalWorkHours; // 最优工作时间（小时）
    private BigDecimal stopThreshold;   // 停止阈值
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
    private Integer isDeleted;          // 是否删除
}
```

## 使用示例

### 1. 获取所有工作参数
```bash
curl -X GET "http://localhost:8080/path/workParameter/list"
```

### 2. 获取特定站点参数
```bash
curl -X GET "http://localhost:8080/path/workParameter/getByName?name=韶关市"
```

### 3. 更新工作参数
```bash
curl -X POST "http://localhost:8080/path/workParameter/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "韶关市",
    "maxWorkHours": 8.50,
    "flexibleRange": 0.60,
    "optimalWorkHours": 7.00,
    "stopThreshold": 0.90
  }'
```

## 测试

运行测试类验证功能：
```bash
mvn test -Dtest=WorkParameterServiceTest
```

## 注意事项

1. **站点名称唯一性**: 每个站点名称在系统中是唯一的
2. **全局参数**: "韶关市"作为全局参数，当特定站点没有配置时会使用全局参数
3. **数据验证**: 所有数值参数都有合理的默认值和验证
4. **软删除**: 使用逻辑删除，不会物理删除数据
5. **事务支持**: 更新操作支持事务回滚

## 文件结构

```
path-calculate/
├── src/main/java/com/ict/ycwl/pathcalculate/
│   ├── pojo/WorkParameter.java                    # 实体类
│   ├── mapper/WorkParameterMapper.java            # Mapper接口
│   ├── service/WorkParameterService.java          # 服务接口
│   ├── service/Impl/WorkParameterServiceImpl.java # 服务实现
│   └── controller/WorkParameterController.java    # 控制器
├── src/main/resources/
│   ├── sql/create_work_parameter_table.sql        # 建表SQL
│   └── docs/工作参数配置功能说明.md                # 功能说明文档
└── src/test/java/com/ict/ycwl/pathcalculate/
    └── service/WorkParameterServiceTest.java      # 测试类
```
