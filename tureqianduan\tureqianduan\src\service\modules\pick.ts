import requests from "../index";
import type {
  IPickupUserCalculateData,
  IPickupUserGetListData,
  IPickupUserUpdateData,
  IPickupLogImport,
  IFromDownload,
  ILogDownload,
  IDeleteLog,
  IUnassignedList,
} from "@/types/pickup";
import { IRequest } from "../request/type";

//导出取货户信息
export function exportPickup() {
  return requests.get<IRequest<any>>({
    timeout: 180000,
    url: "/pickup/pickupUser/pickupUserExport",
    responseType: "blob",
  });
}

//获取路线信息
export function getRouteIf(id: number) {
  return requests.get<IRequest<any>>({
    url: "/pickup/pickupUser/detail/" + id,
  });
}

//导入取货户信息
export function importPickup(formData: FormData, config: any) {
  return requests.post<IRequest<any>>({
    url: "/pickup/pickupUser/pickupUserImport",
    data: formData,
    headers: {
      accept: "*/*",
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: config.onUploadProgress,
    signal: config.signal,
  });
}

//导入日志
export function importPickupLog(params: any) {
  return requests.get<IRequest<IPickupLogImport>>({
    url: "/datamanagement/getImportLogs",
    params,
  });
}

//下载表格
export function downloadTemplate(params: IFromDownload) {
  return requests.post<IRequest<IFromDownload>>({
    url: "/pickup/pickupUser/downloadNullFrom",
    responseType: "blob",
    params,
  });
}

//下载日志
export function downloadLog(params: ILogDownload) {
  return requests.get<IRequest<ILogDownload>>({
    url: "/datamanagement/downloadLogs",
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  });
}

//获取取货户列表
export function getPickupList(params: IPickupUserGetListData) {
  return requests.get<IRequest<IPickupUserGetListData>>({
    url: "/pickup/pickupUser/pickupUserList",
    params,
  });
}

//修改取货户信息
export function updatePickup(params: IPickupUserUpdateData) {
  return requests.patch<IRequest<IPickupUserUpdateData>>({
    url: "/pickup/pickupUser/pickupUserUpdate",
    params,
  });
}

//计算取货户
export function calculatePickup(params: IPickupUserCalculateData) {
  return requests.get<IRequest<IPickupUserCalculateData>>({
    url: "/pickup/pickupUser/pickupUserRecalculate",
    params,
    timeout: 180000,
  });
}

//删除日志
export function deleteLog(params: IDeleteLog) {
  return requests.delete<IRequest<IDeleteLog>>({
    url: "/datamanagement/deleteImportLogs",
    params,
  });
}

//获取下拉框
export function getSelect() {
  return requests.get<IRequest<any>>({
    url: "/pickup/pickupUser/searchDownBox",
  });
}

//待分配
export function unassignedList(params: IUnassignedList) {
  return requests.patch<IRequest<IUnassignedList>>({
    url: "/pickup/pickupUser/toBeAssigned",
    headers: {
      "Content-Type": "application/json", // 确保设置正确的 Content-Type
    },
    params,
    paramsSerializer: (params) => new URLSearchParams(params).toString(),
  });
}

//地图姐构
export function getMapData() {
  return requests.post<IRequest<any>>({
    url: "/pickup/pickupUser/mapMarkers",
    timeout: 0,
  });
}

//参数列表
export function getParamsList() {
  return requests.get<IRequest<any>>({
    url: "/pickup/pickupUser/parameters",
  });
}

//参数更新
export function updateParams(params: any) {
  return requests.get<IRequest<any>>({
    url: "/pickup/pickupUser/updateParameters",
    params,
  });
}
