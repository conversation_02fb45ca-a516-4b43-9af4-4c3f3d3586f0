<template>
  <div class="changeAreaDialog">
    <el-dialog v-model="changeAreaOpen" title="修改班组信息" width="60%" :close-on-click-modal="false" @close="closeChange">
      <el-form label-width="auto" class="areaForm" :model="formData" ref="formRef">
        <el-form-item label="班组">
          <div class="areaItem"> {{ props.teamName }}</div>
        </el-form-item>
        <el-form-item label="配送域" prop="deliveryList">
          <el-select
            style="width: 440px"
            multiple
            placeholder="请选择"
            v-model="formData.deliveryList"
            @change="changeDelivery"
          >
          <el-option label="无" value="无" />
          <el-option v-for="item in deliveryStore.selectList.deliveryList" :label="item" :value="item" :key="item + '2'"/>
          </el-select>
        </el-form-item>
        <el-form-item label="中转站" prop="transitDepotFromData">
          <div class="flex">
            <el-tag type="primary" v-for="(item) in formData.transitDepotFromData" :key="item + '2'">{{ item }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="车辆总数" prop="carSum">
          <div class="areaItem"> {{ formData.carSum }}</div>
        </el-form-item>
        <el-form-item label="路径总数" prop="routeSum">
          <div class="areaItem">
            {{ formData.routeSum }}
          </div>
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="closeChange">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmChange"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
  <el-dialog
    v-model="confirmDialogVis"
    title="确认变更"
    class="transform"
    width="40%"
    align-center
    :close-on-click-modal="false"
  >
    <div class="content">{{transformText}}</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog" type="primary">取消</el-button>
        <el-button type="primary" @click="confirmDialog">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useAreaStore } from "@/store/managerment/area";
import { useDeliveryStore } from '@/store/delivery';
const areaStore = useAreaStore()
const deliveryStore = useDeliveryStore()
const formRef = ref<any>()
const changeAreaOpen = ref(false);
defineExpose({
  changeAreaOpen,
});

const props = defineProps({
  teamName: String,
  teamId: Number,
  info: Object
})

//弹窗的可视化
const confirmDialogVis = ref<boolean>(false)

  const formData = ref<any>({
  deliveryList: [],
  carSum: 0,
  routeSum: 0,
  transitDepotFromData: []
  })

//页面更新时
onUpdated(() => {
  formData.value = {
  deliveryList: props.info.deliveryAreaName.split(','),
  carSum: props.info.carSum,
  routeSum: props.info.routeSum,
  transitDepotFromData: props.info.transitDepotName ? props.info.transitDepotName.split(',') : []
  }
})

//仓库配送域数据列表
const addEmit = defineEmits(["confirmChange"]);
const transformDialogTotalData = ref<any>({})

async function opentransformDiaLog() {
  changeAreaOpen.value = false
  transformDialogTotalData.value = await getTeamChangeDataTransfROM()
  const {deliveryList,carSum, routeSum, teamName} = transformDialogTotalData.value;
  const data : any = {
    deliveryList,carSum, routeSum, teamName
  }
  areaStore.queryAreaAddData(data).then((res) => {
    transformText.value = res;
  })
  confirmDialogVis.value = true
}

//确定修改班组
async function confirmChange() {
  if(formData.value.deliveryList.length < 1) {
    ElMessage.error('未修改数据')
    changeAreaOpen.value = false;
    return;
  }
  opentransformDiaLog()
}

//取消修改班组
const closeChange = () => {
  formRef.value.resetFields()
  changeAreaOpen.value = false
};

const transformText = ref<string>('')

// 确认变更的信息
async function confirmDialog() {
  confirmDialogVis.value = false;
  formRef.value.resetFields()
  areaStore.updateAreaData(transformDialogTotalData.value).then((res) => {
    if(res.code === 50001 || res.message === '系统异常') {
      ElMessage.error('系统异常!')
      return
    } 
    addEmit("confirmChange")
  })
}

//取消变更
function cancelDialog() {
  formRef.value.resetFields()
  confirmDialogVis.value = false;
}

const teamInfo = computed(() => areaStore.addTeamInfo)

function changeDelivery() {
  if(formData.value.deliveryList.includes("无")) {
    formData.value.deliveryList = ["无"]
    formData.value.carSum = 0
    formData.value.routeSum = 0
    formData.value.transitDepotFromData = []
    return
  }
  areaStore.getAddTeamInfo().then(() => {
    formData.value.transitDepotFromData = []
    const deliveryArray = 
    teamInfo.value.deliveryList.filter((item : any) => 
    formData.value.deliveryList.includes(item.deliveryName)
  );
  deliveryArray.forEach((item : any) => {
    if(!formData.value.transitDepotFromData.includes(item.transitDepotInfo.transitDepotName)) {
      if(item.transitDepotInfo.transitDepotName === '') {
        formData.value.transitDepotFromData.push('无')
      } else {
        formData.value.transitDepotFromData.push(item.transitDepotInfo.transitDepotName)
      }
    }
  })
    formData.value.carSum = deliveryArray.reduce((accumulator : number, currentItem : any) => {
    return accumulator + currentItem.carNumber; // 累加当前值
    }, 0); // 初始值为 0

    formData.value.routeSum = deliveryArray.reduce((accumulator : number, currentItem : any) => {
    return accumulator + currentItem.routeNumber; // 累加当前值
  }, 0); // 初始值为 0
  })
}

//获取表格信息
async function getTeamChangeDataTransfROM() {
  if(formData.value.deliveryList.includes("无") || formData.value.deliveryList.length < 1) {
      formData.value.carSum = 0
      formData.value.routeSum = 0
      return Promise.resolve({
          deliveryList: [],
          carSum: formData.value.carSum,
          routeSum: formData.value.routeSum,
          teamName: props.teamName,
          teamId: props.teamId
        })
    }
  const deliveryArray = 
    teamInfo.value.deliveryList.filter((item : any) => 
    formData.value.deliveryList.includes(item.deliveryName)
  );

  return Promise.resolve({
    deliveryList: deliveryArray,
    carSum: formData.value.carSum,
    routeSum: formData.value.routeSum,
    teamName: props.teamName,
    teamId: props.teamId
  })
}

</script>

<style lang="less" scoped>
.areaForm {
  margin-left: 100px;

  .areaItem {
    font-size: 20px;
  }
}
.btns {
  display: flex;
  justify-content: center;
  color: black;
}

.transform {
  .content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    font-size: 20px;
  }
}

.flex {
  display: flex;
  flex-wrap: wrap;
}

</style>
