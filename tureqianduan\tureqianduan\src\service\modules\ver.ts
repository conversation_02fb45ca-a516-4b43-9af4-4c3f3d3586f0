import requests from "../index";
import { IRequest } from "../request/type";

//得到版本list
export function getVerList() {
  return requests.get<IRequest<any>>({
    url: "/pathcalculate/dynamicDatasource/versionList",
  });
}

//版本删除
export function delVer(id: string) {
  return requests.delete<IRequest<any>>({
    url: `/pathcalculate/dynamicDatasource/deleteVersion/${id}`,
  });
}

//版本切换
export function changeVer(version: any) {
  return requests.get<IRequest<any>>({
    url: `/pathcalculate/dynamicDatasource/enable/version/${version}`,
  });
}

//获取路线
export function getRoute(params: any) {
  return requests.get<IRequest<any>>({
    url: "/pathcalculate/dynamicDatasource/getRouteById",
    params,
  });
}

//切换数据源
export function changeSource(dbName: string) {
  return requests.get<IRequest<any>>({
    url: `/pathcalculate/dynamicDatasource/handoff/${dbName}`,
  });
}

//版本信息更新
export function updateVer(data: any) {
  return requests.put<IRequest<any>>({
    url: "/pathcalculate/dynamicDatasource/saveNewVersion",
    data,
  });
}

//当前版本
export function getCurrentVer() {
  return requests.get<IRequest<any>>({
    url: "/pathcalculate/dynamicDatasource/getCurrVersion",
  });
}

//版本另存
export function saveNewVersion(params: any) {
  return requests.post<IRequest<any>>({
    url: "/pathcalculate/dynamicDatasource/SaveNewVersion",
    params,
  });
}
