-- 历史版本功能数据库初始化脚本
-- 用于修复历史版本功能无法使用的问题

-- =============================================
-- 1. 初始化 ycwl_slave1 数据库
-- =============================================
USE ycwl_slave1;

-- 创建版本表（如果不存在）
CREATE TABLE IF NOT EXISTS `version` (
  `version_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `version_name` varchar(100) NOT NULL COMMENT '版本名称',
  `version_db` varchar(50) NOT NULL COMMENT '版本对应的数据库名',
  `version_info` text COMMENT '版本描述信息',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_show` tinyint(1) DEFAULT 1 COMMENT '是否显示：0-隐藏，1-显示',
  PRIMARY KEY (`version_id`),
  UNIQUE KEY `uk_version_db` (`version_db`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本管理表';

-- 复制主数据库的表结构到slave1（如果表不存在）
-- 这里列出主要的业务表，您可以根据实际需要调整

-- 路线表
CREATE TABLE IF NOT EXISTS `route` LIKE ycdb.route;
-- 聚集区表  
CREATE TABLE IF NOT EXISTS `accumulation` LIKE ycdb.accumulation;
-- 商铺表
CREATE TABLE IF NOT EXISTS `store` LIKE ycdb.store;
-- 中转站表
CREATE TABLE IF NOT EXISTS `transit_depot` LIKE ycdb.transit_depot;
-- 班组表
CREATE TABLE IF NOT EXISTS `team` LIKE ycdb.team;
-- 路线详情表
CREATE TABLE IF NOT EXISTS `route_detail` LIKE ycdb.route_detail;
-- 系统参数表
CREATE TABLE IF NOT EXISTS `system_parameter` LIKE ycdb.system_parameter;
-- 商铺时间表
CREATE TABLE IF NOT EXISTS `store_time` LIKE ycdb.store_time;

-- =============================================
-- 2. 初始化 ycwl_slave2 数据库
-- =============================================
USE ycwl_slave2;

-- 创建版本表（如果不存在）
CREATE TABLE IF NOT EXISTS `version` (
  `version_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `version_name` varchar(100) NOT NULL COMMENT '版本名称',
  `version_db` varchar(50) NOT NULL COMMENT '版本对应的数据库名',
  `version_info` text COMMENT '版本描述信息',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_show` tinyint(1) DEFAULT 1 COMMENT '是否显示：0-隐藏，1-显示',
  PRIMARY KEY (`version_id`),
  UNIQUE KEY `uk_version_db` (`version_db`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本管理表';

-- 复制主要业务表结构
CREATE TABLE IF NOT EXISTS `route` LIKE ycdb.route;
CREATE TABLE IF NOT EXISTS `accumulation` LIKE ycdb.accumulation;
CREATE TABLE IF NOT EXISTS `store` LIKE ycdb.store;
CREATE TABLE IF NOT EXISTS `transit_depot` LIKE ycdb.transit_depot;
CREATE TABLE IF NOT EXISTS `team` LIKE ycdb.team;
CREATE TABLE IF NOT EXISTS `route_detail` LIKE ycdb.route_detail;
CREATE TABLE IF NOT EXISTS `system_parameter` LIKE ycdb.system_parameter;
CREATE TABLE IF NOT EXISTS `store_time` LIKE ycdb.store_time;

-- =============================================
-- 3. 初始化 ycwl_slave3 数据库
-- =============================================
USE ycwl_slave3;

-- 创建版本表（如果不存在）
CREATE TABLE IF NOT EXISTS `version` (
  `version_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `version_name` varchar(100) NOT NULL COMMENT '版本名称',
  `version_db` varchar(50) NOT NULL COMMENT '版本对应的数据库名',
  `version_info` text COMMENT '版本描述信息',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_show` tinyint(1) DEFAULT 1 COMMENT '是否显示：0-隐藏，1-显示',
  PRIMARY KEY (`version_id`),
  UNIQUE KEY `uk_version_db` (`version_db`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本管理表';

-- 复制主要业务表结构
CREATE TABLE IF NOT EXISTS `route` LIKE ycdb.route;
CREATE TABLE IF NOT EXISTS `accumulation` LIKE ycdb.accumulation;
CREATE TABLE IF NOT EXISTS `store` LIKE ycdb.store;
CREATE TABLE IF NOT EXISTS `transit_depot` LIKE ycdb.transit_depot;
CREATE TABLE IF NOT EXISTS `team` LIKE ycdb.team;
CREATE TABLE IF NOT EXISTS `route_detail` LIKE ycdb.route_detail;
CREATE TABLE IF NOT EXISTS `system_parameter` LIKE ycdb.system_parameter;
CREATE TABLE IF NOT EXISTS `store_time` LIKE ycdb.store_time;

-- =============================================
-- 4. 初始化主数据库的版本数据
-- =============================================
USE ycdb;

-- 确保主数据库有版本表
CREATE TABLE IF NOT EXISTS `version` (
  `version_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `version_name` varchar(100) NOT NULL COMMENT '版本名称',
  `version_db` varchar(50) NOT NULL COMMENT '版本对应的数据库名',
  `version_info` text COMMENT '版本描述信息',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_show` tinyint(1) DEFAULT 1 COMMENT '是否显示：0-隐藏，1-显示',
  PRIMARY KEY (`version_id`),
  UNIQUE KEY `uk_version_db` (`version_db`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本管理表';

-- 插入初始版本数据（如果不存在）
INSERT IGNORE INTO `version` (`version_id`, `version_name`, `version_db`, `version_info`, `is_show`) VALUES
(1, '主版', 'master', '系统主版本，包含当前生产数据', 1),
(2, '版本1', 'slave1', '历史版本1，用于备份和测试', 0),
(3, '版本2', 'slave2', '历史版本2，用于备份和测试', 0),
(4, '版本3', 'slave3', '历史版本3，用于备份和测试', 0);

-- =============================================
-- 5. 验证数据完整性
-- =============================================

-- 检查版本表数据
SELECT '主数据库版本数据:' as info;
SELECT version_id, version_name, version_db, is_show FROM ycdb.version;

-- 检查slave数据库表是否创建成功
SELECT '检查slave1数据库表:' as info;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ycwl_slave1';

SELECT '检查slave2数据库表:' as info;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ycwl_slave2';

SELECT '检查slave3数据库表:' as info;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ycwl_slave3';

-- 完成提示
SELECT '数据库初始化完成！' as status, 
       '请检查masterDatasource.txt文件内容是否为master' as next_step;
