<template>
  <div class="changeDeliveryDialog">
    <el-dialog v-model="changeDeliveryOpen" title="修改信息" width="50%" @close="closeChange">
      <el-form label-width="auto" class="areaForm" :model="totalFields" ref="formRef" :rules="rules">
        <div class="flex">
          <el-form-item label="配送域名称">
          <div style="width: 200px;">
            {{ props.deliveryInfo.deliverName }}
          </div>
        </el-form-item>
        <el-form-item label="所属班组" prop="teamName">
          <el-select
            style="width: 200px"
            placeholder="请选择"
            v-model="totalFields.teamName"
            @change="getTrans"
          >
           <el-option label="无" value="无"/>
            <el-option :label="item" :value="item" v-for="(item) in Object.keys(store.info.teamAndTransitDepotMap)" :key="item + '9'"/>
          </el-select>
        </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="所属行政区" prop="areaName">
          <el-select
            style="width: 200px"
            placeholder="请选择"
            v-model="totalFields.areaName"
          >
          <el-option :label="item" :value="item" v-for="(item) in store.info.area" :key="item + '9'" />
          </el-select>
        </el-form-item>
        <el-form-item label="对接中转站" style="margin-left: 20px;" prop="transitDepotName">
          <el-select
            style="width: 180px"
            placeholder="请选择"
            v-model="totalFields.transitDepotName"
            @change="changeTeamName"
          >
          <el-option :label="item" :value="item" v-for="(item) in trans" :key="item + '9'" />
          </el-select>
        </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="配送类型" prop="deliveryType">
          <el-select
            style="width: 200px"
            multiple
            placeholder="请选择"
            v-model="totalFields.deliveryType"
          >
          <el-option :label="item" :value="item" v-for="(item) in store.info.deliveryType" :key="item + '9'" />
          </el-select>
        </el-form-item>
        <el-form-item label="路径数" prop="routeNumber">
          <div class="areaItem">
            <el-input-number :min="0" v-model="totalFields.routeNumber" />
          </div>
        </el-form-item>
        </div>
        <el-row class="rowtest" >
        <div style="margin-top: 4px; cursor: pointer; width: 16px; height: max-content; position: absolute; z-index: 2;" @click="dialogPath = true">
          <img
              src="../../../../../assets/images/空心问号.png"
              alt=""
              width="16px"
              height="16px"
              @click="dialogPath = true"
          />
        </div>
        <el-form-item label="车辆数" prop="carNumber" style="margin-left: -10px;">
          <div class="areaItem">
            {{ totalFields.carNumber }}
          </div>
        </el-form-item>
        </el-row>
        <!-- <el-form-item label="车辆数" prop="carNumber">
          <div class="areaItem">
            {{ totalFields.carNumber }}
          </div>
        </el-form-item> -->
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="closeChange">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmChange"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      v-model="dialogPath"
      title="提醒"
      width="500"
    >
      <span>请到车辆基本信息页面配置</span>
      <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="dialogPath = false">
          确定
        </el-button>
      </div>
    </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useDeliveryStore } from "@/store/delivery";
const store = useDeliveryStore()

const dialogPath = ref<boolean>(false)
const changeDeliveryOpen = ref(false);
const props = defineProps({
  deliveryInfo: Object
})
defineExpose({
  changeDeliveryOpen,
});

onUpdated(() => {
  totalFields.value.areaName = props.deliveryInfo.area
  totalFields.value.carNumber = props.deliveryInfo.carNumber
  totalFields.value.deliveryType = props.deliveryInfo.deliveryAreaType.split(',')
  totalFields.value.routeNumber = props.deliveryInfo.routeNumber
  totalFields.value.teamName = props.deliveryInfo.teamName 
  totalFields.value.transitDepotName = props.deliveryInfo.transitDepotName
  if(!props.deliveryInfo.teamName) {
    totalFields.value.teamName = '无'
    totalFields.value.transitDepotName = '无'
    trans.value = []
  } else {
    trans.value = store.info.teamAndTransitDepotMap[totalFields.value.teamName]
  }
})

const addEmit = defineEmits(["confirmChange"]);
const formRef = ref<any>()
const totalFields = ref<any>({
  areaName: '',
  carNumber: 0,
  deliveryType: [],
  routeNumber: 0,
  teamName: '',
  transitDepotName: ''
})

const rules = reactive({
})

async function confirmChange() {
  if (!formRef.value) return
    await formRef.value.validate((valid : any) => {
    if (valid) {
      getDeliveryType()
      if(totalFields.value.teamName === '无') {
        totalFields.value.teamName = ''
        totalFields.value.transitDepotName = ''
      }
      const data = {
        ...totalFields.value,
        deliveryName: props.deliveryInfo.deliveryName,
        deliveryId: Number(props.deliveryInfo.deliveryId)
      }
      addEmit("confirmChange", data)
      formRef.value.resetFields()
      trans.value = []
      changeDeliveryOpen.value = false
    } else {
      ElMessage({
      message: '修改失败',
      type: 'warning'
      })
    }
  })
}

function getRouteNumber () {
  totalFields.value.routeNumber = totalFields.value.carNumber * 5
}

function getDeliveryType() {
  totalFields.value.deliveryType = totalFields.value.deliveryType.join(',')
}

const trans = ref<string[]>([])

function getTrans() {
  const teamName : string = totalFields.value.teamName
  if(teamName === '无') {
    totalFields.value.transitDepotName = '无'
    trans.value = []
    return
  }
  totalFields.value.transitDepotName = ''
  trans.value = store.info.teamAndTransitDepotMap[teamName]
}

function closeChange() {
  trans.value = []
  formRef.value.resetFields()
  changeDeliveryOpen.value = false
}

function changeTeamName() {
 Object.keys(store.info.teamAndTransitDepotMap).forEach((key: any) => {
    if(store.info.teamAndTransitDepotMap[key].includes(totalFields.value.transitDepotName)) {
      totalFields.value.teamName = key;
      return;
    }
  })
}

</script>

<style lang="less" scoped>

.flex {
  display: flex;
}

.addArea {
  color: black;
}
.areaForm {
  margin-left: 100px;

  .areaItem {
    font-size: 20px;
  }
}
.btns {
  display: flex;
  justify-content: center;
  color: black;
}
</style>
