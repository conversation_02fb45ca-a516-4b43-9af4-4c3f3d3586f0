# 路径规划算法工具类接口文档

## 🎯 算法目标

实现粤北卷烟物流运输系统的路径规划算法，对给定的聚集区进行路线分配和路径优化，目标是使各路线工作时长尽可能均衡，同时保证各层级（路线间、中转站间、班组间）的时间差距最小化。

## 📋 工作范围

### ✅ 算法职责
- 接收聚集区数据和层级关系
- 将聚集区分配给不同的路线
- 优化每条路线内聚集区的访问顺序
- 实现多层级时间均衡算法（路线间、中转站间、班组间）
- 生成路线凸包并保证凸包不重叠
- 计算并返回优化后的路径规划方案

### ❌ 非算法职责
- 商铺数据处理（由系统完成）
- 聚集区生成（K-means聚类，由系统完成）
- 距离矩阵计算（由地图API完成）
- 业务逻辑处理（由业务层完成）
- 数据持久化（由数据层完成）

## 🔧 工具类设计

### 1. 主要接口方法

```java
/**
 * 路径规划工具类
 */
public class PathPlanningUtils {
    
    /**
     * 全地图路径规划算法
     * @param request 路径规划请求数据
     * @return 路径规划结果
     */
    public static PathPlanningResult calculate(PathPlanningRequest request) {
        // 实现路径规划算法
    }
}
```

## 📥 输入数据格式

### 1. PathPlanningRequest 数据结构

```java
public class PathPlanningRequest {
    
    /**
     * 聚集区列表
     */
    private List<Accumulation> accumulations;
    
    /**
     * 中转站列表
     */
    private List<TransitDepot> transitDepots;
    
    /**
     * 班组列表（用于均衡计算）
     */
    private List<Team> teams;
    
    /**
     * 时间矩阵（边权）
     */
    private Map<String, TimeInfo> timeMatrix;
}
```

### 2. 核心数据结构

#### 2.1 Accumulation - 聚集区数据
```java
public class Accumulation {
    /**
     * 聚集区ID
     */
    private Long accumulationId;
    
    /**
     * 聚集区名称
     */
    private String accumulationName;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 所属中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 聚集区配送时间（分钟）- 点权
     */
    private Double deliveryTime;
}
```

#### 2.2 TransitDepot - 中转站数据
```java
public class TransitDepot {
    /**
     * 中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 中转站名称
     */
    private String transitDepotName;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 所属班组ID
     */
    private Long groupId;
    
    /**
     * 规划路线数量
     */
    private Integer routeCount;
}
```

#### 2.3 Team - 班组数据
```java
public class Team {
    /**
     * 班组ID
     */
    private Long teamId;
    
    /**
     * 班组名称
     */
    private String teamName;
    
    /**
     * 班组下所有中转站ID列表
     */
    private List<Long> transitDepotIds;
}
```

#### 2.4 TimeInfo - 时间信息（边权）
```java
public class TimeInfo {
    /**
     * 起点经度
     */
    private Double fromLongitude;
    
    /**
     * 起点纬度
     */
    private Double fromLatitude;
    
    /**
     * 终点经度
     */
    private Double toLongitude;
    
    /**
     * 终点纬度
     */
    private Double toLatitude;
    
    /**
     * 行驶时间（分钟）- 边权
     */
    private Double travelTime;
}
```

## 📤 输出数据格式

### 1. PathPlanningResult 数据结构

```java
public class PathPlanningResult {
    /**
     * 计算状态
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 路径规划结果
     */
    private List<RouteResult> routes;
    
    /**
     * 计算耗时（毫秒）
     */
    private Long executionTime;
    
    /**
     * 时间均衡统计（用于评估算法效果）
     */
    private TimeBalanceStats timeBalanceStats;
}
```

### 2. RouteResult - 路线结果
```java
public class RouteResult {
    /**
     * 路线ID
     */
    private Long routeId;
    
    /**
     * 路线名称
     */
    private String routeName;
    
    /**
     * 所属中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 路线包含的聚集区ID列表（按访问顺序）
     */
    private List<Long> accumulationSequence;
    
    /**
     * 路线坐标串（用于地图绘制）
     */
    private List<CoordinatePoint> polyline;
    
    /**
     * 路线总工作时长（分钟）
     */
    private Double totalWorkTime;
    
    /**
     * 路线凸包坐标点串（用于区域显示和重叠检测）
     */
    private List<CoordinatePoint> convexHull;
}
```

### 3. TimeBalanceStats - 时间均衡统计
```java
public class TimeBalanceStats {
    /**
     * 路线间时间差距统计（每个中转站下所有路线的最大时间差）
     * Key: 中转站ID, Value: 该中转站下路线间最大时间差（分钟）
     */
    private Map<Long, Double> routeTimeGapByDepot;
    
    /**
     * 中转站间时间差距统计（每个班组下所有中转站的最大时间差）
     * Key: 班组ID, Value: 该班组下中转站间最大时间差（分钟）
     */
    private Map<Long, Double> depotTimeGapByTeam;
    
    /**
     * 班组间时间差距统计（全局最大时间差）
     * Value: 所有班组间最大时间差（分钟）
     */
    private Double teamTimeGap;
}
```

### 4. CoordinatePoint - 坐标点
```java
public class CoordinatePoint {
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
}
```

## 📊 层级关系说明

### 1. 数据层级结构
```
班组 (Team)
└── 中转站 (TransitDepot) - 通过groupId关联
    └── 聚集区 (Accumulation) - 通过transitDepotId关联，待分配到路线
```

### 2. 均衡算法层级
1. **路线间均衡**：同一中转站下的多条路线时间均衡
2. **中转站间均衡**：同一班组下的多个中转站平均时间均衡
3. **班组间均衡**：全地图多个班组平均时间均衡

### 3. 关键算法逻辑
1. **聚集区分配**：将聚集区按中转站分组，再按routeCount分配给各条路线
2. **TSP优化**：每条路线内聚集区访问顺序优化（最小化总时间）
3. **时间均衡**：通过聚集区在路线间的重新分配实现时间均衡

## 💡 算法核心要点

### 1. 点权和边权
- **点权**：聚集区配送时间（`deliveryTime`）
- **边权**：两点间行驶时间（`travelTime`）
- **目标**：最小化路线总时间，同时实现时间均衡

### 2. 路径计算
- 每条路线从中转站出发，按顺序访问聚集区，最后回到中转站
- 路线总时间 = 装车时间 + 行驶时间总和 + 聚集区配送时间总和

### 3. 时间均衡策略
- 通过调整聚集区在路线间的分配来实现均衡
- 多层级均衡：先路线间，再中转站间，最后班组间
- 使用TimeBalanceStats评估各层级均衡效果

### 4. 凸包生成与不重叠约束
- 为每条路线生成凸包（包含该路线所有聚集区的最小凸多边形）
- 使用Jarvis步进算法计算凸包
- 确保同一中转站下的路线凸包不重叠
- 凸包用于路线区域可视化和配送区域划分

## 🔺 凸包算法详解

### 1. 凸包生成算法
```java
/**
 * 凸包生成工具类
 */
public class ConvexHullGenerator {
    
    /**
     * 使用Jarvis步进算法生成凸包
     * @param points 聚集区坐标点数组
     * @return 凸包坐标点列表（逆时针顺序）
     */
    public static List<CoordinatePoint> generateConvexHull(CoordinatePoint[] points) {
        // 实现Jarvis步进算法
        // 1. 找到最左边的点作为起点
        // 2. 逆时针方向寻找下一个凸包点
        // 3. 直到回到起点形成闭合凸包
    }
}
```

### 2. 凸包不重叠处理
- **检测算法**：使用几何相交检测判断凸包是否重叠
- **处理策略**：通过调整聚集区分配来消除重叠
- **优化目标**：在保证不重叠的前提下，最小化时间均衡损失

### 3. 凸包数据格式
- **输入格式**：路线内所有聚集区的坐标点
- **输出格式**：`List<CoordinatePoint>` 凸包顶点坐标
- **存储格式**：数据库中存储为坐标点字符串（如："113.596766,24.810403;113.598123,24.812456"）

### 4. 凸包约束条件
- **最小点数**：凸包至少需要3个点
- **闭合要求**：首尾坐标点相同形成闭合多边形
- **顺序要求**：坐标点按逆时针顺序排列
- **重叠检测**：同一中转站下的路线凸包不能相交或包含

## ⚠️ 注意事项

### 1. 数据一致性
- 所有ID引用必须在对应列表中存在
- 时间矩阵必须包含所有聚集区和中转站之间的时间
- 层级关系必须正确

### 2. 算法约束
- 每个聚集区只能分配给一条路线
- 每条路线必须从中转站出发回到中转站
- 时间均衡是软约束，尽力而为
- 路线凸包不重叠是硬约束，必须满足

### 3. 性能考虑
- 建议聚集区数量控制在500以内
- 时间矩阵建议预先计算并缓存 