server:
  port: 8083
spring:
  application:
    name: clustercalculate
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
  mvc:
    servlet:
      load-on-startup: 1
mybatis:
  type-aliases-package: com.ict.ycwl.clustercalculate.pojo
  configuration:
    map-underscore-to-camel-case: true
logging:
  level:
    cn.itcast: debug
knife4j:
  enable: false

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ict.ycwl.clustercalculate.pojo
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl