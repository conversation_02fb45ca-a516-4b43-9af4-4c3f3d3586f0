// src/router/index.ts
import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";

// 静态路由
const constantRoutes: RouteRecordRaw[] = [
  { path: "/", redirect: "/login" },
  {
    path: "/login",
    name: "login",
    component: () => import("@/pages/Login/Login.vue"),
  },
  {
    path: "/home",
    name: "home",
    component: () => import("@/pages/Home/Home.vue"),
    meta: { order: 0 },
    redirect: "/home/<USER>",
    children: [
      {
        path: "/home/<USER>",
        component: () => import("@/pages/Home/Board/Board.vue"),
        redirect: "/home/<USER>/info?feedbackType=1",
        children: [
          {
            path: "/home/<USER>/info",
            name: "boardInfo",
            component: () =>
              import("@/pages/Home/Board/BoardInfo/BoardInfo.vue"),
          },
        ],
      },
      {
        path: "/home/<USER>",
        name: "group",
        meta: { order: 1 },
        component: () => import("@/pages/Home/Group/Group.vue"),
      },
      {
        path: "/home/<USER>",
        name: "computer",
        meta: { order: 2 },
        component: () => import("@/pages/Home/Computer/Computer.vue"),
        redirect: "/home/<USER>/route",
        children: [
          {
            path: "/home/<USER>/area",
            name: "area",
            component: () => import("@/pages/Home/Computer/Area/Area.vue"),
          },
          {
            path: "/home/<USER>/route",
            name: "route",
            component: () => import("@/pages/Home/Computer/Route/Route.vue"),
          },
          {
            path: "/home/<USER>/MilkRun",
            name: "MilkRun",
            component: () =>
              import("@/pages/Home/Computer/MilkRun/MilkRun.vue"),
            redirect: "/home/<USER>/MilkRun/pickup",
            children: [
              {
                path: "/home/<USER>/MilkRun/pickup",
                name: "pickup",
                component: () =>
                  import(
                    "@/pages/Home/Computer/MilkRun/pickupAnalysis/pickup.vue"
                  ),
              },
              {
                path: "/home/<USER>/MilkRun/pos",
                name: "pos",
                component: () =>
                  import("@/pages/Home/Computer/MilkRun/posAnalysis/pos.vue"),
              },
            ],
          },
          {
            path: "/home/<USER>/AreaAdjust",
            name: "AreaAdjust",
            component: () =>
              import("@/pages/Home/Computer/AreaAdjust/AreaAdjust.vue"),
            meta: {
              isShow: true,
              order: 2,
            },
          },
        ],
      },

      {
        path: "/home/<USER>",
        name: "AnalysisRoute",
        component: () =>
          import("@/pages/Home/Computer/AnalysisRoute/AnalysisRoute.vue"),
      },
      {
        path: "/home/<USER>",
        name: "management",
        meta: { order: 3 },
        component: () => import("@/pages/Home/Management/Management.vue"),
        children: [
          {
            path: "/home/<USER>/shops",
            name: "shops",
            component: () => import("@/pages/Home/Management/Shops/Shops.vue"),
          },
          {
            path: "/home/<USER>/area",
            name: "areas",
            component: () => import("@/pages/Home/Management/Area/Area.vue"),
          },
          {
            path: "/home/<USER>/delivery",
            name: "delivery",
            component: () =>
              import("@/pages/Home/Management/Delivery/Delivery.vue"),
          },
          {
            path: "/home/<USER>/transfer",
            name: "transfer",
            component: () =>
              import("@/pages/Home/Management/Transfer/Transfer.vue"),
          },
          {
            path: "/home/<USER>/car",
            name: "car",
            component: () => import("@/pages/Home/management/Car/Car.vue"),
            redirect: "/home/<USER>/car/message",
            children: [
              {
                path: "/home/<USER>/car/message",
                name: "message",
                component: () =>
                  import(
                    "@/pages/Home/management/Car/cpns/Message/Message.vue"
                  ),
              },
              {
                path: "/home/<USER>/car/work",
                name: "work",
                component: () =>
                  import("@/pages/Home/Management/Car/cpns/Work/Work.vue"),
              },
            ],
          },
          {
            path: "/home/<USER>/system",
            name: "system",
            component: () =>
              import("@/pages/Home/Management/System/System.vue"),
          },
        ],
      },
    ],
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
});

/**
 * 重置路由
 */
export function resetRouter() {
  router.push({ path: "/login" });
  location.reload();
}
router.beforeEach((to) => {
  //登录成功有token进入main
  const token = localStorage.getItem("token");
  if (to.path.startsWith("/home") && !token) {
    return "/login";
  }
});
// 声明meta属性类型
declare module "vue-router" {
  interface RouteMeta {
    order: number;
  }
}
export default router;
