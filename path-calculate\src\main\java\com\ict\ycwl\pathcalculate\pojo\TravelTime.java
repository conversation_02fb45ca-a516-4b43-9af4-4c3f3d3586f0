package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 行驶时间实体类
 * 对应travel_time表
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@TableName("travel_time")
public class TravelTime {
    
    /**
     * 起始点经度
     */
    private String longitudeStart;
    
    /**
     * 起始点纬度
     */
    private String latitudeStart;
    
    /**
     * 终点经度
     */
    private String longitudeEnd;
    
    /**
     * 终点纬度
     */
    private String latitudeEnd;
    
    /**
     * 行驶时间（分钟）
     */
    private Double travelTime;
}
