package com.ict.ycwl.pathcalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pathcalculate.pojo.WorkParameter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 工作参数配置Mapper接口
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Mapper
public interface WorkParameterMapper extends BaseMapper<WorkParameter> {

    /**
     * 根据站点名称查询工作参数
     *
     * @param name 站点名称
     * @return 工作参数配置
     */
    WorkParameter selectByName(@Param("name") String name);

    /**
     * 获取全局参数（韶关市）
     *
     * @return 全局工作参数配置
     */
    WorkParameter selectGlobalParameter();
}
