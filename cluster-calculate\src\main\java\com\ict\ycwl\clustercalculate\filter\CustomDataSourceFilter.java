package com.ict.ycwl.clustercalculate.filter;


import com.ict.ycwl.clustercalculate.utlis.dbDataSourceUtils.DataSourceContextHolder;
import com.ict.ycwl.clustercalculate.utlis.dbDataSourceUtils.FileUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.servlet.*;
import java.io.IOException;

@Configuration
public class CustomDataSourceFilter implements Filter {

    @Value("${jjking.dbPath}")
    private String dbPath;


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Fi<PERSON><PERSON>hain filterChain) throws IOException, ServletException {
        String type = FileUtil.readSingleLine(dbPath);
        DataSourceContextHolder.setDataSource(type);
        filterChain.doFilter(servletRequest, servletResponse);
    }

}
