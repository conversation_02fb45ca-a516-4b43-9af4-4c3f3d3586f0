<template>
  <div class="changeActualDialog">
    <el-dialog v-model="changeActualOpen" title="修改实情信息" width="60%" height="70%" :close-on-click-modal="false" @close="closeChange">
      <div class="flex-center">
        <el-form label-width="auto" class="areaForm" :model="totalFields" ref="formRef" :rules="rules">
        <div class="flex">
          <el-form-item label="车牌号" >
            <div style="width: 250px;">
            {{ props.info.licensePlateNumber }}
            </div>
        </el-form-item>
        <el-form-item label="配送域">
          <div style="width: 250px;">
            {{ props.info.deliveryAreaName }}
          </div>
        </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="驾驶人">
          <div style="width: 250px;">
            {{ props.info.carDriverName }}
          </div>
        </el-form-item>
        <el-form-item label="星期">
          {{ getWeekday(props.info.date) }}
        </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="实际载货量" prop="actualLoad" :style="{display: 'flex'}">
            <el-input
              style="width: 200px;"
              placeholder="点击输入"
              v-model="totalFields.actualLoad"
            >
            </el-input>
            <div class="tons">吨</div>
          </el-form-item>
          <el-form-item label="日期">
            <div>
            {{ props.info.date }}
          </div>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="实际工作时长" prop="actualTime" :style="{display: 'flex'}">
            <el-input
              style="width: 200px;"
              placeholder="点击输入"
              v-model="totalFields.actualTime"
            >
            </el-input>
            <div class="tons">小时</div>
          </el-form-item>
          <el-form-item label="路线" prop="route">
              <el-input
              style="width: 200px;"
              placeholder="点击输入"
              v-model="totalFields.route"
              >
              </el-input>
          </el-form-item>
        </div>
      </el-form>
      </div>

      <div class="btns">
        <el-button type="primary" @click="closeChange">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmChange"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { carStore } from "@/store/managerment/car";
const store = carStore()
const changeActualOpen = ref(false);
defineExpose({
  changeActualOpen
});

const formRef = ref<any>()
const changeEmit = defineEmits(["confirmChange"]);

const props = defineProps({
  info: Object
})

const totalFields = ref<any>({
  actualLoad: '',
  actualTime: '',
  route: ''
})

onUpdated(() => {
  totalFields.value.actualLoad = props.info.actualLoad
  totalFields.value.actualTime = props.info.actualTime
  totalFields.value.route = props.info.routeName 
})

const rules = reactive({
})


//确认添加
async function confirmChange() {
  if (!formRef.value) return
  const totalData : any = {
    licensePlateNumber: props.info.licensePlateNumber,
    carDriverName: props.info.carDriverName,
    carId: Number(props.info.carId),
    date: props.info.date,
    week: getWeekday(props.info.date),
    deliveryAreaName: props.info.deliveryAreaName,
    ...totalFields.value
  }
  await formRef.value.validate((valid : any) => {
  if (valid) {
    store.updateCarActual(totalData).then((res) => {
      formRef.value.resetFields()
      changeActualOpen.value = false
      if(res.code === 50001 || res.message === '系统异常') {
      ElMessage.error('系统异常!')
      return
      }
        changeEmit("confirmChange");
    })
    
  } else {
    ElMessage({
    message: '修改失败',
    type: 'warning'
    })
  }
})
}

//取消添加
const closeChange = () => {
  formRef.value.resetFields()
  changeActualOpen.value = false;
};

function getWeekday(dateString : string) {
  const date = new Date(dateString);
  const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  return weekdays[date.getDay()]
}
</script>

<style lang="less" scoped>
.flex-center {
width: 100%;
display: flex;
justify-content: center;
}
.flex {
display: flex;
}
.areaForm {
  .areaItem {
    font-size: 20px;
  }
}
.btns {
  display: flex;
  justify-content: center;
  color: black;
}

.transform {
  .content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    font-size: 20px;
  }
}

.tons {
  font-size: 20px;
  margin-left: 20px;
}
</style>
