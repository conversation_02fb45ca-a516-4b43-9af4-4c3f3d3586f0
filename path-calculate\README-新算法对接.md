# 粤北卷烟物流管理系统 - 新算法对接文档

## 概述

本文档描述了如何将PathPlanningUtils新算法集成到现有的粤北卷烟物流管理系统中。新算法提供了更高效的路径规划功能，支持多种优化策略。

## 系统架构

### 核心组件

1. **DatabaseToAlgorithmAdapter** - 数据转换适配器
   - 负责将数据库数据转换为算法输入格式
   - 负责将算法结果转换为前端需要的格式

2. **AlgorithmConfig** - 算法配置管理
   - 管理算法执行参数
   - 支持动态配置切换

3. **NewAlgorithmController** - 新算法测试控制器
   - 提供算法测试接口
   - 支持算法性能监控

4. **PathPlanningUtils** - 核心算法引擎
   - 执行路径规划计算
   - 支持Spring容器集成

## 快速开始

### 1. 配置文件设置

在 `application.yml` 中添加算法配置：

```yaml
ycwl:
  algorithm:
    enable-new-algorithm: true
    timeout-ms: 300000
    enable-fallback: true
    params:
      max-route-count: 50
      average-speed: 30.0
```

### 2. 启动应用

确保数据库连接正常，启动Spring Boot应用：

```bash
mvn spring-boot:run
```

### 3. 测试新算法

#### 健康检查
```bash
GET http://localhost:8080/api/new-algorithm/health
```

#### 测试数据加载
```bash
GET http://localhost:8080/api/new-algorithm/test-data-loading
```

#### 执行完整算法流程
```bash
POST http://localhost:8080/api/new-algorithm/test-full-execution?apiKey=your-api-key
```

## API接口说明

### 1. 健康检查接口

**接口地址**: `GET /api/new-algorithm/health`

**功能**: 检查新算法各组件的健康状态

**返回示例**:
```json
{
  "success": true,
  "status": "healthy",
  "components": {
    "databaseAdapter": "ok",
    "pathPlanningUtils": "ok",
    "algorithmConfig": "ok"
  },
  "timestamp": 1691740800000
}
```

### 2. 数据加载测试接口

**接口地址**: `GET /api/new-algorithm/test-data-loading`

**功能**: 测试从数据库加载数据并转换为算法格式

**返回示例**:
```json
{
  "success": true,
  "message": "数据加载成功",
  "data": {
    "accumulationCount": 150,
    "transitDepotCount": 5,
    "teamCount": 3,
    "timeMatrixSize": 2250
  }
}
```

### 3. 完整算法执行接口

**接口地址**: `POST /api/new-algorithm/test-full-execution`

**参数**: 
- `apiKey` (可选): API密钥，默认为"test-api-key"

**功能**: 执行完整的算法流程（数据加载 → 算法计算 → 结果转换）

**返回示例**:
```json
{
  "success": true,
  "message": "新算法执行成功",
  "data": {
    "inputData": {
      "accumulationCount": 150,
      "transitDepotCount": 5,
      "teamCount": 3,
      "timeMatrixSize": 2250
    },
    "algorithmResult": {
      "success": true,
      "routeCount": 15,
      "executionTime": 2500,
      "algorithmExecutionTime": 2300
    },
    "finalResult": {
      "resultRouteCount": 15
    }
  },
  "sampleRoutes": [...]
}
```

### 4. 配置管理接口

**获取配置**: `GET /api/new-algorithm/config`

**更新配置**: `POST /api/new-algorithm/config`

请求体示例：
```json
{
  "enableNewAlgorithm": true,
  "enableFallback": true,
  "timeoutMs": 300000
}
```

## 数据流程

### 1. 数据加载流程

```
数据库表 → DatabaseToAlgorithmAdapter → PathPlanningRequest
```

涉及的数据表：
- `accumulation` - 聚集区信息
- `transit_depot` - 中转站信息  
- `group` - 班组信息
- `point_distance` - 点距离信息（用于构建时间矩阵）

### 2. 算法执行流程

```
PathPlanningRequest → PathPlanningUtils → PathPlanningResult
```

### 3. 结果转换流程

```
PathPlanningResult → DatabaseToAlgorithmAdapter → List<ResultRoute>
```

## 配置参数说明

### 基础配置

- `enable-new-algorithm`: 是否启用新算法
- `timeout-ms`: 算法执行超时时间（毫秒）
- `enable-fallback`: 是否启用降级（新算法失败时使用旧算法）
- `max-retry-count`: 最大重试次数

### 算法参数

- `max-route-count`: 最大路线数量
- `min-accumulation-count`: 最小聚集区数量
- `max-work-time-hours`: 最大工作时间（小时）
- `average-speed`: 平均行驶速度（km/h）
- `delivery-time-factor`: 配送时间系数
- `consider-traffic`: 是否考虑交通拥堵
- `optimize-route-order`: 是否优化路线顺序
- `route-balance-factor`: 路线平衡系数

## 测试说明

### 单元测试

运行集成测试：
```bash
mvn test -Dtest=NewAlgorithmIntegrationTest
```

### 性能测试

测试类包含性能测试方法，可以评估算法的执行效率。

### 手动测试

1. 启动应用后访问健康检查接口
2. 测试数据加载功能
3. 执行完整算法流程
4. 检查返回结果的正确性

## 故障排除

### 常见问题

1. **数据加载失败**
   - 检查数据库连接
   - 确认相关数据表存在且有数据
   - 查看日志中的具体错误信息

2. **算法执行失败**
   - 检查输入数据的有效性
   - 确认算法参数配置正确
   - 查看算法执行日志

3. **结果转换失败**
   - 检查算法结果格式
   - 确认API密钥有效
   - 查看转换过程中的异常信息

### 日志配置

在 `application.yml` 中配置详细日志：

```yaml
logging:
  level:
    com.ict.ycwl.pathcalculate.algorithm: DEBUG
    com.ict.ycwl.pathcalculate.service.adapter: DEBUG
```

## 部署说明

### 生产环境配置

1. 设置合适的超时时间
2. 启用结果缓存（如需要）
3. 配置监控和告警
4. 设置合理的日志级别

### 性能优化建议

1. 根据实际数据量调整算法参数
2. 考虑使用异步执行模式
3. 启用结果缓存减少重复计算
4. 监控内存使用情况

## 联系信息

如有问题，请联系开发团队或查看相关技术文档。
