<!-- eslint-disable no-mixed-spaces-and-tabs -->
<template>
  <div class="shopsUpload">
    <el-dialog title="导入表格" v-model="uploadOpen">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        action=""
        :limit="1"
        :file-list="fileList"
        :http-request="uploadFile"
        :multiple="false"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
        :auto-upload="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text" style="line-height: 30px">
          将文件拖拽至此区域<br />
          -或- <br />
          <el-button style="margin-top: 10px">选择文件</el-button>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: rgb(226, 51, 51)">
            *一次只能导入一个文件，仅支持xls、xlsx和csv格式
          </div>
          <div class="download">
            <el-button
              :icon="Download"
              class="button"
              @click="downloadNullShops"
              >点击此处下载空格表格</el-button
            >
          </div>
          
        </template>
      </el-upload>
      <div class="mode">
            <el-radio-group v-model="mode">
              <el-radio label="追加">追加模式</el-radio>
              <el-radio label="覆盖">覆盖模式</el-radio>
            </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer" style="display: flex;justify-content: center;">
          <el-button @click="cancelDialog" type="primary">取消</el-button>
          <el-button type="primary" @click="confirmDialog">
            确定
          </el-button>
        </div>
      </template> 
    </el-dialog>
  </div>
  <el-dialog
    v-model="tableVisible"
    title="上传文件"
    class="transform"
    width="50%"
    align-center
    @close="cancelUploadVis"
    :close-on-click-modal="false"
  >
    <el-table
      ref="tableRef"
      :data="uploadList"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{
        height: '1vh',
        'text-align': 'center',
      }"
      size="small"
      :row-style="{ height: '4vh' }"
      style="font-size: 1vw"
    >
      <el-table-column label="文件名" min-width="1%" prop="fileName" />
      <el-table-column label="大小" min-width="1%" prop="fileSize" />
      <el-table-column
        v-if="uploadList[0].fileName"
        label="状态"
        min-width="1%"
      >
        <el-progress
          :text-inside="true"
          :stroke-width="26"
          :percentage="percentage"
        />
      </el-table-column>
      <el-table-column
        v-if="uploadList[0].fileName"
        label="操作"
        min-width="1%"
      >
        <template #default>
          <el-button
            size="small"
            type="primary"
            @click="deleteLogData"
            :icon="CloseBold"
          >
            删除
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="reUpload"
            :icon="RefreshRight"
          >
            重传
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  UploadFilled,
  Download,
  CloseBold,
  RefreshRight,
} from "@element-plus/icons-vue";
import {
  genFileId,
  UploadInstance,
  UploadRawFile,
  UploadUserFile,
} from "element-plus";
import { ref } from "vue";
import { usedatamanagementStore } from "@/store/datamanagement";
import { carStore } from "@/store/managerment/car";
const fileList = ref<UploadUserFile[]>([]);
const fileType = ref(["csv", "xls", "xlsx"]);
const store = usedatamanagementStore();
const CarStore = carStore();
const uploadRef = ref<UploadInstance>();
const mode = ref<string>("追加");

const percentage = ref<number>(0);
const uploadOpen = ref(false);
const tableVisible = ref<boolean>(false);
const uploadList = ref<any>([
  {
    fileName: "",
    fileSize: "",
  },
]);
const controller = new AbortController();

defineExpose({
  uploadOpen,
});

const uploadEmit = defineEmits(["confirmUpload"])


async function downloadNullShops() {
  CarStore.downloadFrom({ code: 0, csvOrExcel: 1 }).then((res) => {
    let a = document.createElement("a");
    a.download = "商铺空白表格模版.xlsx";
    a.style.display = "none";
    let url = URL.createObjectURL(res);
    a.href = url;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url); // 销毁
    document.body.removeChild(a);
  });
}

function cancelDialog() {
  uploadRef.value!.clearFiles();
  uploadOpen.value = false;
}

function confirmDialog() {
  uploadRef.value?.submit();
  uploadOpen.value = false;
}

function cancelUploadVis() {
  uploadRef.value!.clearFiles();
}

//上传文件之前
function beforeUpload(file: any) {
  if (file.type != "" || file.type != null || file.type != undefined) {
    const FileExt = file.name.replace(/.+\./, "").toLowerCase();
    //计算文件的大小
    const isLt5M = file.size / 1024 / 1024 < 50; //这里做文件大小限制
    //如果大于50M
    if (!isLt5M) {
      ElMessage("上传文件大小不能超过 50MB!");
      return false;
    }
    //如果文件类型不在允许上传的范围内
    if (fileType.value.includes(FileExt)) {
      fileList.value[0] = file;
      uploadList.value = [
        {
          fileName: fileList.value[0].name,
          fileSize: (fileList.value[0].size / 1024).toFixed(2) + "kb",
        },
      ];
      return true;
    } else {
      ElMessage.error("上传文件格式不正确!");
      return false;
    }
  }
}

//超出文件个数的回调
function handleExceed(files: any) {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
  fileList.value[0] = file;
}

//删除文件记录
function deleteLogData() {
  uploadList.value = [{}];
  if (percentage.value < 100) {
    controller.abort();
  }
}

//重新上传
function reUpload() {
  if (percentage.value == 100) {
    ElMessage.error("已上传成功,请勿重复上传");
  } else {
    uploadRef.value?.submit();
  }
}

//上传文件的事件
function uploadFile(item: any) {
  const config = {
    signal: controller.signal,
    onUploadProgress: (progressEvent: any) => {
      percentage.value =
        Number(
          ((progressEvent.loaded / progressEvent.total) * 100).toFixed(1)
        ) - 1;
    },
  };
  let formData: FormData = new FormData();
  formData.append("File", item.file);
  formData.append("Authorization", localStorage.getItem("token"));
  formData.append("mode", mode.value);
  store.importStoreTable(formData, config).then((res: any) => {
    if (res.message === "系统异常") {
      ElMessage.error("系统异常");
      uploadRef.value!.clearFiles();
      return;
    }
    if (res.includes("导入失败")) {
      ElMessage.error(res);
      uploadRef.value!.clearFiles();
      return;
    }
    if (res.includes("导入成功")) {
      percentage.value = 100;
      uploadOpen.value = false;
      uploadEmit("confirmUpload")
      tableVisible.value = true;
    }
  });
}
</script>

<style lang="less" scoped>
.shopsUpload {
  color: black;
}
.download {
  margin-top: 10px;
  font-size: 14px;
}
.button {
  height: 25px;
}
.mode,
.button {
  margin-top: 20px;
}
.button {
  display: flex;
  justify-content: center;
}
</style>
