<template>
    <el-dialog
      v-model="deleteVis"
      title="删除班组"
      class="transform"
      width="40%"
      align-center
      :close-on-click-modal="false"
    >
      <div class="content">确定删除所选班组</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog" type="primary">取消</el-button>
          <el-button type="primary" @click="confirmDialog">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
<script setup lang="ts">
const deleteVis = ref<boolean>(false)
const deleteEmit = defineEmits(["confirmDelete"]);

defineExpose({
  deleteVis
});

function cancelDialog() {
  deleteVis.value = false
}

function confirmDialog() {
  deleteEmit("confirmDelete")
}
  
</script>
  
<style lang="less" scoped>
.transform {
    .content {
      display: flex;
      justify-content: center;
      font-size: 20px;
    }
}
</style>
  