<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.RouteMapper">

    <resultMap id="BaseResultMap" type="com.ict.datamanagement.domain.entity.Route">
            <id property="route_id" column="route_id" jdbcType="BIGINT"/>
            <result property="route_name" column="route_name" jdbcType="VARCHAR"/>
            <result property="distance" column="distance" jdbcType="VARCHAR"/>
            <result property="transit_depot_id" column="transit_depot_id" jdbcType="BIGINT"/>
            <result property="area_id" column="area_id" jdbcType="BIGINT"/>
            <result property="polyline" column="polyline" jdbcType="VARCHAR"/>
            <result property="is_delete" column="is_delete" jdbcType="TINYINT"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="update_time" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="cargo_weight" column="cargo_weight" jdbcType="VARCHAR"/>
            <result property="version_number" column="version_number" jdbcType="INTEGER"/>
            <result property="convex" column="convex" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        route_id,route_name,distance,
        transit_depot_id,area_id,polyline,
        is_delete,create_time,update_time,
        cargo_weight,version_number,convex
    </sql>
    <select id="selectRoutes" resultType="com.ict.datamanagement.domain.entity.Route">
        select * from route where is_delete=0
    </select>
</mapper>
