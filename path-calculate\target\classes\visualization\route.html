<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化路径规划分析平台</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            background: white;
            border-radius: 12px;
            margin: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        #map {
            flex: 1;
            height: 100%;
            position: relative;
        }
        
        .sidebar {
            width: 420px;
            background: white;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #e0e6ed;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .upload-section {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #667eea;
            background: #f7faff;
            transform: translateY(-2px);
        }

        .upload-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .tabs-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .tabs-header {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 10px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            color: #667eea;
            background: white;
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .tab-btn:hover:not(.active) {
            background: #f1f5f9;
            color: #475569;
        }

        .tab-content {
            flex: 1;
            overflow-y: auto;
            display: none;
            max-height: calc(100vh - 200px);
        }

        .tab-content.active {
            display: block;
        }

        /* 筛选控制面板 */
        .filter-panel {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .filter-section {
            margin-bottom: 20px;
        }

        .filter-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: #374151;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .filter-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .select-control {
            flex: 1;
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            font-size: 13px;
            transition: border-color 0.3s ease;
        }

        .select-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-input {
            flex: 1;
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            font-size: 13px;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .clear-btn {
            padding: 10px 16px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        /* 概览标签页 */
        .overview-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 8px;
            font-weight: 500;
        }

        .controls-section {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #374151;
            font-size: 13px;
        }

        .toggle-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .toggle-btn {
            padding: 8px 16px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        /* 路线分析标签页 */
        .depot-groups {
            padding: 15px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .depot-group {
            margin-bottom: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            transition: all 0.3s ease;
        }

        .depot-group.highlighted {
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .depot-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .depot-header:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
        }

        .depot-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .depot-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .depot-stats {
            font-size: 12px;
            color: #64748b;
        }

        .expand-icon {
            transition: transform 0.3s ease;
        }

        .depot-group.expanded .expand-icon {
            transform: rotate(180deg);
        }

        .routes-list {
            display: none;
        }

        .depot-group.expanded .routes-list {
            display: block;
        }

        .route-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .route-item:hover {
            background: #f8fafc;
            transform: translateX(5px);
        }

        .route-item.selected {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-left: 4px solid #3b82f6;
        }

        .route-item.highlighted {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
        }

        .route-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .route-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 12px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-weight: 700;
            color: #3b82f6;
            font-size: 14px;
        }

        .metric-label {
            font-size: 10px;
            color: #64748b;
            margin-top: 2px;
        }

        /* 中转站分析标签页 */
        .analysis-section {
            padding: 20px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .chart-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            font-size: 12px;
        }

        .comparison-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .comparison-table tr:hover {
            background: #f8fafc;
        }

        /* 信息面板 */
        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            min-width: 300px;
            max-width: 400px;
            z-index: 1000;
            display: none;
            border: 1px solid #e2e8f0;
        }

        .info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .info-title {
            font-weight: 700;
            color: #1e293b;
            font-size: 16px;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #94a3b8;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f1f5f9;
            color: #64748b;
        }

        .info-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .info-content p {
            margin-bottom: 10px;
        }

        .info-content strong {
            color: #374151;
        }

        /* 图例 */
        .legend {
            background: #f8fafc;
            padding: 20px;
            border-top: 1px solid #e2e8f0;
            display: none;
        }

        .legend-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #374151;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .legend-marker, .legend-color {
            margin-right: 12px;
            border-radius: 4px;
        }

        .legend-marker {
            width: 14px;
            height: 14px;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .legend-color {
            width: 24px;
            height: 4px;
        }

        /* 加载动画 */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示 */
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 13px;
        }

        .message.success {
            background: #f0fdf4;
            color: #166534;
            border-left: 4px solid #22c55e;
        }

        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #ef4444;
        }

        /* 高亮样式 */
        .route-highlighted {
            z-index: 1000 !important;
        }

        .route-normal {
            opacity: 0.3 !important;
        }

        .depot-highlighted .depot-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 380px;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
                margin: 4px;
            }
            
            .sidebar {
                width: 100%;
                height: 50%;
            }
            
            #map {
                height: 50%;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }

        /* 筛选状态指示器 */
        .filter-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 1000;
            display: none;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .filter-indicator.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="loading" id="loading" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>正在处理数据...</div>
        </div>
    </div>
    
    <div class="app-container">
        <div id="map">
            <div class="filter-indicator" id="filterIndicator">
                <i class="fas fa-filter"></i>
                <span id="filterText"></span>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="header">
                <h1><i class="fas fa-route"></i> 优化路径规划分析</h1>
                <div class="subtitle">Advanced Route Planning Analytics</div>
            </div>
            
            <div class="upload-section">
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 24px; color: #667eea;"></i>
                    </div>
                    <div class="upload-text">点击选择或拖拽 JSON 文件</div>
                    <button type="button" class="upload-button">
                        <i class="fas fa-file-upload"></i> 选择文件
                    </button>
                    <input type="file" id="fileInput" style="display: none;" accept=".json" />
                    <div class="file-info" id="fileInfo" style="margin-top: 10px; font-size: 12px; color: #64748b;"></div>
                </div>
                <div id="uploadMessage"></div>
            </div>

            <div class="tabs-container" id="tabsContainer" style="display: none;">
                <div class="tabs-header">
                    <button class="tab-btn active" data-tab="overview">
                        <i class="fas fa-chart-pie"></i><br>筛选
                    </button>
                    <button class="tab-btn" data-tab="routes">
                        <i class="fas fa-route"></i><br>路线
                    </button>
                    <button class="tab-btn" data-tab="depots">
                        <i class="fas fa-warehouse"></i><br>中转站
                    </button>
                    <button class="tab-btn" data-tab="analysis">
                        <i class="fas fa-chart-line"></i><br>分析
                    </button>
                </div>

                <!-- 筛选标签页 -->
                <div class="tab-content active" id="overviewTab">
                    <div class="overview-stats">
                        <div class="stat-card">
                            <div class="stat-value" id="totalRoutes">0</div>
                            <div class="stat-label">总路线数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalDepots">0</div>
                            <div class="stat-label">中转站数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalAccumulations">0</div>
                            <div class="stat-label">聚集区数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="avgEfficiency">0%</div>
                            <div class="stat-label">平均效率</div>
                        </div>
                    </div>

                    <div class="filter-panel">
                        <div class="filter-section">
                            <div class="filter-title">
                                <i class="fas fa-eye"></i> 显示筛选
                            </div>
                            <div class="filter-controls">
                                <div class="filter-row">
                                    <select id="depotFilter" class="select-control">
                                        <option value="">所有中转站</option>
                                    </select>
                                    <button class="clear-btn" onclick="clearDepotFilter()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="filter-row">
                                    <select id="routeFilter" class="select-control">
                                        <option value="">所有路线</option>
                                    </select>
                                    <button class="clear-btn" onclick="clearRouteFilter()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="filter-row">
                                    <input type="text" id="accumulationFilter" class="search-input" placeholder="搜索聚集区ID..." />
                                    <button class="clear-btn" onclick="clearAccumulationFilter()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="filter-section">
                            <div class="filter-title">
                                <i class="fas fa-sliders-h"></i> 数值筛选
                            </div>
                            <div class="filter-controls">
                                <div class="filter-row">
                                    <input type="number" id="efficiencyMin" class="search-input" placeholder="最小效率%" min="0" max="100">
                                    <input type="number" id="efficiencyMax" class="search-input" placeholder="最大效率%" min="0" max="100">
                                </div>
                                <div class="filter-row">
                                    <input type="number" id="workTimeMin" class="search-input" placeholder="最小工作时间(分)">
                                    <input type="number" id="workTimeMax" class="search-input" placeholder="最大工作时间(分)">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="controls-section">
                        <div class="control-group">
                            <label class="control-label">显示图层</label>
                            <div class="toggle-group">
                                <button class="toggle-btn active" data-layer="routes">
                                    <i class="fas fa-route"></i> 路线
                                </button>
                                <button class="toggle-btn active" data-layer="convexHulls">
                                    <i class="fas fa-draw-polygon"></i> 凸包
                                </button>
                                <button class="toggle-btn active" data-layer="depots">
                                    <i class="fas fa-warehouse"></i> 中转站
                                </button>
                                <button class="toggle-btn active" data-layer="accumulations">
                                    <i class="fas fa-map-marker-alt"></i> 聚集区
                                </button>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label class="control-label">着色模式</label>
                            <select id="colorMode" class="select-control">
                                <option value="depot">按中转站区分</option>
                                <option value="efficiency">按效率等级</option>
                                <option value="workTime">按工作时间</option>
                                <option value="area">按凸包面积</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 路线分析标签页 -->
                <div class="tab-content" id="routesTab">
                    <div class="depot-groups" id="depotGroups">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 中转站分析标签页 -->
                <div class="tab-content" id="depotsTab">
                    <div class="analysis-section">
                        <div class="chart-container">
                            <div class="chart-title">中转站对比分析</div>
                            <table class="comparison-table" id="depotComparison">
                                <thead>
                                    <tr>
                                        <th>中转站</th>
                                        <th>路线数</th>
                                        <th>聚集区数</th>
                                        <th>平均效率</th>
                                        <th>平均工作时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 统计分析标签页 -->
                <div class="tab-content" id="analysisTab">
                    <div class="analysis-section">
                        <div class="chart-container">
                            <div class="chart-title">效率分布</div>
                            <div id="efficiencyChart" style="height: 200px; display: flex; align-items: end; gap: 4px; padding: 20px 0;"></div>
                        </div>
                        
                        <div class="chart-container">
                            <div class="chart-title">工作时间分布</div>
                            <div id="workTimeChart" style="height: 200px; display: flex; align-items: end; gap: 4px; padding: 20px 0;"></div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">关键指标统计</div>
                            <table class="comparison-table">
                                <tbody id="statsTable">
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="legend">
                    <div class="legend-title">图例说明</div>
                    <div id="legendContent">
                        <!-- 动态生成基于当前筛选状态的图例 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="info-panel" id="infoPanel">
        <div class="info-header">
            <div class="info-title" id="infoTitle"></div>
            <button class="close-btn" onclick="closeInfoPanel()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="info-content" id="infoContent"></div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let routeData = null;
        let filteredRoutes = [];
        let layerGroups = {
            routes: L.layerGroup(),
            convexHulls: L.layerGroup(),
            depots: L.layerGroup(),
            accumulations: L.layerGroup()
        };
        let selectedRoute = null;
        let currentFilter = {
            type: null, // 'depot', 'route', 'accumulation'
            value: null
        };
        
        // 中转站专用颜色方案 - 每个中转站一个主色调
        const depotColorSchemes = {
            1: {
                main: '#ef4444',      // 红色系
                light: '#fca5a5',
                routes: ['#dc2626', '#ef4444', '#f87171', '#fca5a5', '#fecaca']
            },
            2: {
                main: '#3b82f6',      // 蓝色系
                light: '#93c5fd',
                routes: ['#1d4ed8', '#2563eb', '#3b82f6', '#60a5fa', '#93c5fd']
            },
            3: {
                main: '#059669',      // 绿色系
                light: '#6ee7b7',
                routes: ['#047857', '#059669', '#10b981', '#34d399', '#6ee7b7']
            },
            4: {
                main: '#d97706',      // 橙色系
                light: '#fbbf24',
                routes: ['#b45309', '#d97706', '#f59e0b', '#fbbf24', '#fcd34d']
            },
            5: {
                main: '#7c3aed',      // 紫色系
                light: '#a78bfa',
                routes: ['#5b21b6', '#7c3aed', '#8b5cf6', '#a78bfa', '#c4b5fd']
            },
            6: {
                main: '#db2777',      // 粉色系
                light: '#f472b6',
                routes: ['#be185d', '#db2777', '#ec4899', '#f472b6', '#f9a8d4']
            },
            7: {
                main: '#0891b2',      // 青色系
                light: '#67e8f9',
                routes: ['#0e7490', '#0891b2', '#06b6d4', '#22d3ee', '#67e8f9']
            },
            8: {
                main: '#65a30d',      // 草绿色系
                light: '#a3e635',
                routes: ['#4d7c0f', '#65a30d', '#84cc16', '#a3e635', '#bef264']
            }
        };

        const otherColorSchemes = {
            efficiency: ['#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e', '#06d6a0'],
            workTime: ['#22c55e', '#84cc16', '#eab308', '#f97316', '#ef4444', '#dc2626'],
            area: ['#06b6d4', '#0891b2', '#0e7490', '#155e75', '#164e63']
        };

        // 初始化地图
        function initMap() {
            map = L.map('map', {
                zoomControl: false
            }).setView([24.5, 113.5], 10);
            
            L.control.zoom({
                position: 'bottomright'
            }).addTo(map);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            Object.values(layerGroups).forEach(group => group.addTo(map));
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('uploadMessage');
            const className = type === 'error' ? 'error' : 'success';
            messageDiv.innerHTML = `<div class="message ${className}">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'}"></i>
                ${message}
            </div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }

        // 处理文件上传
        function handleFile(file) {
            if (!file) return;
            
            if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                showMessage('请选择JSON文件', 'error');
                return;
            }

            document.getElementById('loading').style.display = 'flex';
            document.getElementById('fileInfo').innerHTML = `
                <i class="fas fa-file-alt"></i>
                ${file.name} (${(file.size / 1024).toFixed(1)} KB)
            `;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    if (!data.routes || !Array.isArray(data.routes)) {
                        throw new Error('JSON格式不正确：缺少routes数组');
                    }

                    routeData = data;
                    filteredRoutes = [...data.routes];
                    processData();
                    showMessage('文件加载成功！数据分析已完成', 'success');
                    document.getElementById('tabsContainer').style.display = 'flex';
                } catch (error) {
                    showMessage(`文件解析失败: ${error.message}`, 'error');
                    console.error('JSON解析错误:', error);
                } finally {
                    document.getElementById('loading').style.display = 'none';
                }
            };

            reader.onerror = function() {
                showMessage('文件读取失败', 'error');
                document.getElementById('loading').style.display = 'none';
            };

            reader.readAsText(file);
        }

        // 获取路线颜色
        function getRouteColor(route, mode, routeIndex = 0) {
            const depotId = route.transitDepotId;
            const depotScheme = depotColorSchemes[depotId] || depotColorSchemes[1];
            
            switch(mode) {
                case 'depot':
                    return depotScheme.routes[routeIndex % depotScheme.routes.length];
                    
                case 'efficiency':
                    const efficiencyLevel = Math.min(Math.floor(route.workEfficiency * 6), 5);
                    return otherColorSchemes.efficiency[efficiencyLevel];
                    
                case 'workTime':
                    const maxTime = Math.max(...filteredRoutes.map(r => r.totalWorkTime));
                    const timeLevel = Math.min(Math.floor((route.totalWorkTime / maxTime) * 6), 5);
                    return otherColorSchemes.workTime[timeLevel];
                    
                case 'area':
                    const maxArea = Math.max(...filteredRoutes.map(r => r.convexHullArea));
                    const areaLevel = Math.min(Math.floor((route.convexHullArea / maxArea) * 5), 4);
                    return otherColorSchemes.area[areaLevel];
                    
                default:
                    return '#3b82f6';
            }
        }

        // 获取中转站颜色
        function getDepotColor(depotId) {
            const depotScheme = depotColorSchemes[depotId] || depotColorSchemes[1];
            return depotScheme.main;
        }

        // 获取聚集区颜色 (根据所属中转站)
        function getAccumulationColor(depotId) {
            const depotScheme = depotColorSchemes[depotId] || depotColorSchemes[1];
            return depotScheme.light;
        }

        // 渲染路线
        function renderRoutes(colorMode = 'depot') {
            layerGroups.routes.clearLayers();
            
            const routesByDepot = {};
            filteredRoutes.forEach(route => {
                if (!routesByDepot[route.transitDepotId]) {
                    routesByDepot[route.transitDepotId] = [];
                }
                routesByDepot[route.transitDepotId].push(route);
            });

            // 渲染每条路线
            Object.values(routesByDepot).forEach(depotRoutes => {
                depotRoutes.forEach((route, index) => {
                    const color = getRouteColor(route, colorMode, index);
                    const polylineCoords = route.polyline.map(point => [point.latitude, point.longitude]);
                    
                    const polyline = L.polyline(polylineCoords, {
                        color: color,
                        weight: 4,
                        opacity: 0.8,
                        className: `route-${route.routeId}`,
                        routeId: route.routeId,
                        depotId: route.transitDepotId
                    }).addTo(layerGroups.routes);

                    polyline.bindPopup(`
                        <div style="font-family: sans-serif; max-width: 250px;">
                            <h4 style="margin: 0 0 15px 0; color: #1e293b; padding-bottom: 10px; border-bottom: 2px solid #e2e8f0;">
                                <i class="fas fa-route"></i> ${route.routeName}
                            </h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                                <div style="text-align: center; padding: 8px; background: #f8fafc; border-radius: 6px;">
                                    <div style="font-size: 18px; font-weight: bold; color: #3b82f6;">${route.accumulationCount}</div>
                                    <div style="font-size: 11px; color: #64748b;">聚集区</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: #f8fafc; border-radius: 6px;">
                                    <div style="font-size: 18px; font-weight: bold; color: #059669;">${(route.workEfficiency * 100).toFixed(1)}%</div>
                                    <div style="font-size: 11px; color: #64748b;">效率</div>
                                </div>
                            </div>
                            <div style="font-size: 13px; line-height: 1.6;">
                                <p style="margin: 5px 0;"><strong>工作时间:</strong> ${route.totalWorkTime.toFixed(1)} 分钟</p>
                                <p style="margin: 5px 0;"><strong>平均卸货时间:</strong> ${route.averageDeliveryTime.toFixed(1)} 分钟/点</p>
                                <p style="margin: 5px 0;"><strong>服务面积:</strong> ${route.convexHullArea.toFixed(4)} 平方度</p>
                            </div>
                        </div>
                    `);

                    polyline.on('click', () => selectRoute(route));
                    
                    polyline.on('mouseover', function() {
                        if (selectedRoute?.routeId !== route.routeId) {
                            this.setStyle({weight: 6, opacity: 1});
                        }
                    });

                    polyline.on('mouseout', function() {
                        if (selectedRoute?.routeId !== route.routeId) {
                            this.setStyle({weight: 4, opacity: 0.8});
                        }
                    });
                });
            });
        }

        // 渲染凸包
        function renderConvexHulls(colorMode = 'depot') {
            layerGroups.convexHulls.clearLayers();
            
            const routesByDepot = {};
            filteredRoutes.forEach(route => {
                if (!routesByDepot[route.transitDepotId]) {
                    routesByDepot[route.transitDepotId] = [];
                }
                routesByDepot[route.transitDepotId].push(route);
            });

            Object.values(routesByDepot).forEach(depotRoutes => {
                depotRoutes.forEach((route, index) => {
                    if (route.convexHull && route.convexHull.length > 0) {
                        const color = getRouteColor(route, colorMode, index);
                        const hullCoords = route.convexHull.map(point => [point.latitude, point.longitude]);
                        
                        const polygon = L.polygon(hullCoords, {
                            color: color,
                            weight: 2,
                            fillColor: color,
                            fillOpacity: 0.15,
                            className: `hull-${route.routeId}`,
                            routeId: route.routeId,
                            depotId: route.transitDepotId
                        }).addTo(layerGroups.convexHulls);

                        polygon.bindPopup(`
                            <div style="font-family: sans-serif;">
                                <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                    <i class="fas fa-draw-polygon"></i> ${route.routeName} - 服务区域
                                </h4>
                                <p><strong>覆盖面积:</strong> ${route.convexHullArea.toFixed(6)} 平方度</p>
                                <p><strong>边界点数:</strong> ${route.convexHull.length}</p>
                                <p><strong>点位密度:</strong> ${(route.accumulationCount / route.convexHullArea).toFixed(2)} 点/平方度</p>
                            </div>
                        `);
                    }
                });
            });
        }

        // 渲染中转站 (使用中转站专用颜色)
        function renderDepots() {
            layerGroups.depots.clearLayers();
            
            const depots = {};
            filteredRoutes.forEach(route => {
                if (!depots[route.transitDepotId]) {
                    const firstPoint = route.polyline[0];
                    depots[route.transitDepotId] = {
                        id: route.transitDepotId,
                        position: [firstPoint.latitude, firstPoint.longitude],
                        routes: []
                    };
                }
                depots[route.transitDepotId].routes.push(route);
            });

            Object.values(depots).forEach(depot => {
                const avgEfficiency = depot.routes.reduce((sum, r) => sum + r.workEfficiency, 0) / depot.routes.length;
                const totalAccumulations = depot.routes.reduce((sum, r) => sum + r.accumulationCount, 0);
                
                // 使用中转站专用颜色
                const color = getDepotColor(depot.id);
                const radius = 14 + (avgEfficiency * 6);

                const marker = L.circleMarker(depot.position, {
                    radius: radius,
                    fillColor: color,
                    color: 'white',
                    weight: 3,
                    opacity: 1,
                    fillOpacity: 0.9,
                    className: `depot-${depot.id}`,
                    depotId: depot.id
                }).addTo(layerGroups.depots);

                marker.bindPopup(`
                    <div style="font-family: sans-serif;">
                        <h4 style="margin: 0 0 15px 0; color: #1e293b; text-align: center;">
                            <i class="fas fa-warehouse"></i> 中转站 ${depot.id}
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                            <div style="text-align: center; padding: 10px; background: #f8fafc; border-radius: 6px;">
                                <div style="font-size: 20px; font-weight: bold; color: #3b82f6;">${depot.routes.length}</div>
                                <div style="font-size: 11px; color: #64748b;">路线数</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: #f8fafc; border-radius: 6px;">
                                <div style="font-size: 20px; font-weight: bold; color: #059669;">${totalAccumulations}</div>
                                <div style="font-size: 11px; color: #64748b;">聚集区</div>
                            </div>
                        </div>
                        <p><strong>平均效率:</strong> ${(avgEfficiency * 100).toFixed(2)}%</p>
                        <p><strong>总工作时间:</strong> ${depot.routes.reduce((sum, r) => sum + r.totalWorkTime, 0).toFixed(1)} 分钟</p>
                    </div>
                `);

                // 添加点击事件进行筛选
                marker.on('click', () => {
                    filterByDepot(depot.id);
                });
            });
        }

        // 渲染聚集区 (使用中转站相关的浅色)
        function renderAccumulations() {
            layerGroups.accumulations.clearLayers();
            
            filteredRoutes.forEach(route => {
                route.polyline.forEach((point, index) => {
                    if (index > 0 && index < route.polyline.length - 1) {
                        const progress = index / (route.polyline.length - 1);
                        const color = getAccumulationColor(route.transitDepotId);
                        const alpha = 0.7 + (progress * 0.3);
                        
                        const marker = L.circleMarker([point.latitude, point.longitude], {
                            radius: 6,
                            fillColor: color,
                            color: 'white',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: alpha,
                            className: `accumulation-${route.accumulationSequence[index-1]}`,
                            accumulationId: route.accumulationSequence[index-1],
                            routeId: route.routeId,
                            depotId: route.transitDepotId
                        }).addTo(layerGroups.accumulations);

                        marker.bindPopup(`
                            <div style="font-family: sans-serif;">
                                <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                    <i class="fas fa-map-marker-alt"></i> 聚集区 ${route.accumulationSequence[index-1]}
                                </h4>
                                <p><strong>所属路线:</strong> ${route.routeName}</p>
                                <p><strong>中转站:</strong> ${route.transitDepotId}</p>
                                <p><strong>访问顺序:</strong> 第 ${index} 站</p>
                                <p><strong>坐标:</strong> ${point.latitude.toFixed(6)}, ${point.longitude.toFixed(6)}</p>
                                <p><strong>路线进度:</strong> ${(progress * 100).toFixed(1)}%</p>
                            </div>
                        `);

                        // 添加点击事件进行筛选
                        marker.on('click', () => {
                            filterByAccumulation(route.accumulationSequence[index-1]);
                        });
                    }
                });
            });
        }

        // 按中转站筛选
        function filterByDepot(depotId) {
            currentFilter = { type: 'depot', value: depotId };
            
            // 更新筛选器UI
            document.getElementById('depotFilter').value = depotId;
            
            // 筛选路线
            filteredRoutes = routeData.routes.filter(route => route.transitDepotId == depotId);
            
            // 重新渲染
            rerenderAll();
            
            // 显示筛选指示器
            showFilterIndicator(`显示中转站 ${depotId} 的路线`);
            
            // 高亮相关UI元素
            highlightDepotGroup(depotId);
        }

        // 按路线筛选
        function filterByRoute(routeId) {
            currentFilter = { type: 'route', value: routeId };
            
            // 更新筛选器UI
            document.getElementById('routeFilter').value = routeId;
            
            // 筛选路线
            filteredRoutes = routeData.routes.filter(route => route.routeId == routeId);
            
            // 重新渲染
            rerenderAll();
            
            // 获取路线名称
            const route = routeData.routes.find(r => r.routeId == routeId);
            showFilterIndicator(`显示路线: ${route ? route.routeName : routeId}`);
            
            // 高亮路线
            if (route) {
                selectRoute(route);
            }
        }

        // 按聚集区筛选
        function filterByAccumulation(accumulationId) {
            currentFilter = { type: 'accumulation', value: accumulationId };
            
            // 更新筛选器UI
            document.getElementById('accumulationFilter').value = accumulationId;
            
            // 筛选包含该聚集区的路线
            filteredRoutes = routeData.routes.filter(route => 
                route.accumulationSequence.includes(parseInt(accumulationId))
            );
            
            // 重新渲染
            rerenderAll();
            
            showFilterIndicator(`显示包含聚集区 ${accumulationId} 的路线`);
            
            // 高亮聚集区
            highlightAccumulation(accumulationId);
        }

        // 清除筛选
        function clearAllFilters() {
            currentFilter = { type: null, value: null };
            filteredRoutes = [...routeData.routes];
            
            // 清除筛选器UI
            document.getElementById('depotFilter').value = '';
            document.getElementById('routeFilter').value = '';
            document.getElementById('accumulationFilter').value = '';
            
            // 重新渲染
            rerenderAll();
            
            // 隐藏筛选指示器
            hideFilterIndicator();
            
            // 清除高亮
            clearAllHighlights();
        }

        // 单独清除函数
        function clearDepotFilter() {
            document.getElementById('depotFilter').value = '';
            if (currentFilter.type === 'depot') {
                clearAllFilters();
            }
        }

        function clearRouteFilter() {
            document.getElementById('routeFilter').value = '';
            if (currentFilter.type === 'route') {
                clearAllFilters();
            }
        }

        function clearAccumulationFilter() {
            document.getElementById('accumulationFilter').value = '';
            if (currentFilter.type === 'accumulation') {
                clearAllFilters();
            }
        }

        // 显示筛选指示器
        function showFilterIndicator(text) {
            const indicator = document.getElementById('filterIndicator');
            document.getElementById('filterText').textContent = text;
            indicator.classList.add('active');
        }

        // 隐藏筛选指示器
        function hideFilterIndicator() {
            document.getElementById('filterIndicator').classList.remove('active');
        }

        // 高亮中转站分组
        function highlightDepotGroup(depotId) {
            // 清除之前的高亮
            document.querySelectorAll('.depot-group').forEach(group => {
                group.classList.remove('highlighted');
            });
            
            // 高亮指定中转站
            const depotGroup = document.querySelector(`[data-depot="${depotId}"]`);
            if (depotGroup) {
                depotGroup.classList.add('highlighted');
                depotGroup.classList.add('expanded');
            }
        }

        // 高亮聚集区
        function highlightAccumulation(accumulationId) {
            // 地图上的高亮通过CSS类实现
            map.eachLayer(layer => {
                if (layer.options && layer.options.accumulationId == accumulationId) {
                    layer.setStyle({
                        radius: 10,
                        weight: 4,
                        fillOpacity: 1
                    });
                }
            });
        }

        // 清除所有高亮
        function clearAllHighlights() {
            document.querySelectorAll('.depot-group').forEach(group => {
                group.classList.remove('highlighted');
            });
            document.querySelectorAll('.route-item').forEach(item => {
                item.classList.remove('highlighted');
            });
        }

        // 重新渲染所有图层
        function rerenderAll() {
            const colorMode = document.getElementById('colorMode').value;
            renderRoutes(colorMode);
            renderConvexHulls(colorMode);
            renderDepots();
            renderAccumulations();
            
            // 更新UI
            generateDepotGroups();
            updateOverviewStats();
            updateLegend();
            
            // 调整地图视野
            adjustMapView();
        }

        // 调整地图视野到筛选结果
        function adjustMapView() {
            if (filteredRoutes.length === 0) return;
            
            const allPoints = [];
            filteredRoutes.forEach(route => {
                route.polyline.forEach(point => {
                    allPoints.push([point.latitude, point.longitude]);
                });
            });
            
            if (allPoints.length > 0) {
                const bounds = L.latLngBounds(allPoints);
                map.fitBounds(bounds, { padding: [20, 20] });
            }
        }

        // 选择路线 (增强高亮效果)
        function selectRoute(route) {
            selectedRoute = route;
            
            // 清除之前的选中状态
            document.querySelectorAll('.route-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加新的选中状态
            const routeItem = document.querySelector(`[data-route-id="${route.routeId}"]`);
            if (routeItem) {
                routeItem.classList.add('selected');
            }

            // 高亮地图上的路线 - 使选中路线突出显示
            highlightSelectedRoute(route);

            // 显示详细信息
            showRouteInfo(route);
        }

        // 高亮选中路线 (让其他路线变淡)
        function highlightSelectedRoute(route) {
            map.eachLayer(layer => {
                if (layer.options && layer.options.routeId) {
                    if (layer.options.routeId === route.routeId) {
                        // 选中路线：增加宽度，提高透明度，提升层级
                        layer.setStyle({
                            weight: 8,
                            opacity: 1,
                            zIndexOffset: 1000
                        });
                        layer.bringToFront();
                    } else {
                        // 其他路线：降低透明度
                        layer.setStyle({
                            weight: 3,
                            opacity: 0.3,
                            zIndexOffset: 0
                        });
                    }
                }
            });

            // 同样处理凸包
            map.eachLayer(layer => {
                if (layer.options && layer.options.className && layer.options.className.startsWith('hull-')) {
                    if (layer.options.routeId === route.routeId) {
                        layer.setStyle({
                            weight: 4,
                            opacity: 1,
                            fillOpacity: 0.3
                        });
                        layer.bringToFront();
                    } else {
                        layer.setStyle({
                            weight: 1,
                            opacity: 0.2,
                            fillOpacity: 0.1
                        });
                    }
                }
            });

            // 高亮相关聚集区和中转站
            map.eachLayer(layer => {
                if (layer.options) {
                    if (layer.options.depotId === route.transitDepotId) {
                        if (layer.options.className && layer.options.className.startsWith('depot-')) {
                            // 中转站闪烁效果
                            layer.setStyle({
                                radius: layer.options.radius + 4,
                                opacity: 1,
                                fillOpacity: 1
                            });
                        } else if (layer.options.routeId === route.routeId) {
                            // 该路线的聚集区高亮
                            layer.setStyle({
                                radius: 8,
                                weight: 3,
                                opacity: 1,
                                fillOpacity: 1
                            });
                        }
                    } else if (layer.options.className && (
                        layer.options.className.startsWith('accumulation-') || 
                        layer.options.className.startsWith('depot-')
                    )) {
                        // 其他中转站和聚集区变淡
                        layer.setStyle({
                            opacity: 0.3,
                            fillOpacity: 0.3
                        });
                    }
                }
            });
        }

        // 通过ID选择路线
        function selectRouteById(routeId) {
            const route = filteredRoutes.find(r => r.routeId === routeId);
            if (route) {
                selectRoute(route);
            }
        }

        // 显示路线详细信息
        function showRouteInfo(route) {
            const infoPanel = document.getElementById('infoPanel');
            const infoTitle = document.getElementById('infoTitle');
            const infoContent = document.getElementById('infoContent');

            infoTitle.innerHTML = `<i class="fas fa-route"></i> ${route.routeName}`;
            infoContent.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div style="text-align: center; padding: 15px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
                        <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">${route.accumulationCount}</div>
                        <div style="font-size: 12px; color: #64748b;">聚集区数量</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #059669;">
                        <div style="font-size: 24px; font-weight: bold; color: #059669;">${(route.workEfficiency * 100).toFixed(1)}%</div>
                        <div style="font-size: 12px; color: #64748b;">工作效率</div>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px; color: #374151;">
                        <i class="fas fa-clock"></i> 时间统计
                    </h4>
                    <p style="margin: 8px 0;"><strong>总工作时间:</strong> ${route.totalWorkTime.toFixed(2)} 分钟</p>
                    <p style="margin: 8px 0;"><strong>平均卸货时间:</strong> ${route.averageDeliveryTime.toFixed(2)} 分钟/点</p>
                    <p style="margin: 8px 0;"><strong>时间效率:</strong> ${((route.accumulationCount / route.totalWorkTime) * 60).toFixed(2)} 点/小时</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px; color: #374151;">
                        <i class="fas fa-map"></i> 区域信息
                    </h4>
                    <p style="margin: 8px 0;"><strong>服务面积:</strong> ${route.convexHullArea.toFixed(6)} 平方度</p>
                    <p style="margin: 8px 0;"><strong>点位密度:</strong> ${(route.accumulationCount / route.convexHullArea).toFixed(2)} 点/平方度</p>
                    <p style="margin: 8px 0;"><strong>中转站:</strong> ${route.transitDepotId}</p>
                </div>

                <div>
                    <h4 style="margin-bottom: 10px; color: #374151;">
                        <i class="fas fa-list"></i> 聚集区序列
                    </h4>
                    <div style="max-height: 150px; overflow-y: auto; font-size: 11px; background: #f8fafc; padding: 12px; border-radius: 6px; border: 1px solid #e2e8f0;">
                        ${route.accumulationSequence.map((id, index) => 
                            `<span style="display: inline-block; margin: 2px 4px; padding: 2px 6px; background: white; border-radius: 4px; border: 1px solid #d1d5db; cursor: pointer;" onclick="filterByAccumulation(${id})">
                                ${index + 1}. ${id}
                            </span>`
                        ).join('')}
                    </div>
                </div>
            `;

            infoPanel.style.display = 'block';
        }

        // 关闭信息面板
        function closeInfoPanel() {
            document.getElementById('infoPanel').style.display = 'none';
            
            // 恢复所有路线的正常显示
            if (selectedRoute) {
                map.eachLayer(layer => {
                    if (layer.options && layer.options.routeId) {
                        layer.setStyle({
                            weight: 4,
                            opacity: 0.8,
                            zIndexOffset: 0
                        });
                    }
                    if (layer.options && layer.options.className && layer.options.className.startsWith('hull-')) {
                        layer.setStyle({
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.15
                        });
                    }
                    if (layer.options && (
                        layer.options.className?.startsWith('accumulation-') || 
                        layer.options.className?.startsWith('depot-')
                    )) {
                        // 恢复聚集区和中转站的正常显示
                        if (layer.options.className.startsWith('depot-')) {
                            layer.setStyle({
                                opacity: 1,
                                fillOpacity: 0.9
                            });
                        } else {
                            layer.setStyle({
                                radius: 6,
                                weight: 2,
                                opacity: 1,
                                fillOpacity: layer.options.fillOpacity || 0.7
                            });
                        }
                    }
                });
            }
            
            selectedRoute = null;
            
            // 清除选中状态
            document.querySelectorAll('.route-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 生成中转站分组
        function generateDepotGroups() {
            const depotGroups = document.getElementById('depotGroups');
            const depots = {};

            filteredRoutes.forEach(route => {
                if (!depots[route.transitDepotId]) {
                    depots[route.transitDepotId] = {
                        id: route.transitDepotId,
                        routes: []
                    };
                }
                depots[route.transitDepotId].routes.push(route);
            });

            depotGroups.innerHTML = Object.values(depots).map(depot => {
                const avgEfficiency = depot.routes.reduce((sum, r) => sum + r.workEfficiency, 0) / depot.routes.length;
                const totalTime = depot.routes.reduce((sum, r) => sum + r.totalWorkTime, 0);
                const totalAccumulations = depot.routes.reduce((sum, r) => sum + r.accumulationCount, 0);
                const depotColor = getDepotColor(depot.id);

                return `
                    <div class="depot-group" data-depot="${depot.id}">
                        <div class="depot-header" onclick="toggleDepotGroup(${depot.id})">
                            <div class="depot-info">
                                <div class="depot-icon" style="background: ${depotColor};"></div>
                                <div>
                                    <div style="font-weight: 700;">中转站 ${depot.id}</div>
                                    <div class="depot-stats">${depot.routes.length} 条路线 • ${totalAccumulations} 个聚集区 • 效率 ${(avgEfficiency * 100).toFixed(1)}%</div>
                                </div>
                            </div>
                            <div class="expand-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="routes-list">
                            <div style="padding: 10px 20px; background: #f8fafc; border-bottom: 1px solid #e2e8f0; font-size: 12px; color: #64748b;">
                                <button onclick="filterByDepot(${depot.id})" style="padding: 5px 10px; background: ${depotColor}; color: white; border: none; border-radius: 15px; font-size: 11px; cursor: pointer;">
                                    <i class="fas fa-filter"></i> 只看此中转站
                                </button>
                            </div>
                            ${depot.routes.map((route, index) => {
                                const routeColor = getRouteColor(route, 'depot', index);
                                return `
                                    <div class="route-item" data-route-id="${route.routeId}" onclick="selectRouteById(${route.routeId})">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div style="width: 12px; height: 4px; background: ${routeColor}; border-radius: 2px;"></div>
                                            <div class="route-name">${route.routeName}</div>
                                        </div>
                                        <div style="font-size: 12px; color: #64748b; margin: 5px 0;">
                                            ${route.accumulationCount} 个聚集区 • 面积 ${route.convexHullArea.toFixed(4)} 平方度
                                        </div>
                                        <div class="route-metrics">
                                            <div class="metric">
                                                <div class="metric-value">${route.totalWorkTime.toFixed(0)}</div>
                                                <div class="metric-label">工作时间(分)</div>
                                            </div>
                                            <div class="metric">
                                                <div class="metric-value">${(route.workEfficiency * 100).toFixed(1)}%</div>
                                                <div class="metric-label">效率</div>
                                            </div>
                                            <div class="metric">
                                                <div class="metric-value">${route.averageDeliveryTime.toFixed(1)}</div>
                                                <div class="metric-label">卸货时间/点</div>
                                            </div>
                                        </div>
                                        <div style="margin-top: 10px;">
                                            <button onclick="event.stopPropagation(); filterByRoute(${route.routeId})" style="padding: 3px 8px; background: #3b82f6; color: white; border: none; border-radius: 12px; font-size: 10px; cursor: pointer;">
                                                <i class="fas fa-search"></i> 只看此路线
                                            </button>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 切换中转站分组展开/收起
        function toggleDepotGroup(depotId) {
            const group = document.querySelector(`[data-depot="${depotId}"]`);
            group.classList.toggle('expanded');
        }

        // 更新图例
        function updateLegend() {
            const legendContent = document.getElementById('legendContent');
            const depots = {};
            
            filteredRoutes.forEach(route => {
                if (!depots[route.transitDepotId]) {
                    depots[route.transitDepotId] = true;
                }
            });

            let legendHTML = '';

            // 中转站图例
            Object.keys(depots).forEach(depotId => {
                const color = getDepotColor(depotId);
                legendHTML += `
                    <div class="legend-item">
                        <div class="legend-marker" style="background: ${color};"></div>
                        <span>中转站 ${depotId}</span>
                    </div>
                `;
            });

            // 通用图例
            legendHTML += `
                <div class="legend-item">
                    <div class="legend-color" style="background: #3b82f6;"></div>
                    <span>路线轨迹</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: rgba(59, 130, 246, 0.2); border: 1px solid #3b82f6;"></div>
                    <span>服务区域</span>
                </div>
            `;

            // 聚集区图例
            Object.keys(depots).forEach(depotId => {
                const color = getAccumulationColor(depotId);
                legendHTML += `
                    <div class="legend-item">
                        <div class="legend-marker" style="background: ${color};"></div>
                        <span>中转站${depotId}聚集区</span>
                    </div>
                `;
            });

            legendContent.innerHTML = legendHTML;
        }

        // 更新概览统计
        function updateOverviewStats() {
            const totalRoutes = filteredRoutes.length;
            const totalDepots = new Set(filteredRoutes.map(r => r.transitDepotId)).size;
            const totalAccumulations = filteredRoutes.reduce((sum, r) => sum + r.accumulationCount, 0);
            const avgEfficiency = filteredRoutes.length > 0 ? 
                filteredRoutes.reduce((sum, r) => sum + r.workEfficiency, 0) / totalRoutes : 0;

            document.getElementById('totalRoutes').textContent = totalRoutes;
            document.getElementById('totalDepots').textContent = totalDepots;
            document.getElementById('totalAccumulations').textContent = totalAccumulations;
            document.getElementById('avgEfficiency').textContent = (avgEfficiency * 100).toFixed(1) + '%';
        }

        // 生成筛选器选项
        function generateFilterOptions() {
            const depotFilter = document.getElementById('depotFilter');
            const routeFilter = document.getElementById('routeFilter');

            // 生成中转站选项
            const depots = [...new Set(routeData.routes.map(r => r.transitDepotId))].sort((a, b) => a - b);
            depotFilter.innerHTML = '<option value="">所有中转站</option>' + 
                depots.map(depotId => `<option value="${depotId}">中转站 ${depotId}</option>`).join('');

            // 生成路线选项
            routeFilter.innerHTML = '<option value="">所有路线</option>' + 
                routeData.routes.map(route => `<option value="${route.routeId}">${route.routeName}</option>`).join('');
        }

        // 处理数据并渲染
        function processData() {
            Object.values(layerGroups).forEach(group => group.clearLayers());

            renderRoutes();
            renderConvexHulls();
            renderDepots();
            renderAccumulations();

            generateFilterOptions();
            generateDepotGroups();
            updateOverviewStats();
            updateLegend();

            adjustMapView();
        }

        // 切换图层显示
        function toggleLayer(layerName) {
            const button = document.querySelector(`[data-layer="${layerName}"]`);
            button.classList.toggle('active');
            
            if (button.classList.contains('active')) {
                map.addLayer(layerGroups[layerName]);
            } else {
                map.removeLayer(layerGroups[layerName]);
            }
        }

        // 更新颜色模式
        function updateColorMode() {
            if (!routeData) return;
            const colorMode = document.getElementById('colorMode').value;
            renderRoutes(colorMode);
            renderConvexHulls(colorMode);
        }

        // 切换标签页
        function switchTab(tabName) {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}Tab`).classList.add('active');

            if (tabName === 'analysis') {
                generateCharts();
                generateStatsTable();
            } else if (tabName === 'depots') {
                generateDepotComparison();
            }
        }

        // 生成中转站对比分析
        function generateDepotComparison() {
            const table = document.getElementById('depotComparison').querySelector('tbody');
            const depots = {};

            filteredRoutes.forEach(route => {
                if (!depots[route.transitDepotId]) {
                    depots[route.transitDepotId] = {
                        id: route.transitDepotId,
                        routes: []
                    };
                }
                depots[route.transitDepotId].routes.push(route);
            });

            table.innerHTML = Object.values(depots).map(depot => {
                const avgEfficiency = depot.routes.reduce((sum, r) => sum + r.workEfficiency, 0) / depot.routes.length;
                const avgWorkTime = depot.routes.reduce((sum, r) => sum + r.totalWorkTime, 0) / depot.routes.length;
                const totalAccumulations = depot.routes.reduce((sum, r) => sum + r.accumulationCount, 0);
                const depotColor = getDepotColor(depot.id);

                return `
                    <tr onclick="filterByDepot(${depot.id})" style="cursor: pointer;">
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 12px; height: 12px; background: ${depotColor}; border-radius: 50%;"></div>
                                <strong>中转站 ${depot.id}</strong>
                            </div>
                        </td>
                        <td>${depot.routes.length}</td>
                        <td>${totalAccumulations}</td>
                        <td>${(avgEfficiency * 100).toFixed(2)}%</td>
                        <td>${avgWorkTime.toFixed(1)} 分钟</td>
                    </tr>
                `;
            }).join('');
        }

        // 生成图表和统计表格的函数 (保持原有逻辑)
        function generateCharts() {
            // 效率分布图
            const efficiencyChart = document.getElementById('efficiencyChart');
            const efficiencyData = Array(6).fill(0);
            
            filteredRoutes.forEach(route => {
                const level = Math.min(Math.floor(route.workEfficiency * 6), 5);
                efficiencyData[level]++;
            });

            const maxCount = Math.max(...efficiencyData) || 1;
            efficiencyChart.innerHTML = efficiencyData.map((count, index) => {
                const height = (count / maxCount) * 100;
                const label = `${(index * 16.7).toFixed(0)}-${((index + 1) * 16.7).toFixed(0)}%`;
                return `
                    <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 30px; background: ${otherColorSchemes.efficiency[index]}; height: ${height}%; margin-bottom: 10px; border-radius: 4px 4px 0 0;"></div>
                        <div style="font-size: 10px; color: #64748b; writing-mode: vertical-rl; text-orientation: mixed;">${label}</div>
                        <div style="font-size: 12px; font-weight: bold; margin-top: 5px;">${count}</div>
                    </div>
                `;
            }).join('');

            // 工作时间分布图
            const workTimeChart = document.getElementById('workTimeChart');
            if (filteredRoutes.length > 0) {
                const times = filteredRoutes.map(r => r.totalWorkTime).sort((a, b) => a - b);
                const minTime = Math.min(...times);
                const maxTime = Math.max(...times);
                const timeRange = (maxTime - minTime) / 6;

                const timeData = Array(6).fill(0);
                filteredRoutes.forEach(route => {
                    const level = Math.min(Math.floor((route.totalWorkTime - minTime) / timeRange), 5);
                    timeData[level]++;
                });

                const maxTimeCount = Math.max(...timeData) || 1;
                workTimeChart.innerHTML = timeData.map((count, index) => {
                    const height = (count / maxTimeCount) * 100;
                    const rangeStart = (minTime + index * timeRange).toFixed(0);
                    const rangeEnd = (minTime + (index + 1) * timeRange).toFixed(0);
                    return `
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                            <div style="width: 30px; background: ${otherColorSchemes.workTime[5-index]}; height: ${height}%; margin-bottom: 10px; border-radius: 4px 4px 0 0;"></div>
                            <div style="font-size: 10px; color: #64748b; writing-mode: vertical-rl; text-orientation: mixed;">${rangeStart}-${rangeEnd}分</div>
                            <div style="font-size: 12px; font-weight: bold; margin-top: 5px;">${count}</div>
                        </div>
                    `;
                }).join('');
            }
        }

        function generateStatsTable() {
            const table = document.getElementById('statsTable');
            
            if (filteredRoutes.length === 0) {
                table.innerHTML = '<tr><td colspan="2">无数据</td></tr>';
                return;
            }

            const totalTime = filteredRoutes.reduce((sum, r) => sum + r.totalWorkTime, 0);
            const avgEfficiency = filteredRoutes.reduce((sum, r) => sum + r.workEfficiency, 0) / filteredRoutes.length;
            const totalArea = filteredRoutes.reduce((sum, r) => sum + r.convexHullArea, 0);
            const avgDeliveryTime = filteredRoutes.reduce((sum, r) => sum + r.averageDeliveryTime, 0) / filteredRoutes.length;
            const maxTime = Math.max(...filteredRoutes.map(r => r.totalWorkTime));
            const minTime = Math.min(...filteredRoutes.map(r => r.totalWorkTime));

            table.innerHTML = `
                <tr><td><strong>总工作时间</strong></td><td>${totalTime.toFixed(1)} 分钟</td></tr>
                <tr><td><strong>平均效率</strong></td><td>${(avgEfficiency * 100).toFixed(2)}%</td></tr>
                <tr><td><strong>总服务面积</strong></td><td>${totalArea.toFixed(4)} 平方度</td></tr>
                <tr><td><strong>平均卸货时间</strong></td><td>${avgDeliveryTime.toFixed(1)} 分钟/点</td></tr>
                <tr><td><strong>最长工作时间</strong></td><td>${maxTime.toFixed(1)} 分钟</td></tr>
                <tr><td><strong>最短工作时间</strong></td><td>${minTime.toFixed(1)} 分钟</td></tr>
                <tr><td><strong>时间差距</strong></td><td>${(maxTime - minTime).toFixed(1)} 分钟</td></tr>
            `;
        }

        // 初始化文件上传
        function initFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');

            fileInput.addEventListener('change', function(e) {
                handleFile(e.target.files[0]);
            });

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFile(e.dataTransfer.files[0]);
            });
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 初始化
        function init() {
            initMap();
            initFileUpload();

            // 绑定标签页切换事件
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    switchTab(btn.dataset.tab);
                });
            });

            // 绑定控制事件
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    toggleLayer(btn.dataset.layer);
                });
            });

            document.getElementById('colorMode').addEventListener('change', updateColorMode);

            // 绑定筛选器事件
            document.getElementById('depotFilter').addEventListener('change', function() {
                const value = this.value;
                if (value) {
                    filterByDepot(value);
                } else if (currentFilter.type === 'depot') {
                    clearAllFilters();
                }
            });

            document.getElementById('routeFilter').addEventListener('change', function() {
                const value = this.value;
                if (value) {
                    filterByRoute(value);
                } else if (currentFilter.type === 'route') {
                    clearAllFilters();
                }
            });

            document.getElementById('accumulationFilter').addEventListener('input', debounce(function() {
                const value = this.value.trim();
                if (value) {
                    filterByAccumulation(value);
                } else if (currentFilter.type === 'accumulation') {
                    clearAllFilters();
                }
            }, 500));

            // 绑定数值筛选器 (保持原有逻辑)
            ['efficiencyMin', 'efficiencyMax', 'workTimeMin', 'workTimeMax'].forEach(inputId => {
                document.getElementById(inputId).addEventListener('input', debounce(() => {
                    applyRangeFilters();
                }, 500));
            });

            // 点击筛选指示器清除筛选
            document.getElementById('filterIndicator').addEventListener('click', clearAllFilters);
        }

        // 应用数值范围筛选
        function applyRangeFilters() {
            const efficiencyMin = document.getElementById('efficiencyMin').value;
            const efficiencyMax = document.getElementById('efficiencyMax').value;
            const workTimeMin = document.getElementById('workTimeMin').value;
            const workTimeMax = document.getElementById('workTimeMax').value;

            // 如果有其他筛选条件，先应用其他筛选
            let baseRoutes = routeData.routes;
            if (currentFilter.type && currentFilter.value) {
                switch (currentFilter.type) {
                    case 'depot':
                        baseRoutes = routeData.routes.filter(route => route.transitDepotId == currentFilter.value);
                        break;
                    case 'route':
                        baseRoutes = routeData.routes.filter(route => route.routeId == currentFilter.value);
                        break;
                    case 'accumulation':
                        baseRoutes = routeData.routes.filter(route => 
                            route.accumulationSequence.includes(parseInt(currentFilter.value))
                        );
                        break;
                }
            }

            // 应用数值筛选
            filteredRoutes = baseRoutes.filter(route => {
                const efficiency = route.workEfficiency * 100;
                const workTime = route.totalWorkTime;

                let passes = true;

                if (efficiencyMin && efficiency < parseFloat(efficiencyMin)) passes = false;
                if (efficiencyMax && efficiency > parseFloat(efficiencyMax)) passes = false;
                if (workTimeMin && workTime < parseFloat(workTimeMin)) passes = false;
                if (workTimeMax && workTime > parseFloat(workTimeMax)) passes = false;

                return passes;
            });

            rerenderAll();
        }

        // 启动应用
        init();
    </script>
</body>
</html>