import { defineStore } from "pinia";
import {
  ImanagementSearchData,
  IupdateArea,
  IOptionalData,
  ImanagementStoreDetial,
} from "@/types/datamanagement";
import { ILogDelete, ILogDownload } from "@/types/car";
import {
  getCode,
  getStore,
  getOptional,
  deleteStore,
  updateArea,
  updateStore,
  addStore,
  getStoreDetial,
  importStore,
  deleteLog,
  importLog,
  downloadLog,
  getSelect,
  getAccumulation,
  addPoint,
} from "@/service/modules/datamanagement";

export const usedatamanagementStore = defineStore("datamanagement", () => {
  const loading = ref<boolean>(false);
  const noteAllList = ref<any>();
  const noteList = ref<any>();
  const logList = ref<any>();
  const datamanagementList = ref<any>({});
  const actualShopTotalList = ref<any>([]);
  const isInTable = ref<any>(false);

  async function addSpecialPoint(data: any) {
    const res = await addPoint(data);
    return res;
  }

  async function getCheckInPoint() {
    const res = await getAccumulation();
    return res;
  }

  async function getStoreAction(data: ImanagementSearchData) {
    loading.value = true;
    const res = await getStore(data);
    datamanagementList.value = res.data;
    loading.value = false;
  }

  async function getArea() {
    const res = await getSelect();
    return res.data;
  }

  const getOptionalData = ref<IOptionalData[]>();
  async function getOptionalAction(data: string) {
    const res = await getOptional(data);
    getOptionalData.value = res.data;
  }
  async function deleteStoreAction(data: string) {
    const res = await deleteStore(data);
    if (res.code === 20000) {
      ElMessage.success("删除成功");
    } else {
      ElMessage.error("删除失败");
    }
  }
  async function updateAreaAction(data: IupdateArea) {
    const res = await updateArea(data);
    console.log(res);
    if (res.code === 20000) {
      ElMessage.success("调整成功");
    } else {
      ElMessage.error("调整失败");
    }
  }
  async function updateStoreAction(data: ImanagementStoreDetial) {
    console.log(data);
    const dataasign = JSON.parse(JSON.stringify(data));
    delete dataasign.accumulationName;
    delete dataasign.resaleCycle;
    dataasign.district = dataasign.areaName;
    const res = await updateStore(dataasign);
    if (res.code === 20000) {
      ElMessage.success("修改成功");
    } else {
      ElMessage.error("修改失败");
    }
  }
  async function addStoreAction(
    data: ImanagementStoreDetial,
    areaId: number = 0
  ) {
    if (areaId) {
      data.areaId = areaId;
    }
    const dataasign = JSON.parse(JSON.stringify(data));
    delete dataasign.accumulationName;
    delete dataasign.district;
    delete dataasign.updateTime;
    delete dataasign.createTime;
    delete dataasign.orderCycle;
    const res = await addStore(dataasign);
    if (res.code === 20000) {
      ElMessage.success("添加成功");
    } else {
      ElMessage.error("添加失败");
    }
  }
  const storeDetialData = ref<any>();
  async function getStoreDetialAction(data: number) {
    const res = await getStoreDetial(data);
    storeDetialData.value = res.data;
  }

  async function importStoreTable(formData: FormData, config: any) {
    const res = await importStore(formData, config);
    console.log(res);
    return res;
  }

  async function getTotalShopActual(pageSize: number) {
    let pageNum = 1;
    let hasMoreData = true;
    actualShopTotalList.value = [];
    while (hasMoreData) {
      try {
        const res: any = await getStore({ pageNum, pageSize });
        actualShopTotalList.value = [
          ...actualShopTotalList.value,
          ...res.data.records,
        ];
        hasMoreData = res.data.records.length === pageSize;
        pageNum++;
      } catch (error) {
        console.error("请求数据时出错:", error);
        hasMoreData = false; // 出现错误时停止请求
      }
    }
  }

  //导入全部日志
  async function importAllLog(pageSize: number) {
    let pageNum = 1;
    const type = "0";
    let hasMoreData = true;
    noteAllList.value = [];
    while (hasMoreData) {
      try {
        const res: any = await importLog({ pageNum, pageSize, type });
        noteAllList.value = [...noteAllList.value, ...res.data.records];
        // 检查是否还有更多数据
        hasMoreData = res.data.records.length === pageSize;
        pageNum++;
      } catch (error) {
        console.error("请求数据时出错:", error);
        hasMoreData = false; // 出现错误时停止请求
      }
    }
  }

  async function importShopLog(params: any) {
    const res = await importLog(params);
    logList.value = res.data;
  }

  //删除日志
  async function deleteCarLog(params: ILogDelete) {
    await deleteLog(params);
  }

  //下载日志
  async function downloadCarLog(params: ILogDownload) {
    const res: any = await downloadLog(params);
    return res;
  }

  function updatelogList(pageNum: number, pageSize: number) {
    noteList.value = noteAllList.value.slice(
      pageNum * pageSize - pageSize,
      pageNum * pageSize
    );
  }

  function updateIsinTable() {
    isInTable.value = !isInTable.value;
  }

  // 显示用户编码
  async function getAddCode() {
    const res = await getCode();
    // console.log(res);
    return res;
  }

  return {
    getAddCode,
    getCheckInPoint,
    datamanagementList,
    getStoreAction,
    loading,
    getOptionalData,
    getOptionalAction,
    deleteStoreAction,
    updateAreaAction,
    updateStoreAction,
    addStoreAction,
    importAllLog,
    noteAllList,
    noteList,
    storeDetialData,
    updatelogList,
    downloadCarLog,
    deleteCarLog,
    getStoreDetialAction,
    importStoreTable,
    getTotalShopActual,
    updateIsinTable,
    importShopLog,
    getArea,
    addSpecialPoint,
    logList,
  };
});
