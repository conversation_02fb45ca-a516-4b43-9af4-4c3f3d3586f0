package com.ict.ycwl.clustercalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.clustercalculate.pojo.LngAndLat;
import com.ict.ycwl.clustercalculate.pojo.Store;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface StoreMapper extends BaseMapper<Store> {

    /**
     * 查询数据的经度和纬度
     *
     * @param keyword 指定大区的名字
     * @return 返回包含大区里每一条数据的经纬度的一个数组
     */
    List<LngAndLat> selectCoordinatesByAreaName(String keyword);

    /**
     * 查询大区名
     *
     * @return 返回所有去重的大区名
     */
    List<String> selectAllAreaName();

    /**
     * 根据经纬度找到商铺，并将商铺的accumulation_id字段改成accumulationId值
     *
     * @param accumulationId 聚集区id
     * @param longitude      经度
     * @param latitude       纬度
     */
    void updateAccumulationIdByLonAndLat(Long accumulationId, double longitude, double latitude);

    /**
     * 根据簇心找到该聚集区所包含的商铺
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 商铺数组
     */
    List<LngAndLat> selectByLongAndLat(double longitude, double latitude);

    /**
     * 查询除聚集区点、错误点之外的所有点
     *
     * @return 普通点数组
     */
    List<Store> getOrdinaryPoint();

    /**
     * 根据聚集区id，查询除聚集区点、错误点之外的所有点
     *
     * @param accumulationId 聚集区id
     * @return 普通点数组
     */
    List<Store> getOrdinaryPointByAccumulationId(Long accumulationId);

    /**
     * 查询还未被聚集区计算涉及到的经度和纬度
     *
     * @param areaName 指定大区的名字
     * @return 返回还未被聚集区计算涉及到的经纬度的一个数组
     */
    List<LngAndLat> selectRemainingLngLat(String areaName);

    int deleteStoreSpecial(double pairingStoreLongitude, double pairingStoreLatitude);

    /**
     * 将特殊商铺更新为普通商铺
     * @param customerCode 客户编号
     * @param accumulationId 新的聚集区ID
     * @param routeId 新的路线ID
     * @return 更新的记录数
     */
    int updateStoreToNormal(String customerCode, Long accumulationId, Long routeId);

}
