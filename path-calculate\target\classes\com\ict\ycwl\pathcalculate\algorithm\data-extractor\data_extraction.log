2025-07-20 13:00:53,409 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-20 13:00:53,417 - __main__ - INFO - 数据库连接成功
2025-07-20 13:00:53,417 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-20 13:00:53,418 - __main__ - INFO - 开始提取班组数据...
2025-07-20 13:00:53,418 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-20 13:00:53,418 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-20 13:00:53,418 - __main__ - INFO - 开始提取中转站数据...
2025-07-20 13:00:53,419 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-20 13:00:53,419 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-20 13:00:53,421 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-20 13:00:53,421 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-20 13:00:53,421 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-20 13:00:53,421 - __main__ - INFO - 更新班组关系数据...
2025-07-20 13:00:53,421 - __main__ - INFO - 班组关系数据更新完成
2025-07-20 13:00:53,421 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-20 13:00:53,421 - __main__ - INFO - 开始提取聚集区数据...
2025-07-20 13:00:53,433 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-20 13:00:53,434 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-20 13:00:53,434 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-20 13:00:53,434 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-20 13:00:53,434 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-20 13:00:53,434 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-20 13:00:53,434 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-20 13:00:53,816 - __main__ - INFO - 提取时间矩阵数据完成，有效记录: 476 条
2025-07-20 13:00:53,817 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-20 13:00:53,817 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-20 13:00:53,822 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 2176 条记录
2025-07-20 13:00:53,822 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-20 13:00:53,822 - __main__ - INFO - 开始验证数据完整性...
2025-07-20 13:00:53,822 - __main__ - INFO - 数据完整性验证通过
2025-07-20 13:00:53,822 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-20 13:00:53,822 - __main__ - INFO - 开始保存JSON文件...
2025-07-20 13:00:53,823 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-20 13:00:53,824 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-20 13:00:53,824 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-20 13:00:53,850 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-20 13:00:53,851 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-20 13:00:53,851 - __main__ - INFO - 数据提取流程完成
2025-07-20 13:00:53,851 - __main__ - INFO - 数据库连接已关闭
2025-07-20 13:29:45,627 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-20 13:29:45,635 - __main__ - INFO - 数据库连接成功
2025-07-20 13:29:45,635 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-20 13:29:45,635 - __main__ - INFO - 开始提取班组数据...
2025-07-20 13:29:45,636 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-20 13:29:45,636 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-20 13:29:45,636 - __main__ - INFO - 开始提取中转站数据...
2025-07-20 13:29:45,636 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-20 13:29:45,636 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-20 13:29:45,636 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-20 13:29:45,636 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-20 13:29:45,636 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-20 13:29:45,636 - __main__ - INFO - 更新班组关系数据...
2025-07-20 13:29:45,636 - __main__ - INFO - 班组关系数据更新完成
2025-07-20 13:29:45,636 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-20 13:29:45,636 - __main__ - INFO - 开始提取聚集区数据...
2025-07-20 13:29:45,646 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-20 13:29:45,646 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-20 13:29:45,646 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-20 13:29:45,646 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-20 13:29:45,646 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-20 13:29:45,647 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-20 13:29:45,647 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-20 13:29:48,525 - __main__ - INFO - 批次 1 完成，找到 13070 条记录
2025-07-20 13:29:48,756 - __main__ - INFO - 批次 2 完成，找到 872 条记录
2025-07-20 13:29:48,756 - __main__ - INFO - 提取时间矩阵数据完成，总计有效记录: 13942 条
2025-07-20 13:29:48,756 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-20 13:29:48,756 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-20 13:29:48,762 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 2140 条记录
2025-07-20 13:29:48,762 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-20 13:29:48,762 - __main__ - INFO - 开始验证数据完整性...
2025-07-20 13:29:48,762 - __main__ - INFO - 数据完整性验证通过
2025-07-20 13:29:48,762 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-20 13:29:48,762 - __main__ - INFO - 开始保存JSON文件...
2025-07-20 13:29:48,764 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-20 13:29:48,764 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-20 13:29:48,765 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-20 13:29:48,907 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-20 13:29:48,908 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-20 13:29:48,908 - __main__ - INFO - 数据提取流程完成
2025-07-20 13:29:48,908 - __main__ - INFO - 数据库连接已关闭
2025-07-20 13:42:53,863 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-20 13:42:53,870 - __main__ - INFO - 数据库连接成功
2025-07-20 13:42:53,870 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-20 13:42:53,870 - __main__ - INFO - 开始提取班组数据...
2025-07-20 13:42:53,870 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-20 13:42:53,870 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-20 13:42:53,870 - __main__ - INFO - 开始提取中转站数据...
2025-07-20 13:42:53,871 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-20 13:42:53,871 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-20 13:42:53,871 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-20 13:42:53,871 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-20 13:42:53,871 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-20 13:42:53,871 - __main__ - INFO - 更新班组关系数据...
2025-07-20 13:42:53,871 - __main__ - INFO - 班组关系数据更新完成
2025-07-20 13:42:53,871 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-20 13:42:53,871 - __main__ - INFO - 开始提取聚集区数据...
2025-07-20 13:42:53,881 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-20 13:42:53,881 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-20 13:42:53,881 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-20 13:42:53,881 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-20 13:42:53,881 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-20 13:42:53,881 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-20 13:42:53,881 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-20 13:42:53,881 - __main__ - INFO - 期望的完全图路径对数: 2652 对
2025-07-20 13:42:53,881 - __main__ - INFO - 坐标点示例:
2025-07-20 13:42:53,881 - __main__ - INFO -   1. (113.517758, 24.769346) - 聚集区1906559776938569730
2025-07-20 13:42:53,881 - __main__ - INFO -   2. (113.568939, 24.8284) - 聚集区1906559660299169794
2025-07-20 13:42:53,881 - __main__ - INFO -   3. (113.536028, 24.897287) - 聚集区1906559660429193217
2025-07-20 13:42:53,881 - __main__ - INFO -   4. (114.154214, 25.025656) - 中转站4
2025-07-20 13:42:53,881 - __main__ - INFO -   5. (113.666729, 24.856092) - 聚集区1906559659699384321
2025-07-20 13:42:54,179 - __main__ - INFO - 数据库中找到 1524 个唯一坐标点
2025-07-20 13:42:54,179 - __main__ - INFO - 坐标匹配结果: 34 个匹配，18 个未匹配
2025-07-20 13:42:54,179 - __main__ - WARNING - 未匹配的坐标点示例:
2025-07-20 13:42:54,179 - __main__ - WARNING -   1. (113.517758, 24.769346) - 聚集区1906559776938569730
2025-07-20 13:42:54,179 - __main__ - WARNING -   2. (114.154214, 25.025656) - 中转站4
2025-07-20 13:42:54,179 - __main__ - WARNING -   3. (113.552521, 24.772718) - 聚集区1906559777743876097
2025-07-20 13:43:14,717 - __main__ - INFO - 数据库连接已关闭
2025-07-20 17:18:13,017 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-20 17:18:13,037 - __main__ - INFO - 数据库连接成功
2025-07-20 17:18:13,037 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-20 17:18:13,037 - __main__ - INFO - 开始提取班组数据...
2025-07-20 17:18:13,037 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-20 17:18:13,037 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-20 17:18:13,037 - __main__ - INFO - 开始提取中转站数据...
2025-07-20 17:18:13,038 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-20 17:18:13,039 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-20 17:18:13,039 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-20 17:18:13,039 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-20 17:18:13,039 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-20 17:18:13,039 - __main__ - INFO - 更新班组关系数据...
2025-07-20 17:18:13,039 - __main__ - INFO - 班组关系数据更新完成
2025-07-20 17:18:13,039 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-20 17:18:13,039 - __main__ - INFO - 开始提取聚集区数据...
2025-07-20 17:18:13,050 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-20 17:18:13,050 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-20 17:18:13,050 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-20 17:18:13,050 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-20 17:18:13,050 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-20 17:18:13,050 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-20 17:18:13,050 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-20 17:18:13,050 - __main__ - INFO - 期望的完全图路径对数: 2652 对
2025-07-20 17:18:13,050 - __main__ - INFO - 坐标点示例:
2025-07-20 17:18:13,050 - __main__ - INFO -   1. (113.517758, 24.769346) - 聚集区1906559776938569730
2025-07-20 17:18:13,050 - __main__ - INFO -   2. (113.568939, 24.8284) - 聚集区1906559660299169794
2025-07-20 17:18:13,050 - __main__ - INFO -   3. (113.536028, 24.897287) - 聚集区1906559660429193217
2025-07-20 17:18:13,050 - __main__ - INFO -   4. (114.154214, 25.025656) - 中转站4
2025-07-20 17:18:13,050 - __main__ - INFO -   5. (113.666729, 24.856092) - 聚集区1906559659699384321
2025-07-20 17:18:13,466 - __main__ - INFO - 数据库中找到 1556 个唯一坐标点
2025-07-20 17:18:13,466 - __main__ - INFO - 坐标匹配结果: 50 个匹配，2 个未匹配
2025-07-20 17:18:13,466 - __main__ - WARNING - 未匹配的坐标点示例:
2025-07-20 17:18:13,466 - __main__ - WARNING -   1. (114.154214, 25.025656) - 中转站4
2025-07-20 17:18:13,466 - __main__ - WARNING -   2. (113.58208, 24.75415) - 中转站6
2025-07-20 17:18:34,135 - __main__ - INFO - 数据库连接已关闭
2025-07-20 17:26:16,830 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-20 17:26:16,846 - __main__ - INFO - 数据库连接成功
2025-07-20 17:26:16,846 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-20 17:26:16,846 - __main__ - INFO - 开始提取班组数据...
2025-07-20 17:26:16,849 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-20 17:26:16,849 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-20 17:26:16,849 - __main__ - INFO - 开始提取中转站数据...
2025-07-20 17:26:16,849 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-20 17:26:16,850 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-20 17:26:16,850 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-20 17:26:16,850 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-20 17:26:16,850 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-20 17:26:16,850 - __main__ - INFO - 更新班组关系数据...
2025-07-20 17:26:16,850 - __main__ - INFO - 班组关系数据更新完成
2025-07-20 17:26:16,850 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-20 17:26:16,850 - __main__ - INFO - 开始提取聚集区数据...
2025-07-20 17:26:16,867 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-20 17:26:16,867 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-20 17:26:16,867 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-20 17:26:16,867 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-20 17:26:16,867 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-20 17:26:16,868 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-20 17:26:16,868 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-20 17:26:16,868 - __main__ - INFO - 期望的完全图路径对数: 2652 对
2025-07-20 17:26:16,868 - __main__ - INFO - 坐标点示例:
2025-07-20 17:26:16,868 - __main__ - INFO -   1. (113.517758, 24.769346) - 聚集区1906559776938569730
2025-07-20 17:26:16,868 - __main__ - INFO -   2. (113.568939, 24.8284) - 聚集区1906559660299169794
2025-07-20 17:26:16,868 - __main__ - INFO -   3. (113.536028, 24.897287) - 聚集区1906559660429193217
2025-07-20 17:26:16,868 - __main__ - INFO -   4. (114.154214, 25.025656) - 中转站4
2025-07-20 17:26:16,868 - __main__ - INFO -   5. (113.666729, 24.856092) - 聚集区1906559659699384321
2025-07-20 17:26:17,487 - __main__ - INFO - 数据库中找到 1556 个唯一坐标点
2025-07-20 17:26:17,487 - __main__ - INFO - 坐标匹配结果: 50 个匹配，2 个未匹配
2025-07-20 17:26:17,487 - __main__ - WARNING - 未匹配的坐标点示例:
2025-07-20 17:26:17,487 - __main__ - WARNING -   1. (114.154214, 25.025656) - 中转站4
2025-07-20 17:26:17,487 - __main__ - WARNING -   2. (113.58208, 24.75415) - 中转站6
2025-07-20 17:26:17,487 - __main__ - INFO - 开始查询 50 个匹配坐标点之间的路径...
2025-07-20 17:26:25,624 - __main__ - INFO - 数据库连接已关闭
2025-07-20 17:26:27,935 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-20 17:26:27,943 - __main__ - INFO - 数据库连接成功
2025-07-20 17:26:27,943 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-20 17:26:27,943 - __main__ - INFO - 开始提取班组数据...
2025-07-20 17:26:27,944 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-20 17:26:27,944 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-20 17:26:27,944 - __main__ - INFO - 开始提取中转站数据...
2025-07-20 17:26:27,945 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-20 17:26:27,945 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-20 17:26:27,945 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-20 17:26:27,945 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-20 17:26:27,945 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-20 17:26:27,946 - __main__ - INFO - 更新班组关系数据...
2025-07-20 17:26:27,946 - __main__ - INFO - 班组关系数据更新完成
2025-07-20 17:26:27,946 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-20 17:26:27,946 - __main__ - INFO - 开始提取聚集区数据...
2025-07-20 17:26:27,959 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:21:49,713 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:21:49,736 - __main__ - INFO - 数据库连接成功
2025-07-21 18:21:49,736 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:21:49,736 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:21:49,744 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-21 18:21:49,744 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-21 18:21:49,744 - __main__ - INFO - 开始提取中转站数据...
2025-07-21 18:21:49,746 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-21 18:21:49,746 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-21 18:21:49,746 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-21 18:21:49,746 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-21 18:21:49,746 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-21 18:21:49,746 - __main__ - INFO - 更新班组关系数据...
2025-07-21 18:21:49,746 - __main__ - INFO - 班组关系数据更新完成
2025-07-21 18:21:49,746 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-21 18:21:49,746 - __main__ - INFO - 开始提取聚集区数据...
2025-07-21 18:21:49,765 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-21 18:21:49,765 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-21 18:21:49,765 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-21 18:21:49,765 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-21 18:21:49,765 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-21 18:21:49,765 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-21 18:21:49,765 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-21 18:21:49,765 - __main__ - INFO - 坐标点样例: ['113.544,24.796', '113.567,24.9166', '114.014,25.1828']
2025-07-21 18:21:49,946 - __main__ - INFO - 数据库查询返回记录数: 0
2025-07-21 18:21:49,946 - __main__ - INFO - ==================================================
2025-07-21 18:21:49,946 - __main__ - INFO - 时间矩阵提取结果:
2025-07-21 18:21:49,946 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-21 18:21:49,946 - __main__ - INFO -   数据库中坐标点: 0 个
2025-07-21 18:21:49,946 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-21 18:21:49,946 - __main__ - INFO -   实际获得记录数: 0
2025-07-21 18:21:49,946 - __main__ - INFO -   覆盖率: 0.0%
2025-07-21 18:21:49,946 - __main__ - WARNING - ⚠️  52 个坐标点在数据库中无时间数据:
2025-07-21 18:21:49,946 - __main__ - WARNING -     缺失: 113.461,24.9798 (聚集区1906559659426754561)
2025-07-21 18:21:49,946 - __main__ - WARNING -     缺失: 113.504,24.9151 (聚集区1906559659766493186)
2025-07-21 18:21:49,946 - __main__ - WARNING -     缺失: 113.505,24.7592 (聚集区1906559776808546306)
2025-07-21 18:21:49,946 - __main__ - WARNING -     缺失: 113.518,24.7513 (聚集区1906559776871460866)
2025-07-21 18:21:49,946 - __main__ - WARNING -     缺失: 113.518,24.7693 (聚集区1906559776938569730)
2025-07-21 18:21:49,946 - __main__ - WARNING -     ... 还有 47 个
2025-07-21 18:21:49,946 - __main__ - INFO - ==================================================
2025-07-21 18:21:49,946 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-21 18:21:49,946 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-21 18:21:49,953 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 2652 条记录
2025-07-21 18:21:49,953 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-21 18:21:49,953 - __main__ - INFO - 开始验证数据完整性...
2025-07-21 18:21:49,953 - __main__ - INFO - 数据完整性验证通过
2025-07-21 18:21:49,953 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-21 18:21:49,953 - __main__ - INFO - 开始保存JSON文件...
2025-07-21 18:21:49,954 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-21 18:21:49,955 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-21 18:21:49,956 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-21 18:21:49,982 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-21 18:21:49,983 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-21 18:21:49,983 - __main__ - INFO - 数据提取流程完成
2025-07-21 18:21:49,983 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:31:02,650 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:31:02,657 - __main__ - INFO - 数据库连接成功
2025-07-21 18:31:02,657 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:31:02,657 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:31:02,657 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-21 18:31:02,657 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-21 18:31:02,657 - __main__ - INFO - 开始提取中转站数据...
2025-07-21 18:31:02,658 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-21 18:31:02,658 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-21 18:31:02,658 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-21 18:31:02,659 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-21 18:31:02,659 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-21 18:31:02,659 - __main__ - INFO - 更新班组关系数据...
2025-07-21 18:31:02,659 - __main__ - INFO - 班组关系数据更新完成
2025-07-21 18:31:02,659 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-21 18:31:02,659 - __main__ - INFO - 开始提取聚集区数据...
2025-07-21 18:31:02,668 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-21 18:31:02,668 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-21 18:31:02,668 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-21 18:31:02,669 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-21 18:31:02,669 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-21 18:31:02,669 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-21 18:31:02,669 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-21 18:31:02,669 - __main__ - INFO - 坐标点样例: ['113.565,24.8463', '113.583,24.7781', '113.583,24.7826']
2025-07-21 18:31:02,806 - __main__ - INFO - 数据库查询返回记录数: 0
2025-07-21 18:31:02,806 - __main__ - INFO - ==================================================
2025-07-21 18:31:02,806 - __main__ - INFO - 时间矩阵提取结果:
2025-07-21 18:31:02,806 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-21 18:31:02,806 - __main__ - INFO -   数据库中坐标点: 0 个
2025-07-21 18:31:02,806 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-21 18:31:02,806 - __main__ - INFO -   实际获得记录数: 0
2025-07-21 18:31:02,806 - __main__ - INFO -   覆盖率: 0.0%
2025-07-21 18:31:02,806 - __main__ - WARNING - ⚠️  52 个坐标点在数据库中无时间数据:
2025-07-21 18:31:02,806 - __main__ - WARNING -     缺失: 113.461,24.9798 (聚集区1906559659426754561)
2025-07-21 18:31:02,806 - __main__ - WARNING -     缺失: 113.504,24.9151 (聚集区1906559659766493186)
2025-07-21 18:31:02,806 - __main__ - WARNING -     缺失: 113.505,24.7592 (聚集区1906559776808546306)
2025-07-21 18:31:02,806 - __main__ - WARNING -     缺失: 113.518,24.7513 (聚集区1906559776871460866)
2025-07-21 18:31:02,806 - __main__ - WARNING -     缺失: 113.518,24.7693 (聚集区1906559776938569730)
2025-07-21 18:31:02,806 - __main__ - WARNING -     ... 还有 47 个
2025-07-21 18:31:02,806 - __main__ - INFO - ==================================================
2025-07-21 18:31:02,806 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-21 18:31:02,806 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-21 18:31:02,812 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 2652 条记录
2025-07-21 18:31:02,812 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-21 18:31:02,812 - __main__ - INFO - 开始验证数据完整性...
2025-07-21 18:31:02,812 - __main__ - INFO - 数据完整性验证通过
2025-07-21 18:31:02,812 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-21 18:31:02,812 - __main__ - INFO - 开始保存JSON文件...
2025-07-21 18:31:02,813 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-21 18:31:02,813 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-21 18:31:02,814 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-21 18:31:02,837 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-21 18:31:02,838 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-21 18:31:02,838 - __main__ - INFO - 数据提取流程完成
2025-07-21 18:31:02,838 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:40:27,909 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:40:27,917 - __main__ - INFO - 数据库连接成功
2025-07-21 18:40:27,917 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:40:27,917 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:40:27,917 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-21 18:40:27,917 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-21 18:40:27,917 - __main__ - INFO - 开始提取中转站数据...
2025-07-21 18:40:27,918 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-21 18:40:27,918 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-21 18:40:27,918 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-21 18:40:27,918 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-21 18:40:27,918 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-21 18:40:27,918 - __main__ - INFO - 更新班组关系数据...
2025-07-21 18:40:27,919 - __main__ - INFO - 班组关系数据更新完成
2025-07-21 18:40:27,919 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-21 18:40:27,919 - __main__ - INFO - 开始提取聚集区数据...
2025-07-21 18:40:27,929 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-21 18:40:27,929 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-21 18:40:27,929 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-21 18:40:27,930 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-21 18:40:27,930 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-21 18:40:27,930 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-21 18:40:27,930 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-21 18:40:27,930 - __main__ - INFO - 坐标点样例: ['114.473623,25.116658', '113.588967,24.797791', '113.646255,24.786377']
2025-07-21 18:40:28,047 - __main__ - INFO - 数据库查询返回记录数: 784
2025-07-21 18:40:28,049 - __main__ - INFO - ==================================================
2025-07-21 18:40:28,049 - __main__ - INFO - 时间矩阵提取结果:
2025-07-21 18:40:28,049 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-21 18:40:28,049 - __main__ - INFO -   数据库中坐标点: 50 个
2025-07-21 18:40:28,049 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-21 18:40:28,049 - __main__ - INFO -   实际获得记录数: 784
2025-07-21 18:40:28,049 - __main__ - INFO -   覆盖率: 29.6%
2025-07-21 18:40:28,049 - __main__ - WARNING - ⚠️  2 个坐标点在数据库中无时间数据:
2025-07-21 18:40:28,049 - __main__ - WARNING -     缺失: 113.58208,24.75415 (中转站6)
2025-07-21 18:40:28,050 - __main__ - WARNING -     缺失: 114.154214,25.025656 (中转站4)
2025-07-21 18:40:28,050 - __main__ - INFO - ==================================================
2025-07-21 18:40:28,050 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-21 18:40:28,050 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-21 18:40:28,054 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 1868 条记录
2025-07-21 18:40:28,054 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-21 18:40:28,054 - __main__ - INFO - 开始验证数据完整性...
2025-07-21 18:40:28,054 - __main__ - INFO - 数据完整性验证通过
2025-07-21 18:40:28,054 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-21 18:40:28,054 - __main__ - INFO - 开始保存JSON文件...
2025-07-21 18:40:28,056 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-21 18:40:28,056 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-21 18:40:28,057 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-21 18:40:28,081 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-21 18:40:28,081 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-21 18:40:28,081 - __main__ - INFO - 数据提取流程完成
2025-07-21 18:40:28,081 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:41:00,745 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:41:00,752 - __main__ - INFO - 数据库连接成功
2025-07-21 18:41:00,752 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:41:00,752 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:41:00,753 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-21 18:41:00,753 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-21 18:41:00,753 - __main__ - INFO - 开始提取中转站数据...
2025-07-21 18:41:00,753 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-21 18:41:00,753 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-21 18:41:00,753 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-21 18:41:00,753 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-21 18:41:00,754 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-21 18:41:00,754 - __main__ - INFO - 更新班组关系数据...
2025-07-21 18:41:00,754 - __main__ - INFO - 班组关系数据更新完成
2025-07-21 18:41:00,754 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-21 18:41:00,754 - __main__ - INFO - 开始提取聚集区数据...
2025-07-21 18:41:00,763 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-21 18:41:00,763 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-21 18:41:00,763 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-21 18:41:00,763 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-21 18:41:00,763 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-21 18:41:00,763 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-21 18:41:00,764 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-21 18:41:00,764 - __main__ - INFO - 坐标点样例: ['113.678667,24.790844', '113.504393,24.91505', '114.014315,25.182799']
2025-07-21 18:41:00,902 - __main__ - INFO - 数据库查询返回记录数: 784
2025-07-21 18:41:00,905 - __main__ - INFO - ==================================================
2025-07-21 18:41:00,905 - __main__ - INFO - 时间矩阵提取结果:
2025-07-21 18:41:00,905 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-21 18:41:00,905 - __main__ - INFO -   数据库中坐标点: 50 个
2025-07-21 18:41:00,905 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-21 18:41:00,905 - __main__ - INFO -   实际获得记录数: 784
2025-07-21 18:41:00,905 - __main__ - INFO -   覆盖率: 29.6%
2025-07-21 18:41:00,905 - __main__ - WARNING - ⚠️  2 个坐标点在数据库中无时间数据:
2025-07-21 18:41:00,905 - __main__ - WARNING -     缺失: 113.58208,24.75415 (中转站6)
2025-07-21 18:41:00,905 - __main__ - WARNING -     缺失: 114.154214,25.025656 (中转站4)
2025-07-21 18:41:00,905 - __main__ - INFO - ==================================================
2025-07-21 18:41:00,905 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-21 18:41:00,905 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-21 18:41:00,910 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 1868 条记录
2025-07-21 18:41:00,910 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-21 18:41:00,911 - __main__ - INFO - 开始验证数据完整性...
2025-07-21 18:41:00,911 - __main__ - INFO - 数据完整性验证通过
2025-07-21 18:41:00,911 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-21 18:41:00,911 - __main__ - INFO - 开始保存JSON文件...
2025-07-21 18:41:00,912 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-21 18:41:00,913 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-21 18:41:00,914 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-21 18:41:00,939 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-21 18:41:00,939 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-21 18:41:00,940 - __main__ - INFO - 数据提取流程完成
2025-07-21 18:41:00,940 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:41:13,743 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:41:13,752 - __main__ - INFO - 数据库连接成功
2025-07-21 18:41:13,752 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:41:13,752 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:41:13,753 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-21 18:41:13,753 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-21 18:41:13,753 - __main__ - INFO - 开始提取中转站数据...
2025-07-21 18:41:13,754 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-21 18:41:13,754 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-21 18:41:13,754 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-21 18:41:13,754 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-21 18:41:13,754 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-21 18:41:13,754 - __main__ - INFO - 更新班组关系数据...
2025-07-21 18:41:13,754 - __main__ - INFO - 班组关系数据更新完成
2025-07-21 18:41:13,754 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-21 18:41:13,754 - __main__ - INFO - 开始提取聚集区数据...
2025-07-21 18:41:13,765 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-21 18:41:13,765 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-21 18:41:13,765 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-21 18:41:13,765 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-21 18:41:13,765 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-21 18:41:13,765 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-21 18:41:13,765 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-21 18:41:13,765 - __main__ - INFO - 坐标点样例: ['113.646255,24.786377', '113.583183,24.778097', '114.727913,25.245015']
2025-07-21 18:41:13,939 - __main__ - INFO - 数据库查询返回记录数: 784
2025-07-21 18:41:13,943 - __main__ - INFO - ==================================================
2025-07-21 18:41:13,943 - __main__ - INFO - 时间矩阵提取结果:
2025-07-21 18:41:13,943 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-21 18:41:13,943 - __main__ - INFO -   数据库中坐标点: 50 个
2025-07-21 18:41:13,943 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-21 18:41:13,943 - __main__ - INFO -   实际获得记录数: 784
2025-07-21 18:41:13,943 - __main__ - INFO -   覆盖率: 29.6%
2025-07-21 18:41:13,943 - __main__ - WARNING - ⚠️  2 个坐标点在数据库中无时间数据:
2025-07-21 18:41:13,943 - __main__ - WARNING -     缺失: 113.58208,24.75415 (中转站6)
2025-07-21 18:41:13,943 - __main__ - WARNING -     缺失: 114.154214,25.025656 (中转站4)
2025-07-21 18:41:13,943 - __main__ - INFO - ==================================================
2025-07-21 18:41:13,943 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-21 18:41:13,943 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-21 18:41:13,948 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 1868 条记录
2025-07-21 18:41:13,948 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-21 18:41:13,948 - __main__ - INFO - 开始验证数据完整性...
2025-07-21 18:41:13,948 - __main__ - INFO - 数据完整性验证通过
2025-07-21 18:41:13,948 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-21 18:41:13,948 - __main__ - INFO - 开始保存JSON文件...
2025-07-21 18:41:13,950 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-21 18:41:13,950 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-21 18:41:13,951 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-21 18:41:13,977 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-21 18:41:13,977 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-21 18:41:13,978 - __main__ - INFO - 数据提取流程完成
2025-07-21 18:41:13,978 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:47:58,362 - __main__ - INFO - 使用真正随机模式
2025-07-21 18:47:58,362 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:47:58,369 - __main__ - INFO - 数据库连接成功
2025-07-21 18:47:58,369 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:47:58,369 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:47:58,369 - __main__ - ERROR - 提取班组数据失败: (1054, "Unknown column 'team_id' in 'field list'")
2025-07-21 18:47:58,369 - __main__ - ERROR - 班组数据提取失败，终止流程
2025-07-21 18:47:58,369 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:51:44,089 - __main__ - INFO - 使用真正随机模式
2025-07-21 18:51:44,089 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:51:44,097 - __main__ - INFO - 数据库连接成功
2025-07-21 18:51:44,097 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:51:44,097 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:51:44,097 - __main__ - ERROR - 提取班组数据失败: (1054, "Unknown column 'is_delete' in 'where clause'")
2025-07-21 18:51:44,097 - __main__ - ERROR - 班组数据提取失败，终止流程
2025-07-21 18:51:44,097 - __main__ - INFO - 数据库连接已关闭
2025-07-21 18:53:20,355 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-21 18:53:20,362 - __main__ - INFO - 数据库连接成功
2025-07-21 18:53:20,362 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-21 18:53:20,362 - __main__ - INFO - 开始提取班组数据...
2025-07-21 18:53:20,363 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-21 18:53:20,363 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-21 18:53:20,363 - __main__ - INFO - 开始提取中转站数据...
2025-07-21 18:53:20,364 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-21 18:53:20,364 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-21 18:53:20,364 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-21 18:53:20,364 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-21 18:53:20,364 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-21 18:53:20,364 - __main__ - INFO - 更新班组关系数据...
2025-07-21 18:53:20,364 - __main__ - INFO - 班组关系数据更新完成
2025-07-21 18:53:20,364 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-21 18:53:20,364 - __main__ - INFO - 开始提取聚集区数据...
2025-07-21 18:53:20,375 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-21 18:53:20,375 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-21 18:53:20,375 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-21 18:53:20,375 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-21 18:53:20,375 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-21 18:53:20,375 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-21 18:53:20,375 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-21 18:53:20,375 - __main__ - INFO - 坐标点样例: ['113.533929,24.824828', '114.151281,25.236341', '114.083421,25.238513']
2025-07-21 18:53:20,509 - __main__ - INFO - 数据库查询返回记录数: 784
2025-07-21 18:53:20,513 - __main__ - INFO - ==================================================
2025-07-21 18:53:20,513 - __main__ - INFO - 时间矩阵提取结果:
2025-07-21 18:53:20,513 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-21 18:53:20,513 - __main__ - INFO -   数据库中坐标点: 50 个
2025-07-21 18:53:20,513 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-21 18:53:20,514 - __main__ - INFO -   实际获得记录数: 784
2025-07-21 18:53:20,514 - __main__ - INFO -   覆盖率: 29.6%
2025-07-21 18:53:20,514 - __main__ - WARNING - ⚠️  2 个坐标点在数据库中无时间数据:
2025-07-21 18:53:20,514 - __main__ - WARNING -     缺失: 113.58208,24.75415 (中转站6)
2025-07-21 18:53:20,514 - __main__ - WARNING -     缺失: 114.154214,25.025656 (中转站4)
2025-07-21 18:53:20,514 - __main__ - INFO - ==================================================
2025-07-21 18:53:20,514 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-21 18:53:20,514 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-21 18:53:20,519 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 1868 条记录
2025-07-21 18:53:20,519 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-21 18:53:20,520 - __main__ - INFO - 开始验证数据完整性...
2025-07-21 18:53:20,520 - __main__ - INFO - 数据完整性验证通过
2025-07-21 18:53:20,520 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-21 18:53:20,520 - __main__ - INFO - 开始保存JSON文件...
2025-07-21 18:53:20,521 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-21 18:53:20,521 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-21 18:53:20,522 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-21 18:53:20,545 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-21 18:53:20,546 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-21 18:53:20,546 - __main__ - INFO - 数据提取流程完成
2025-07-21 18:53:20,546 - __main__ - INFO - 数据库连接已关闭
2025-07-22 21:32:39,790 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-22 21:32:39,815 - __main__ - INFO - 数据库连接成功
2025-07-22 21:32:39,815 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-22 21:32:39,815 - __main__ - INFO - 开始提取班组数据...
2025-07-22 21:32:39,822 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-22 21:32:39,822 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-22 21:32:39,822 - __main__ - INFO - 开始提取中转站数据...
2025-07-22 21:32:39,825 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-22 21:32:39,825 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-22 21:32:39,825 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-22 21:32:39,825 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-22 21:32:39,825 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-22 21:32:39,826 - __main__ - INFO - 更新班组关系数据...
2025-07-22 21:32:39,826 - __main__ - INFO - 班组关系数据更新完成
2025-07-22 21:32:39,826 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-22 21:32:39,826 - __main__ - INFO - 开始提取聚集区数据...
2025-07-22 21:32:39,848 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-22 21:32:39,848 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-22 21:32:39,849 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-22 21:32:39,849 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-22 21:32:39,849 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-22 21:32:39,849 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-22 21:32:39,849 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-22 21:32:39,849 - __main__ - INFO - 坐标点样例: ['113.585704,24.7984', '113.646255,24.786377', '114.151281,25.236341']
2025-07-22 21:32:39,989 - __main__ - INFO - 数据库查询返回记录数: 1084
2025-07-22 21:32:39,993 - __main__ - INFO - ==================================================
2025-07-22 21:32:39,993 - __main__ - INFO - 时间矩阵提取结果:
2025-07-22 21:32:39,993 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-22 21:32:39,993 - __main__ - INFO -   数据库中坐标点: 52 个
2025-07-22 21:32:39,993 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-22 21:32:39,993 - __main__ - INFO -   实际获得记录数: 1084
2025-07-22 21:32:39,993 - __main__ - INFO -   覆盖率: 40.9%
2025-07-22 21:32:39,993 - __main__ - INFO - ✅ 所有坐标点都在数据库中找到时间数据
2025-07-22 21:32:39,993 - __main__ - INFO - ==================================================
2025-07-22 21:32:39,993 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-22 21:32:39,993 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-22 21:32:39,998 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 1668 条记录
2025-07-22 21:32:39,998 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-22 21:32:39,998 - __main__ - INFO - 开始验证数据完整性...
2025-07-22 21:32:39,998 - __main__ - INFO - 数据完整性验证通过
2025-07-22 21:32:39,998 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-22 21:32:39,998 - __main__ - INFO - 开始保存JSON文件...
2025-07-22 21:32:40,000 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-22 21:32:40,001 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-22 21:32:40,002 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-22 21:32:40,034 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-22 21:32:40,035 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-22 21:32:40,035 - __main__ - INFO - 数据提取流程完成
2025-07-22 21:32:40,035 - __main__ - INFO - 数据库连接已关闭
2025-07-22 21:48:09,564 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-22 21:48:09,572 - __main__ - INFO - 数据库连接成功
2025-07-22 21:48:09,572 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-22 21:48:09,572 - __main__ - INFO - 开始提取班组数据...
2025-07-22 21:48:09,573 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-22 21:48:09,573 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-22 21:48:09,573 - __main__ - INFO - 开始提取中转站数据...
2025-07-22 21:48:09,573 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-22 21:48:09,573 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-22 21:48:09,573 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-22 21:48:09,573 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-22 21:48:09,573 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-22 21:48:09,573 - __main__ - INFO - 更新班组关系数据...
2025-07-22 21:48:09,573 - __main__ - INFO - 班组关系数据更新完成
2025-07-22 21:48:09,573 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-22 21:48:09,574 - __main__ - INFO - 开始提取聚集区数据...
2025-07-22 21:48:09,583 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-22 21:48:09,583 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-22 21:48:09,583 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-22 21:48:09,583 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-22 21:48:09,583 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-22 21:48:09,583 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-22 21:48:09,584 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个
2025-07-22 21:48:09,584 - __main__ - INFO - 坐标点样例: ['113.635476,24.894997', '113.558708,24.799452', '113.518064,24.751259']
2025-07-22 21:48:09,709 - __main__ - INFO - 数据库查询返回记录数: 1084
2025-07-22 21:48:09,713 - __main__ - INFO - ==================================================
2025-07-22 21:48:09,713 - __main__ - INFO - 时间矩阵提取结果:
2025-07-22 21:48:09,713 - __main__ - INFO -   算法需要坐标点: 52 个
2025-07-22 21:48:09,713 - __main__ - INFO -   数据库中坐标点: 52 个
2025-07-22 21:48:09,713 - __main__ - INFO -   期望记录数(完全图): 2652
2025-07-22 21:48:09,713 - __main__ - INFO -   实际获得记录数: 1084
2025-07-22 21:48:09,713 - __main__ - INFO -   覆盖率: 40.9%
2025-07-22 21:48:09,713 - __main__ - INFO - ✅ 所有坐标点都在数据库中找到时间数据
2025-07-22 21:48:09,713 - __main__ - INFO - 🔍 分析缺失的时间矩阵点对...
2025-07-22 21:48:09,722 - __main__ - WARNING - ❌ 发现 1668 个缺失的点对，以下是前8个示例:
2025-07-22 21:48:09,722 - __main__ - WARNING -   1. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659363840002(浈江区1)
2025-07-22 21:48:09,722 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.566605,24.916563)
2025-07-22 21:48:09,722 - __main__ - WARNING -      距离: 73.25km
2025-07-22 21:48:09,722 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.566605,24.916563
2025-07-22 21:48:09,722 - __main__ - WARNING - 
2025-07-22 21:48:09,722 - __main__ - WARNING -   2. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659426754561(浈江区2)
2025-07-22 21:48:09,722 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.460609,24.979835)
2025-07-22 21:48:09,722 - __main__ - WARNING -      距离: 80.17km
2025-07-22 21:48:09,722 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.460609,24.979835
2025-07-22 21:48:09,722 - __main__ - WARNING - 
2025-07-22 21:48:09,722 - __main__ - WARNING -   3. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659498057729(浈江区3)
2025-07-22 21:48:09,722 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.526327,24.729237)
2025-07-22 21:48:09,722 - __main__ - WARNING -      距离: 88.17km
2025-07-22 21:48:09,722 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.526327,24.729237
2025-07-22 21:48:09,722 - __main__ - WARNING - 
2025-07-22 21:48:09,722 - __main__ - WARNING -   4. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659565166593(浈江区4)
2025-07-22 21:48:09,722 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.646255,24.786377)
2025-07-22 21:48:09,722 - __main__ - WARNING -      距离: 74.80km
2025-07-22 21:48:09,722 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.646255,24.786377
2025-07-22 21:48:09,722 - __main__ - WARNING - 
2025-07-22 21:48:09,723 - __main__ - WARNING -   5. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659632275458(浈江区5)
2025-07-22 21:48:09,723 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.635476,24.894997)
2025-07-22 21:48:09,723 - __main__ - WARNING -      距离: 68.45km
2025-07-22 21:48:09,723 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.635476,24.894997
2025-07-22 21:48:09,723 - __main__ - WARNING - 
2025-07-22 21:48:09,723 - __main__ - WARNING -   6. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659699384321(浈江区6)
2025-07-22 21:48:09,723 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.666729,24.856092)
2025-07-22 21:48:09,723 - __main__ - WARNING -      距离: 68.32km
2025-07-22 21:48:09,723 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.666729,24.856092
2025-07-22 21:48:09,723 - __main__ - WARNING - 
2025-07-22 21:48:09,723 - __main__ - WARNING -   7. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659766493186(浈江区7)
2025-07-22 21:48:09,723 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.504393,24.915050)
2025-07-22 21:48:09,723 - __main__ - WARNING -      距离: 78.92km
2025-07-22 21:48:09,723 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.504393,24.915050
2025-07-22 21:48:09,723 - __main__ - WARNING - 
2025-07-22 21:48:09,723 - __main__ - WARNING -   8. 聚集区1906559422503104514(南雄市1) -> 聚集区1906559659837796354(浈江区8)
2025-07-22 21:48:09,723 - __main__ - WARNING -      坐标: (114.209602,25.224353) -> (113.550133,24.883990)
2025-07-22 21:48:09,723 - __main__ - WARNING -      距离: 76.45km
2025-07-22 21:48:09,723 - __main__ - WARNING -      矩阵键: 114.209602,25.224353->113.550133,24.883990
2025-07-22 21:48:09,723 - __main__ - WARNING - 
2025-07-22 21:48:09,723 - __main__ - WARNING -   ... 还有 1660 个缺失点对未显示
2025-07-22 21:48:09,723 - __main__ - INFO - ==================================================
2025-07-22 21:48:09,723 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-22 21:48:09,726 - __main__ - INFO - 生成缺失的时间矩阵数据...
2025-07-22 21:48:09,731 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 1668 条记录
2025-07-22 21:48:09,731 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-22 21:48:09,731 - __main__ - INFO - 开始验证数据完整性...
2025-07-22 21:48:09,731 - __main__ - INFO - 数据完整性验证通过
2025-07-22 21:48:09,731 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-22 21:48:09,731 - __main__ - INFO - 开始保存JSON文件...
2025-07-22 21:48:09,732 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-22 21:48:09,733 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-22 21:48:09,733 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-22 21:48:09,757 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-22 21:48:09,757 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-22 21:48:09,757 - __main__ - INFO - 数据提取流程完成
2025-07-22 21:48:09,757 - __main__ - INFO - 数据库连接已关闭
2025-07-22 22:07:34,673 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-22 22:07:34,681 - __main__ - INFO - 数据库连接成功
2025-07-22 22:07:34,681 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-22 22:07:34,681 - __main__ - INFO - 开始提取班组数据...
2025-07-22 22:07:34,682 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-22 22:07:34,682 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-22 22:07:34,682 - __main__ - INFO - 开始提取中转站数据...
2025-07-22 22:07:34,683 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-22 22:07:34,683 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-22 22:07:34,683 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-22 22:07:34,683 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-22 22:07:34,683 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-22 22:07:34,683 - __main__ - INFO - 更新班组关系数据...
2025-07-22 22:07:34,683 - __main__ - INFO - 班组关系数据更新完成
2025-07-22 22:07:34,683 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-22 22:07:34,683 - __main__ - INFO - 开始提取聚集区数据...
2025-07-22 22:07:34,693 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-22 22:07:34,693 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-22 22:07:34,693 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-22 22:07:34,693 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-22 22:07:34,693 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-22 22:07:34,693 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-22 22:07:34,693 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个 (分布在 3 个中转站)
2025-07-22 22:07:34,693 - __main__ - INFO - 预期时间矩阵点对数: 884 个 (仅限中转站内部)
2025-07-22 22:07:34,693 - __main__ - INFO -   中转站4: 17个点 -> 272个点对
2025-07-22 22:07:34,693 - __main__ - INFO -   中转站5: 18个点 -> 306个点对
2025-07-22 22:07:34,693 - __main__ - INFO -   中转站6: 18个点 -> 306个点对
2025-07-22 22:07:34,693 - __main__ - INFO - 坐标点样例: ['114.531416,25.352578', '113.526114,24.778843', '113.517758,24.769346']
2025-07-22 22:07:34,814 - __main__ - INFO - 数据库查询返回记录数: 1084
2025-07-22 22:07:34,819 - __main__ - INFO - ==================================================
2025-07-22 22:07:34,819 - __main__ - INFO - 时间矩阵提取结果 (仅限中转站内部):
2025-07-22 22:07:34,819 - __main__ - INFO -   算法需要坐标点: 52 个 (分布在 3 个中转站)
2025-07-22 22:07:34,819 - __main__ - INFO -   数据库中坐标点: 52 个
2025-07-22 22:07:34,819 - __main__ - INFO -   期望记录数(中转站内部): 884
2025-07-22 22:07:34,819 - __main__ - INFO -   实际获得记录数: 1084
2025-07-22 22:07:34,819 - __main__ - INFO -   覆盖率: 122.6%
2025-07-22 22:07:34,822 - __main__ - INFO -   中转站4(17点): 272/272 = 100.0%
2025-07-22 22:07:34,825 - __main__ - INFO -   中转站5(18点): 306/306 = 100.0%
2025-07-22 22:07:34,828 - __main__ - INFO -   中转站6(18点): 306/306 = 100.0%
2025-07-22 22:07:34,828 - __main__ - INFO - ✅ 所有坐标点都在数据库中找到时间数据
2025-07-22 22:07:34,828 - __main__ - INFO - ==================================================
2025-07-22 22:07:34,828 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-22 22:07:34,828 - __main__ - INFO - 生成缺失的时间矩阵数据 (仅限中转站内部)...
2025-07-22 22:07:34,828 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 0 条记录 (仅限中转站内部)
2025-07-22 22:07:34,829 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-22 22:07:34,829 - __main__ - INFO - 开始验证数据完整性...
2025-07-22 22:07:34,829 - __main__ - WARNING - 数据完整性验证发现问题:
2025-07-22 22:07:34,829 - __main__ - WARNING -   - 时间矩阵覆盖率较低: 37.10% (984/2652)
2025-07-22 22:07:34,829 - __main__ - WARNING - 数据完整性验证失败，但将继续保存
2025-07-22 22:07:34,829 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-22 22:07:34,829 - __main__ - INFO - 开始保存JSON文件...
2025-07-22 22:07:34,830 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-22 22:07:34,830 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-22 22:07:34,831 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-22 22:07:34,840 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-22 22:07:34,841 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-22 22:07:34,841 - __main__ - INFO - 数据提取流程完成
2025-07-22 22:07:34,841 - __main__ - INFO - 数据库连接已关闭
2025-07-22 22:11:59,669 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-22 22:11:59,676 - __main__ - INFO - 数据库连接成功
2025-07-22 22:11:59,676 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-22 22:11:59,676 - __main__ - INFO - 开始提取班组数据...
2025-07-22 22:11:59,677 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-22 22:11:59,677 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-22 22:11:59,677 - __main__ - INFO - 开始提取中转站数据...
2025-07-22 22:11:59,678 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-22 22:11:59,678 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-22 22:11:59,678 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-22 22:11:59,678 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-22 22:11:59,678 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-22 22:11:59,678 - __main__ - INFO - 更新班组关系数据...
2025-07-22 22:11:59,678 - __main__ - INFO - 班组关系数据更新完成
2025-07-22 22:11:59,678 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-22 22:11:59,678 - __main__ - INFO - 开始提取聚集区数据...
2025-07-22 22:11:59,688 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-22 22:11:59,688 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-22 22:11:59,688 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-22 22:11:59,688 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-22 22:11:59,688 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-22 22:11:59,688 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-22 22:11:59,688 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个 (分布在 3 个中转站)
2025-07-22 22:11:59,688 - __main__ - INFO - 预期时间矩阵点对数: 884 个 (仅限中转站内部)
2025-07-22 22:11:59,688 - __main__ - INFO -   中转站4: 17个点 -> 272个点对
2025-07-22 22:11:59,688 - __main__ - INFO -   中转站5: 18个点 -> 306个点对
2025-07-22 22:11:59,688 - __main__ - INFO -   中转站6: 18个点 -> 306个点对
2025-07-22 22:11:59,688 - __main__ - INFO - 坐标点样例: ['113.580219,24.827379', '114.024063,25.18527', '114.692249,25.281171']
2025-07-22 22:11:59,813 - __main__ - INFO - 数据库查询返回记录数: 1084
2025-07-22 22:11:59,816 - __main__ - INFO - ==================================================
2025-07-22 22:11:59,816 - __main__ - INFO - 时间矩阵提取结果 (仅限中转站内部):
2025-07-22 22:11:59,816 - __main__ - INFO -   算法需要坐标点: 52 个 (分布在 3 个中转站)
2025-07-22 22:11:59,816 - __main__ - INFO -   数据库中坐标点: 52 个
2025-07-22 22:11:59,816 - __main__ - INFO -   期望记录数(中转站内部): 884
2025-07-22 22:11:59,816 - __main__ - INFO -   实际获得记录数: 1084
2025-07-22 22:11:59,816 - __main__ - INFO -   覆盖率: 122.6%
2025-07-22 22:11:59,820 - __main__ - INFO -   中转站4(17点): 272/272 = 100.0%
2025-07-22 22:11:59,823 - __main__ - INFO -   中转站5(18点): 306/306 = 100.0%
2025-07-22 22:11:59,826 - __main__ - INFO -   中转站6(18点): 306/306 = 100.0%
2025-07-22 22:11:59,826 - __main__ - INFO - ✅ 所有坐标点都在数据库中找到时间数据
2025-07-22 22:11:59,826 - __main__ - INFO - ==================================================
2025-07-22 22:11:59,826 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-22 22:11:59,826 - __main__ - INFO - 生成缺失的时间矩阵数据 (仅限中转站内部)...
2025-07-22 22:11:59,826 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 0 条记录 (仅限中转站内部)
2025-07-22 22:11:59,826 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-22 22:11:59,826 - __main__ - INFO - 开始验证数据完整性...
2025-07-22 22:11:59,826 - __main__ - INFO - 数据完整性验证通过
2025-07-22 22:11:59,827 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-22 22:11:59,827 - __main__ - INFO - 开始保存JSON文件...
2025-07-22 22:11:59,828 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-22 22:11:59,828 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-22 22:11:59,829 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-22 22:11:59,839 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-22 22:11:59,839 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-22 22:11:59,839 - __main__ - INFO - 数据提取流程完成
2025-07-22 22:11:59,839 - __main__ - INFO - 数据库连接已关闭
2025-07-22 22:20:33,135 - __main__ - INFO - 开始数据提取 - 规模: small, 版本: v1.0
2025-07-22 22:20:33,144 - __main__ - INFO - 数据库连接成功
2025-07-22 22:20:33,144 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-22 22:20:33,144 - __main__ - INFO - 开始提取班组数据...
2025-07-22 22:20:33,144 - __main__ - INFO - 提取班组数据完成，共 3 条记录
2025-07-22 22:20:33,145 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-22 22:20:33,145 - __main__ - INFO - 开始提取中转站数据...
2025-07-22 22:20:33,145 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-22 22:20:33,145 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-22 22:20:33,145 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-22 22:20:33,145 - __main__ - INFO - 提取中转站数据完成，共 3 条记录
2025-07-22 22:20:33,145 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-22 22:20:33,145 - __main__ - INFO - 更新班组关系数据...
2025-07-22 22:20:33,145 - __main__ - INFO - 班组关系数据更新完成
2025-07-22 22:20:33,145 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-22 22:20:33,145 - __main__ - INFO - 开始提取聚集区数据...
2025-07-22 22:20:33,154 - __main__ - INFO - 中转站 4 分配聚集区: 16 个
2025-07-22 22:20:33,155 - __main__ - INFO - 中转站 5 分配聚集区: 17 个
2025-07-22 22:20:33,155 - __main__ - INFO - 中转站 6 分配聚集区: 17 个
2025-07-22 22:20:33,155 - __main__ - INFO - 提取聚集区数据完成，共 50 条记录
2025-07-22 22:20:33,155 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-22 22:20:33,155 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-22 22:20:33,155 - __main__ - INFO - 需要查找时间矩阵的坐标点: 52 个 (分布在 3 个中转站)
2025-07-22 22:20:33,155 - __main__ - INFO - 预期时间矩阵点对数: 884 个 (仅限中转站内部)
2025-07-22 22:20:33,155 - __main__ - INFO -   中转站4: 17个点 -> 272个点对
2025-07-22 22:20:33,155 - __main__ - INFO -   中转站5: 18个点 -> 306个点对
2025-07-22 22:20:33,155 - __main__ - INFO -   中转站6: 18个点 -> 306个点对
2025-07-22 22:20:33,155 - __main__ - INFO - 坐标点样例: ['113.543798,24.795991', '113.563022,24.96761', '113.58208,24.75415']
2025-07-22 22:20:33,262 - __main__ - INFO - 中转站4查询返回记录数: 272
2025-07-22 22:20:33,371 - __main__ - INFO - 中转站5查询返回记录数: 340
2025-07-22 22:20:33,478 - __main__ - INFO - 中转站6查询返回记录数: 340
2025-07-22 22:20:33,478 - __main__ - INFO - 总查询返回记录数: 952
2025-07-22 22:20:33,482 - __main__ - INFO - ==================================================
2025-07-22 22:20:33,482 - __main__ - INFO - 时间矩阵提取结果 (仅限中转站内部):
2025-07-22 22:20:33,482 - __main__ - INFO -   算法需要坐标点: 52 个 (分布在 3 个中转站)
2025-07-22 22:20:33,482 - __main__ - INFO -   数据库中坐标点: 52 个
2025-07-22 22:20:33,482 - __main__ - INFO -   期望记录数(中转站内部): 884
2025-07-22 22:20:33,482 - __main__ - INFO -   实际获得记录数: 952
2025-07-22 22:20:33,482 - __main__ - INFO -   覆盖率: 107.7%
2025-07-22 22:20:33,485 - __main__ - INFO -   中转站4(17点): 272/272 = 100.0%
2025-07-22 22:20:33,487 - __main__ - INFO -   中转站5(18点): 306/306 = 100.0%
2025-07-22 22:20:33,490 - __main__ - INFO -   中转站6(18点): 306/306 = 100.0%
2025-07-22 22:20:33,490 - __main__ - INFO - ✅ 所有坐标点都在数据库中找到时间数据
2025-07-22 22:20:33,490 - __main__ - INFO - ==================================================
2025-07-22 22:20:33,490 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-22 22:20:33,490 - __main__ - INFO - 生成缺失的时间矩阵数据 (仅限中转站内部)...
2025-07-22 22:20:33,491 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 0 条记录 (仅限中转站内部)
2025-07-22 22:20:33,491 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-22 22:20:33,491 - __main__ - INFO - 开始验证数据完整性...
2025-07-22 22:20:33,491 - __main__ - INFO - 数据完整性验证通过
2025-07-22 22:20:33,491 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-22 22:20:33,491 - __main__ - INFO - 开始保存JSON文件...
2025-07-22 22:20:33,492 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-22 22:20:33,493 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-22 22:20:33,494 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-22 22:20:33,502 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-22 22:20:33,502 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-22 22:20:33,502 - __main__ - INFO - 数据提取流程完成
2025-07-22 22:20:33,503 - __main__ - INFO - 数据库连接已关闭
2025-07-22 23:15:17,683 - __main__ - INFO - 开始数据提取 - 规模: full, 版本: v1.0
2025-07-22 23:15:17,692 - __main__ - INFO - 数据库连接成功
2025-07-22 23:15:17,693 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-22 23:15:17,693 - __main__ - INFO - 开始提取班组数据...
2025-07-22 23:15:17,693 - __main__ - INFO - 提取班组数据完成，共 6 条记录
2025-07-22 23:15:17,693 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-22 23:15:17,693 - __main__ - INFO - 开始提取中转站数据...
2025-07-22 23:15:17,694 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-22 23:15:17,694 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-22 23:15:17,694 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-22 23:15:17,694 - __main__ - INFO - 班组 4 分配中转站: 1 个
2025-07-22 23:15:17,694 - __main__ - INFO - 班组 5 分配中转站: 2 个
2025-07-22 23:15:17,694 - __main__ - INFO - 提取中转站数据完成，共 6 条记录
2025-07-22 23:15:17,694 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-22 23:15:17,694 - __main__ - INFO - 更新班组关系数据...
2025-07-22 23:15:17,694 - __main__ - INFO - 班组关系数据更新完成
2025-07-22 23:15:17,694 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-22 23:15:17,694 - __main__ - INFO - 开始提取聚集区数据...
2025-07-22 23:15:17,709 - __main__ - INFO - 中转站 1 分配聚集区: 125 个
2025-07-22 23:15:17,709 - __main__ - INFO - 中转站 2 分配聚集区: 235 个
2025-07-22 23:15:17,709 - __main__ - INFO - 中转站 3 分配聚集区: 165 个
2025-07-22 23:15:17,709 - __main__ - INFO - 中转站 4 分配聚集区: 339 个
2025-07-22 23:15:17,710 - __main__ - INFO - 中转站 5 分配聚集区: 507 个
2025-07-22 23:15:17,710 - __main__ - INFO - 中转站 6 分配聚集区: 300 个
2025-07-22 23:15:17,710 - __main__ - INFO - 提取聚集区数据完成，共 1671 条记录
2025-07-22 23:15:17,710 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-22 23:15:17,710 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-22 23:15:17,712 - __main__ - INFO - 需要查找时间矩阵的坐标点: 1676 个 (分布在 6 个中转站)
2025-07-22 23:15:17,712 - __main__ - INFO - 预期时间矩阵点对数: 561716 个 (仅限中转站内部)
2025-07-22 23:15:17,712 - __main__ - INFO -   中转站1: 126个点 -> 15750个点对
2025-07-22 23:15:17,712 - __main__ - INFO -   中转站2: 236个点 -> 55460个点对
2025-07-22 23:15:17,712 - __main__ - INFO -   中转站3: 166个点 -> 27390个点对
2025-07-22 23:15:17,712 - __main__ - INFO -   中转站4: 340个点 -> 115260个点对
2025-07-22 23:15:17,712 - __main__ - INFO -   中转站5: 508个点 -> 257556个点对
2025-07-22 23:15:17,712 - __main__ - INFO -   中转站6: 301个点 -> 90300个点对
2025-07-22 23:15:17,712 - __main__ - INFO - 坐标点样例: ['114.115581,23.995913', '114.087682,24.94275', '113.585929,24.825622']
2025-07-22 23:15:17,952 - __main__ - INFO - 中转站1查询返回记录数: 15750
2025-07-22 23:15:18,379 - __main__ - INFO - 中转站2查询返回记录数: 49506
2025-07-22 23:15:18,661 - __main__ - INFO - 中转站3查询返回记录数: 27390
2025-07-22 23:15:19,181 - __main__ - INFO - 中转站4查询返回记录数: 62312
2025-07-22 23:15:19,909 - __main__ - INFO - 中转站5查询返回记录数: 91908
2025-07-22 23:15:20,182 - __main__ - INFO - 中转站6查询返回记录数: 27524
2025-07-22 23:15:20,182 - __main__ - INFO - 总查询返回记录数: 274390
2025-07-22 23:15:21,269 - __main__ - INFO - ==================================================
2025-07-22 23:15:21,269 - __main__ - INFO - 时间矩阵提取结果 (仅限中转站内部):
2025-07-22 23:15:21,269 - __main__ - INFO -   算法需要坐标点: 1676 个 (分布在 6 个中转站)
2025-07-22 23:15:21,269 - __main__ - INFO -   数据库中坐标点: 1556 个
2025-07-22 23:15:21,269 - __main__ - INFO -   期望记录数(中转站内部): 561716
2025-07-22 23:15:21,269 - __main__ - INFO -   实际获得记录数: 274390
2025-07-22 23:15:21,269 - __main__ - INFO -   覆盖率: 48.8%
2025-07-22 23:15:22,089 - __main__ - INFO -   中转站1(126点): 15750/15750 = 100.0%
2025-07-22 23:15:22,908 - __main__ - INFO -   中转站2(236点): 49506/55460 = 89.3%
2025-07-22 23:15:23,722 - __main__ - INFO -   中转站3(166点): 27390/27390 = 100.0%
2025-07-22 23:15:24,572 - __main__ - INFO -   中转站4(340点): 62312/115260 = 54.1%
2025-07-22 23:15:25,434 - __main__ - INFO -   中转站5(508点): 90894/257556 = 35.3%
2025-07-22 23:15:26,297 - __main__ - INFO -   中转站6(301点): 27138/90300 = 30.1%
2025-07-22 23:15:26,298 - __main__ - WARNING - ⚠️  120 个坐标点在数据库中无时间数据:
2025-07-22 23:15:26,298 - __main__ - WARNING -     缺失: 113.027563,24.565083 (聚集区1906559986263699457)
2025-07-22 23:15:26,298 - __main__ - WARNING -     缺失: 113.061452,24.675266 (聚集区1906559986196590594)
2025-07-22 23:15:26,298 - __main__ - WARNING -     缺失: 113.089315,24.921496 (聚集区1906559986460831746)
2025-07-22 23:15:26,298 - __main__ - WARNING -     缺失: 113.099027,24.971237 (聚集区1906560265067474945)
2025-07-22 23:15:26,298 - __main__ - WARNING -     缺失: 113.123728,25.038126 (聚集区1906560264929062913)
2025-07-22 23:15:26,298 - __main__ - WARNING -     ... 还有 115 个
2025-07-22 23:15:26,298 - __main__ - INFO - 🔍 分析缺失的时间矩阵点对 (仅限中转站内部)...
2025-07-22 23:15:27,185 - __main__ - WARNING - ❌ 发现 288726 个缺失的中转站内部点对，以下是前8个示例:
2025-07-22 23:15:27,186 - __main__ - WARNING -   1. 中转站2(坪石镇中转站) -> 聚集区1906560264715153410(大桥镇1) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.142350,24.917176)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 45.38km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   2. 中转站2(坪石镇中转站) -> 聚集区1906560264786456577(大桥镇2) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.774273,24.916178)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 87.63km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   3. 中转站2(坪石镇中转站) -> 聚集区1906560264853565442(大桥镇3) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.757754,24.892904)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 87.54km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   4. 中转站2(坪石镇中转站) -> 聚集区1906560264929062913(大桥镇4) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.123728,25.038126)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 31.98km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   5. 中转站2(坪石镇中转站) -> 聚集区1906560264996171778(大桥镇5) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.170874,24.989563)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 38.72km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   6. 中转站2(坪石镇中转站) -> 聚集区1906560265067474945(大桥镇6) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.099027,24.971237)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 38.50km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   7. 中转站2(坪石镇中转站) -> 聚集区1906560265138778113(大桥镇7) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.146059,25.012112)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 35.46km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   8. 中转站2(坪石镇中转站) -> 聚集区1906560265210081281(大桥镇8) (中转站2)
2025-07-22 23:15:27,186 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.142185,24.977573)
2025-07-22 23:15:27,186 - __main__ - WARNING -      距离: 38.95km
2025-07-22 23:15:27,186 - __main__ - WARNING - 
2025-07-22 23:15:27,186 - __main__ - WARNING -   ... 还有 288718 个缺失点对未显示
2025-07-22 23:15:27,186 - __main__ - INFO - ==================================================
2025-07-22 23:15:27,218 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-22 23:15:27,218 - __main__ - INFO - 生成缺失的时间矩阵数据 (仅限中转站内部)...
2025-07-22 23:15:28,229 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 288726 条记录 (仅限中转站内部)
2025-07-22 23:15:28,229 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-22 23:15:28,230 - __main__ - INFO - 开始验证数据完整性...
2025-07-22 23:15:28,230 - __main__ - INFO - 数据完整性验证通过
2025-07-22 23:15:28,230 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-22 23:15:28,230 - __main__ - INFO - 开始保存JSON文件...
2025-07-22 23:15:28,247 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-22 23:15:28,248 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-22 23:15:28,249 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-22 23:15:33,500 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-22 23:15:33,501 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-22 23:15:33,501 - __main__ - INFO - 数据提取流程完成
2025-07-22 23:15:33,501 - __main__ - INFO - 数据库连接已关闭
2025-07-23 21:49:24,775 - __main__ - INFO - 开始数据提取 - 规模: full, 版本: v1.0
2025-07-23 21:49:24,825 - __main__ - INFO - 数据库连接成功
2025-07-23 21:49:24,825 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-23 21:49:24,825 - __main__ - INFO - 开始提取班组数据...
2025-07-23 21:49:24,833 - __main__ - INFO - 提取班组数据完成，共 6 条记录
2025-07-23 21:49:24,833 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-23 21:49:24,833 - __main__ - INFO - 开始提取中转站数据...
2025-07-23 21:49:24,836 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-23 21:49:24,836 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-23 21:49:24,836 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-23 21:49:24,836 - __main__ - INFO - 班组 4 分配中转站: 1 个
2025-07-23 21:49:24,836 - __main__ - INFO - 班组 5 分配中转站: 2 个
2025-07-23 21:49:24,836 - __main__ - INFO - 提取中转站数据完成，共 6 条记录
2025-07-23 21:49:24,836 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-23 21:49:24,836 - __main__ - INFO - 更新班组关系数据...
2025-07-23 21:49:24,836 - __main__ - INFO - 班组关系数据更新完成
2025-07-23 21:49:24,836 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-23 21:49:24,836 - __main__ - INFO - 开始提取聚集区数据...
2025-07-23 21:49:24,870 - __main__ - INFO - 中转站 1 分配聚集区: 125 个
2025-07-23 21:49:24,870 - __main__ - INFO - 中转站 2 分配聚集区: 235 个
2025-07-23 21:49:24,871 - __main__ - INFO - 中转站 3 分配聚集区: 165 个
2025-07-23 21:49:24,871 - __main__ - INFO - 中转站 4 分配聚集区: 339 个
2025-07-23 21:49:24,871 - __main__ - INFO - 中转站 5 分配聚集区: 507 个
2025-07-23 21:49:24,871 - __main__ - INFO - 中转站 6 分配聚集区: 300 个
2025-07-23 21:49:24,871 - __main__ - INFO - ==================================================
2025-07-23 21:49:24,871 - __main__ - INFO - 配送时间数据统计:
2025-07-23 21:49:24,871 - __main__ - INFO -   使用实际卸货时间: 1519 个聚集区
2025-07-23 21:49:24,871 - __main__ - INFO -   使用默认配送时间: 152 个聚集区
2025-07-23 21:49:24,871 - __main__ - INFO -   实际数据覆盖率: 90.9%
2025-07-23 21:49:24,871 - __main__ - INFO -   平均配送时间: 16.8 分钟
2025-07-23 21:49:24,871 - __main__ - INFO -   最短配送时间: 0.0 分钟
2025-07-23 21:49:24,871 - __main__ - INFO -   最长配送时间: 56.2 分钟
2025-07-23 21:49:24,871 - __main__ - INFO - ==================================================
2025-07-23 21:49:24,871 - __main__ - INFO - 提取聚集区数据完成，共 1671 条记录
2025-07-23 21:49:24,871 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-23 21:49:24,871 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-23 21:49:24,876 - __main__ - INFO - 需要查找时间矩阵的坐标点: 1676 个 (分布在 6 个中转站)
2025-07-23 21:49:24,876 - __main__ - INFO - 预期时间矩阵点对数: 561716 个 (仅限中转站内部)
2025-07-23 21:49:24,876 - __main__ - INFO -   中转站1: 126个点 -> 15750个点对
2025-07-23 21:49:24,876 - __main__ - INFO -   中转站2: 236个点 -> 55460个点对
2025-07-23 21:49:24,876 - __main__ - INFO -   中转站3: 166个点 -> 27390个点对
2025-07-23 21:49:24,876 - __main__ - INFO -   中转站4: 340个点 -> 115260个点对
2025-07-23 21:49:24,876 - __main__ - INFO -   中转站5: 508个点 -> 257556个点对
2025-07-23 21:49:24,876 - __main__ - INFO -   中转站6: 301个点 -> 90300个点对
2025-07-23 21:49:24,876 - __main__ - INFO - 坐标点样例: ['113.827076,24.706904', '114.348022,24.112482', '114.108442,24.354766']
2025-07-23 21:49:25,255 - __main__ - INFO - 中转站1查询返回记录数: 15750
2025-07-23 21:49:25,812 - __main__ - INFO - 中转站2查询返回记录数: 49506
2025-07-23 21:49:26,097 - __main__ - INFO - 中转站3查询返回记录数: 27390
2025-07-23 21:49:26,657 - __main__ - INFO - 中转站4查询返回记录数: 62312
2025-07-23 21:49:27,392 - __main__ - INFO - 中转站5查询返回记录数: 91908
2025-07-23 21:49:27,671 - __main__ - INFO - 中转站6查询返回记录数: 27524
2025-07-23 21:49:27,671 - __main__ - INFO - 总查询返回记录数: 274390
2025-07-23 21:49:28,808 - __main__ - INFO - ==================================================
2025-07-23 21:49:28,809 - __main__ - INFO - 时间矩阵提取结果 (仅限中转站内部):
2025-07-23 21:49:28,809 - __main__ - INFO -   算法需要坐标点: 1676 个 (分布在 6 个中转站)
2025-07-23 21:49:28,809 - __main__ - INFO -   数据库中坐标点: 1556 个
2025-07-23 21:49:28,809 - __main__ - INFO -   期望记录数(中转站内部): 561716
2025-07-23 21:49:28,809 - __main__ - INFO -   实际获得记录数: 274390
2025-07-23 21:49:28,809 - __main__ - INFO -   覆盖率: 48.8%
2025-07-23 21:49:29,625 - __main__ - INFO -   中转站1(126点): 15750/15750 = 100.0%
2025-07-23 21:49:30,425 - __main__ - INFO -   中转站2(236点): 49506/55460 = 89.3%
2025-07-23 21:49:31,209 - __main__ - INFO -   中转站3(166点): 27390/27390 = 100.0%
2025-07-23 21:49:31,993 - __main__ - INFO -   中转站4(340点): 62312/115260 = 54.1%
2025-07-23 21:49:32,799 - __main__ - INFO -   中转站5(508点): 90894/257556 = 35.3%
2025-07-23 21:49:33,645 - __main__ - INFO -   中转站6(301点): 27138/90300 = 30.1%
2025-07-23 21:49:33,645 - __main__ - WARNING - ⚠️  120 个坐标点在数据库中无时间数据:
2025-07-23 21:49:33,645 - __main__ - WARNING -     缺失: 113.027563,24.565083 (聚集区1906559986263699457)
2025-07-23 21:49:33,645 - __main__ - WARNING -     缺失: 113.061452,24.675266 (聚集区1906559986196590594)
2025-07-23 21:49:33,645 - __main__ - WARNING -     缺失: 113.089315,24.921496 (聚集区1906559986460831746)
2025-07-23 21:49:33,645 - __main__ - WARNING -     缺失: 113.099027,24.971237 (聚集区1906560265067474945)
2025-07-23 21:49:33,645 - __main__ - WARNING -     缺失: 113.123728,25.038126 (聚集区1906560264929062913)
2025-07-23 21:49:33,645 - __main__ - WARNING -     ... 还有 115 个
2025-07-23 21:49:33,645 - __main__ - INFO - 🔍 分析缺失的时间矩阵点对 (仅限中转站内部)...
2025-07-23 21:49:34,469 - __main__ - WARNING - ❌ 发现 288726 个缺失的中转站内部点对，以下是前8个示例:
2025-07-23 21:49:34,469 - __main__ - WARNING -   1. 中转站2(坪石镇中转站) -> 聚集区1906560264715153410(大桥镇1) (中转站2)
2025-07-23 21:49:34,469 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.142350,24.917176)
2025-07-23 21:49:34,469 - __main__ - WARNING -      距离: 45.38km
2025-07-23 21:49:34,469 - __main__ - WARNING - 
2025-07-23 21:49:34,469 - __main__ - WARNING -   2. 中转站2(坪石镇中转站) -> 聚集区1906560264786456577(大桥镇2) (中转站2)
2025-07-23 21:49:34,469 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.774273,24.916178)
2025-07-23 21:49:34,469 - __main__ - WARNING -      距离: 87.63km
2025-07-23 21:49:34,469 - __main__ - WARNING - 
2025-07-23 21:49:34,469 - __main__ - WARNING -   3. 中转站2(坪石镇中转站) -> 聚集区1906560264853565442(大桥镇3) (中转站2)
2025-07-23 21:49:34,469 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.757754,24.892904)
2025-07-23 21:49:34,470 - __main__ - WARNING -      距离: 87.54km
2025-07-23 21:49:34,470 - __main__ - WARNING - 
2025-07-23 21:49:34,470 - __main__ - WARNING -   4. 中转站2(坪石镇中转站) -> 聚集区1906560264929062913(大桥镇4) (中转站2)
2025-07-23 21:49:34,470 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.123728,25.038126)
2025-07-23 21:49:34,470 - __main__ - WARNING -      距离: 31.98km
2025-07-23 21:49:34,470 - __main__ - WARNING - 
2025-07-23 21:49:34,470 - __main__ - WARNING -   5. 中转站2(坪石镇中转站) -> 聚集区1906560264996171778(大桥镇5) (中转站2)
2025-07-23 21:49:34,470 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.170874,24.989563)
2025-07-23 21:49:34,470 - __main__ - WARNING -      距离: 38.72km
2025-07-23 21:49:34,470 - __main__ - WARNING - 
2025-07-23 21:49:34,470 - __main__ - WARNING -   6. 中转站2(坪石镇中转站) -> 聚集区1906560265067474945(大桥镇6) (中转站2)
2025-07-23 21:49:34,470 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.099027,24.971237)
2025-07-23 21:49:34,470 - __main__ - WARNING -      距离: 38.50km
2025-07-23 21:49:34,470 - __main__ - WARNING - 
2025-07-23 21:49:34,470 - __main__ - WARNING -   7. 中转站2(坪石镇中转站) -> 聚集区1906560265138778113(大桥镇7) (中转站2)
2025-07-23 21:49:34,470 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.146059,25.012112)
2025-07-23 21:49:34,470 - __main__ - WARNING -      距离: 35.46km
2025-07-23 21:49:34,470 - __main__ - WARNING - 
2025-07-23 21:49:34,470 - __main__ - WARNING -   8. 中转站2(坪石镇中转站) -> 聚集区1906560265210081281(大桥镇8) (中转站2)
2025-07-23 21:49:34,470 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.142185,24.977573)
2025-07-23 21:49:34,470 - __main__ - WARNING -      距离: 38.95km
2025-07-23 21:49:34,470 - __main__ - WARNING - 
2025-07-23 21:49:34,470 - __main__ - WARNING -   ... 还有 288718 个缺失点对未显示
2025-07-23 21:49:34,470 - __main__ - INFO - ==================================================
2025-07-23 21:49:34,501 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-23 21:49:34,502 - __main__ - INFO - 生成缺失的时间矩阵数据 (仅限中转站内部)...
2025-07-23 21:49:35,434 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 288726 条记录 (仅限中转站内部)
2025-07-23 21:49:35,434 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-23 21:49:35,434 - __main__ - INFO - 开始验证数据完整性...
2025-07-23 21:49:35,434 - __main__ - INFO - 数据完整性验证通过
2025-07-23 21:49:35,434 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-23 21:49:35,434 - __main__ - INFO - 开始保存JSON文件...
2025-07-23 21:49:35,449 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-23 21:49:35,450 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-23 21:49:35,450 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-23 21:49:40,434 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-23 21:49:40,435 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-23 21:49:40,435 - __main__ - INFO - 数据提取流程完成
2025-07-23 21:49:40,435 - __main__ - INFO - 数据库连接已关闭
2025-07-23 21:54:36,049 - __main__ - INFO - 开始数据提取 - 规模: full, 版本: v1.0
2025-07-23 21:54:36,056 - __main__ - INFO - 数据库连接成功
2025-07-23 21:54:36,056 - __main__ - INFO - === 第1步：提取班组数据（顶层） ===
2025-07-23 21:54:36,056 - __main__ - INFO - 开始提取班组数据...
2025-07-23 21:54:36,057 - __main__ - INFO - 提取班组数据完成，共 6 条记录
2025-07-23 21:54:36,057 - __main__ - INFO - === 第2步：提取中转站数据（基于班组关系） ===
2025-07-23 21:54:36,057 - __main__ - INFO - 开始提取中转站数据...
2025-07-23 21:54:36,057 - __main__ - INFO - 班组 1 分配中转站: 1 个
2025-07-23 21:54:36,057 - __main__ - INFO - 班组 2 分配中转站: 1 个
2025-07-23 21:54:36,057 - __main__ - INFO - 班组 3 分配中转站: 1 个
2025-07-23 21:54:36,057 - __main__ - INFO - 班组 4 分配中转站: 1 个
2025-07-23 21:54:36,057 - __main__ - INFO - 班组 5 分配中转站: 2 个
2025-07-23 21:54:36,057 - __main__ - INFO - 提取中转站数据完成，共 6 条记录
2025-07-23 21:54:36,057 - __main__ - INFO - === 第3步：更新班组关系数据 ===
2025-07-23 21:54:36,057 - __main__ - INFO - 更新班组关系数据...
2025-07-23 21:54:36,057 - __main__ - INFO - 班组关系数据更新完成
2025-07-23 21:54:36,057 - __main__ - INFO - === 第4步：提取聚集区数据（基于中转站关系） ===
2025-07-23 21:54:36,057 - __main__ - INFO - 开始提取聚集区数据...
2025-07-23 21:54:36,074 - __main__ - INFO - 中转站 1 分配聚集区: 125 个
2025-07-23 21:54:36,074 - __main__ - INFO - 中转站 2 分配聚集区: 235 个
2025-07-23 21:54:36,075 - __main__ - INFO - 中转站 3 分配聚集区: 165 个
2025-07-23 21:54:36,075 - __main__ - INFO - 中转站 4 分配聚集区: 339 个
2025-07-23 21:54:36,075 - __main__ - INFO - 中转站 5 分配聚集区: 507 个
2025-07-23 21:54:36,075 - __main__ - INFO - 中转站 6 分配聚集区: 300 个
2025-07-23 21:54:36,075 - __main__ - INFO - ==================================================
2025-07-23 21:54:36,075 - __main__ - INFO - 配送时间数据统计:
2025-07-23 21:54:36,075 - __main__ - INFO -   使用实际卸货时间: 1519 个聚集区
2025-07-23 21:54:36,075 - __main__ - INFO -   使用默认配送时间: 152 个聚集区
2025-07-23 21:54:36,075 - __main__ - INFO -   实际数据覆盖率: 90.9%
2025-07-23 21:54:36,075 - __main__ - INFO -   平均配送时间: 16.8 分钟
2025-07-23 21:54:36,075 - __main__ - INFO -   最短配送时间: 0.0 分钟
2025-07-23 21:54:36,075 - __main__ - INFO -   最长配送时间: 56.2 分钟
2025-07-23 21:54:36,075 - __main__ - INFO - 
2025-07-23 21:54:36,075 - __main__ - INFO - 按中转站统计配送时间数据完整性:
2025-07-23 21:54:36,075 - __main__ - INFO -   新丰县中转站: 125/125 = 100.0% 覆盖率
2025-07-23 21:54:36,075 - __main__ - INFO -   坪石镇中转站: 222/235 = 94.5% 覆盖率
2025-07-23 21:54:36,075 - __main__ - INFO -   翁源县中转站: 165/165 = 100.0% 覆盖率
2025-07-23 21:54:36,075 - __main__ - INFO -   马市烟叶工作站: 339/339 = 100.0% 覆盖率
2025-07-23 21:54:36,075 - __main__ - INFO -   班组一物流配送中心: 507/507 = 100.0% 覆盖率
2025-07-23 21:54:36,075 - __main__ - INFO -   班组二物流配送中心: 161/300 = 53.7% 覆盖率
2025-07-23 21:54:36,075 - __main__ - WARNING - ⚠️  发现 152 个聚集区缺失卸货时间数据:
2025-07-23 21:54:36,075 - __main__ - WARNING -   坪石镇中转站 缺失 13 个:
2025-07-23 21:54:36,075 - __main__ - WARNING -     1. 聚集区1906560264715153410 (大桥镇1)
2025-07-23 21:54:36,075 - __main__ - WARNING -        坐标: (113.142350, 24.917176)
2025-07-23 21:54:36,075 - __main__ - WARNING -     2. 聚集区1906560264786456577 (大桥镇2)
2025-07-23 21:54:36,075 - __main__ - WARNING -        坐标: (113.774273, 24.916178)
2025-07-23 21:54:36,075 - __main__ - WARNING -     3. 聚集区1906560264853565442 (大桥镇3)
2025-07-23 21:54:36,075 - __main__ - WARNING -        坐标: (113.757754, 24.892904)
2025-07-23 21:54:36,075 - __main__ - WARNING -     4. 聚集区1906560264929062913 (大桥镇4)
2025-07-23 21:54:36,075 - __main__ - WARNING -        坐标: (113.123728, 25.038126)
2025-07-23 21:54:36,075 - __main__ - WARNING -     5. 聚集区1906560264996171778 (大桥镇5)
2025-07-23 21:54:36,076 - __main__ - WARNING -        坐标: (113.170874, 24.989563)
2025-07-23 21:54:36,076 - __main__ - WARNING -        ... 还有 8 个缺失
2025-07-23 21:54:36,076 - __main__ - WARNING - 
2025-07-23 21:54:36,076 - __main__ - WARNING -   班组二物流配送中心 缺失 139 个:
2025-07-23 21:54:36,076 - __main__ - WARNING -     1. 聚集区1906559776674328577 (市辖区1)
2025-07-23 21:54:36,076 - __main__ - WARNING -        坐标: (113.558708, 24.799452)
2025-07-23 21:54:36,076 - __main__ - WARNING -     2. 聚集区1906559776741437442 (市辖区2)
2025-07-23 21:54:36,076 - __main__ - WARNING -        坐标: (113.533929, 24.824828)
2025-07-23 21:54:36,076 - __main__ - WARNING -     3. 聚集区1906559776808546306 (市辖区3)
2025-07-23 21:54:36,076 - __main__ - WARNING -        坐标: (113.504524, 24.759227)
2025-07-23 21:54:36,076 - __main__ - WARNING -     4. 聚集区1906559776871460866 (市辖区4)
2025-07-23 21:54:36,076 - __main__ - WARNING -        坐标: (113.518064, 24.751259)
2025-07-23 21:54:36,076 - __main__ - WARNING -     5. 聚集区1906559776938569730 (市辖区5)
2025-07-23 21:54:36,076 - __main__ - WARNING -        坐标: (113.517758, 24.769346)
2025-07-23 21:54:36,076 - __main__ - WARNING -        ... 还有 134 个缺失
2025-07-23 21:54:36,076 - __main__ - WARNING - 
2025-07-23 21:54:36,076 - __main__ - INFO - ✅ 卸货时间数据覆盖率良好
2025-07-23 21:54:36,076 - __main__ - INFO - ==================================================
2025-07-23 21:54:36,076 - __main__ - INFO - 提取聚集区数据完成，共 1671 条记录
2025-07-23 21:54:36,076 - __main__ - INFO - === 第5步：提取时间矩阵数据（基于所有坐标点） ===
2025-07-23 21:54:36,076 - __main__ - INFO - 开始提取时间矩阵数据...
2025-07-23 21:54:36,078 - __main__ - INFO - 需要查找时间矩阵的坐标点: 1676 个 (分布在 6 个中转站)
2025-07-23 21:54:36,079 - __main__ - INFO - 预期时间矩阵点对数: 561716 个 (仅限中转站内部)
2025-07-23 21:54:36,079 - __main__ - INFO -   中转站1: 126个点 -> 15750个点对
2025-07-23 21:54:36,079 - __main__ - INFO -   中转站2: 236个点 -> 55460个点对
2025-07-23 21:54:36,079 - __main__ - INFO -   中转站3: 166个点 -> 27390个点对
2025-07-23 21:54:36,079 - __main__ - INFO -   中转站4: 340个点 -> 115260个点对
2025-07-23 21:54:36,079 - __main__ - INFO -   中转站5: 508个点 -> 257556个点对
2025-07-23 21:54:36,079 - __main__ - INFO -   中转站6: 301个点 -> 90300个点对
2025-07-23 21:54:36,079 - __main__ - INFO - 坐标点样例: ['113.360766,25.107692', '113.774273,24.916178', '114.348077,25.315945']
2025-07-23 21:54:36,310 - __main__ - INFO - 中转站1查询返回记录数: 15750
2025-07-23 21:54:36,728 - __main__ - INFO - 中转站2查询返回记录数: 49506
2025-07-23 21:54:37,010 - __main__ - INFO - 中转站3查询返回记录数: 27390
2025-07-23 21:54:37,480 - __main__ - INFO - 中转站4查询返回记录数: 62312
2025-07-23 21:54:38,194 - __main__ - INFO - 中转站5查询返回记录数: 91908
2025-07-23 21:54:38,458 - __main__ - INFO - 中转站6查询返回记录数: 27524
2025-07-23 21:54:38,458 - __main__ - INFO - 总查询返回记录数: 274390
2025-07-23 21:54:39,536 - __main__ - INFO - ==================================================
2025-07-23 21:54:39,536 - __main__ - INFO - 时间矩阵提取结果 (仅限中转站内部):
2025-07-23 21:54:39,536 - __main__ - INFO -   算法需要坐标点: 1676 个 (分布在 6 个中转站)
2025-07-23 21:54:39,536 - __main__ - INFO -   数据库中坐标点: 1556 个
2025-07-23 21:54:39,536 - __main__ - INFO -   期望记录数(中转站内部): 561716
2025-07-23 21:54:39,536 - __main__ - INFO -   实际获得记录数: 274390
2025-07-23 21:54:39,536 - __main__ - INFO -   覆盖率: 48.8%
2025-07-23 21:54:40,347 - __main__ - INFO -   中转站1(126点): 15750/15750 = 100.0%
2025-07-23 21:54:41,150 - __main__ - INFO -   中转站2(236点): 49506/55460 = 89.3%
2025-07-23 21:54:41,934 - __main__ - INFO -   中转站3(166点): 27390/27390 = 100.0%
2025-07-23 21:54:42,731 - __main__ - INFO -   中转站4(340点): 62312/115260 = 54.1%
2025-07-23 21:54:43,543 - __main__ - INFO -   中转站5(508点): 90894/257556 = 35.3%
2025-07-23 21:54:44,340 - __main__ - INFO -   中转站6(301点): 27138/90300 = 30.1%
2025-07-23 21:54:44,340 - __main__ - WARNING - ⚠️  120 个坐标点在数据库中无时间数据:
2025-07-23 21:54:44,340 - __main__ - WARNING -     缺失: 113.027563,24.565083 (聚集区1906559986263699457)
2025-07-23 21:54:44,340 - __main__ - WARNING -     缺失: 113.061452,24.675266 (聚集区1906559986196590594)
2025-07-23 21:54:44,340 - __main__ - WARNING -     缺失: 113.089315,24.921496 (聚集区1906559986460831746)
2025-07-23 21:54:44,340 - __main__ - WARNING -     缺失: 113.099027,24.971237 (聚集区1906560265067474945)
2025-07-23 21:54:44,340 - __main__ - WARNING -     缺失: 113.123728,25.038126 (聚集区1906560264929062913)
2025-07-23 21:54:44,340 - __main__ - WARNING -     ... 还有 115 个
2025-07-23 21:54:44,340 - __main__ - INFO - 🔍 分析缺失的时间矩阵点对 (仅限中转站内部)...
2025-07-23 21:54:45,156 - __main__ - WARNING - ❌ 发现 288726 个缺失的中转站内部点对，以下是前8个示例:
2025-07-23 21:54:45,156 - __main__ - WARNING -   1. 中转站2(坪石镇中转站) -> 聚集区1906560264715153410(大桥镇1) (中转站2)
2025-07-23 21:54:45,156 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.142350,24.917176)
2025-07-23 21:54:45,156 - __main__ - WARNING -      距离: 45.38km
2025-07-23 21:54:45,156 - __main__ - WARNING - 
2025-07-23 21:54:45,156 - __main__ - WARNING -   2. 中转站2(坪石镇中转站) -> 聚集区1906560264786456577(大桥镇2) (中转站2)
2025-07-23 21:54:45,156 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.774273,24.916178)
2025-07-23 21:54:45,156 - __main__ - WARNING -      距离: 87.63km
2025-07-23 21:54:45,156 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   3. 中转站2(坪石镇中转站) -> 聚集区1906560264853565442(大桥镇3) (中转站2)
2025-07-23 21:54:45,157 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.757754,24.892904)
2025-07-23 21:54:45,157 - __main__ - WARNING -      距离: 87.54km
2025-07-23 21:54:45,157 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   4. 中转站2(坪石镇中转站) -> 聚集区1906560264929062913(大桥镇4) (中转站2)
2025-07-23 21:54:45,157 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.123728,25.038126)
2025-07-23 21:54:45,157 - __main__ - WARNING -      距离: 31.98km
2025-07-23 21:54:45,157 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   5. 中转站2(坪石镇中转站) -> 聚集区1906560264996171778(大桥镇5) (中转站2)
2025-07-23 21:54:45,157 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.170874,24.989563)
2025-07-23 21:54:45,157 - __main__ - WARNING -      距离: 38.72km
2025-07-23 21:54:45,157 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   6. 中转站2(坪石镇中转站) -> 聚集区1906560265067474945(大桥镇6) (中转站2)
2025-07-23 21:54:45,157 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.099027,24.971237)
2025-07-23 21:54:45,157 - __main__ - WARNING -      距离: 38.50km
2025-07-23 21:54:45,157 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   7. 中转站2(坪石镇中转站) -> 聚集区1906560265138778113(大桥镇7) (中转站2)
2025-07-23 21:54:45,157 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.146059,25.012112)
2025-07-23 21:54:45,157 - __main__ - WARNING -      距离: 35.46km
2025-07-23 21:54:45,157 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   8. 中转站2(坪石镇中转站) -> 聚集区1906560265210081281(大桥镇8) (中转站2)
2025-07-23 21:54:45,157 - __main__ - WARNING -      坐标: (113.020500,25.310110) -> (113.142185,24.977573)
2025-07-23 21:54:45,157 - __main__ - WARNING -      距离: 38.95km
2025-07-23 21:54:45,157 - __main__ - WARNING - 
2025-07-23 21:54:45,157 - __main__ - WARNING -   ... 还有 288718 个缺失点对未显示
2025-07-23 21:54:45,157 - __main__ - INFO - ==================================================
2025-07-23 21:54:45,185 - __main__ - INFO - === 第6步：生成缺失的时间矩阵 ===
2025-07-23 21:54:45,185 - __main__ - INFO - 生成缺失的时间矩阵数据 (仅限中转站内部)...
2025-07-23 21:54:46,085 - __main__ - INFO - 生成缺失时间矩阵数据完成，新增 288726 条记录 (仅限中转站内部)
2025-07-23 21:54:46,085 - __main__ - INFO - === 第7步：验证数据完整性 ===
2025-07-23 21:54:46,086 - __main__ - INFO - 开始验证数据完整性...
2025-07-23 21:54:46,086 - __main__ - INFO - 数据完整性验证通过
2025-07-23 21:54:46,086 - __main__ - INFO - === 第8步：保存JSON文件 ===
2025-07-23 21:54:46,086 - __main__ - INFO - 开始保存JSON文件...
2025-07-23 21:54:46,102 - __main__ - INFO - 保存文件成功: ../data\v1.0\accumulations.json
2025-07-23 21:54:46,102 - __main__ - INFO - 保存文件成功: ../data\v1.0\transit_depots.json
2025-07-23 21:54:46,103 - __main__ - INFO - 保存文件成功: ../data\v1.0\teams.json
2025-07-23 21:54:50,957 - __main__ - INFO - 保存文件成功: ../data\v1.0\time_matrix.json
2025-07-23 21:54:50,957 - __main__ - INFO - 数据提取完成，文件保存到: ../data\v1.0
2025-07-23 21:54:50,957 - __main__ - INFO - 数据提取流程完成
2025-07-23 21:54:50,957 - __main__ - INFO - 数据库连接已关闭
