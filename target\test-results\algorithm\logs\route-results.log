2025-08-20 00:14:44.830 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 7, 调试会话: debug_20250820_001444
2025-08-20 00:14:44.836 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-20 00:14:50.672 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 7 个中转站分组
2025-08-20 00:14:50.672 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250820_001444
2025-08-20 00:14:50.673 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-20 00:14:50.673 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县对接点 分配 118 个聚集区到 10 条路线
2025-08-20 00:14:50.732 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.732 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇对接点 分配 45 个聚集区到 9 条路线
2025-08-20 00:14:50.741 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.741 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县对接点 分配 168 个聚集区到 10 条路线
2025-08-20 00:14:50.768 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.768 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-20 00:14:50.813 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.814 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-20 00:14:50.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-20 00:14:50.962 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.962 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 乐昌对接点 分配 192 个聚集区到 10 条路线
2025-08-20 00:14:50.985 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 00:14:50.985 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-20 00:14:50.985 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 7, 会话ID: debug_20250820_001444
2025-08-20 00:14:51.019 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-20 00:14:51.019 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-20 00:14:53.653 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-20 00:14:56.196 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-20 00:14:58.664 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-20 00:15:01.507 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-20 00:15:04.522 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-20 00:15:07.045 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-20 00:15:09.615 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-20 00:15:12.564 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-20 00:15:14.918 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-20 00:15:17.577 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-20 00:15:20.473 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-20 00:15:22.924 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-20 00:15:24.232 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-20 00:15:24.235 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-20 00:15:24.267 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-20 00:15:24.267 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 27, 超30分钟差距中转站: 7
2025-08-20 00:15:24.267 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-20 00:15:24.267 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-20 00:15:24.269 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-20 00:15:24.285 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-20 00:15:24.287 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-20 00:16:40.755 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 150, 中转站调整: 1
2025-08-20 00:16:40.755 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-20 00:16:40.766 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 115926ms, 生成路线: 125, 总工作时间: 53067.4分钟
2025-08-20 01:20:47.034 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 7, 调试会话: debug_20250820_012047
2025-08-20 01:20:47.035 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-20 01:20:56.522 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 7 个中转站分组
2025-08-20 01:20:56.522 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250820_012047
2025-08-20 01:20:56.522 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-20 01:20:56.522 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县对接点 分配 118 个聚集区到 10 条路线
2025-08-20 01:20:56.590 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.590 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇对接点 分配 45 个聚集区到 9 条路线
2025-08-20 01:20:56.603 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.604 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县对接点 分配 168 个聚集区到 10 条路线
2025-08-20 01:20:56.638 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.638 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-20 01:20:56.700 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.700 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-20 01:20:56.783 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.783 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-20 01:20:56.830 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.831 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 乐昌对接点 分配 192 个聚集区到 10 条路线
2025-08-20 01:20:56.864 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:20:56.864 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-20 01:20:56.864 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 7, 会话ID: debug_20250820_012047
2025-08-20 01:20:56.900 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-20 01:20:56.901 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-20 01:21:00.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-20 01:21:03.793 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-20 01:21:06.513 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-20 01:21:09.303 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-20 01:21:12.901 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-20 01:21:15.404 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-20 01:21:17.944 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-20 01:21:20.975 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-20 01:21:23.806 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-20 01:21:26.765 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-20 01:21:29.339 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-20 01:21:31.630 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-20 01:21:32.743 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-20 01:21:32.746 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-20 01:21:32.780 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-20 01:21:32.780 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 27, 超30分钟差距中转站: 7
2025-08-20 01:21:32.780 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-20 01:21:32.780 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-20 01:21:32.783 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-20 01:21:32.788 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-20 01:21:32.792 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-20 01:22:44.853 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 150, 中转站调整: 1
2025-08-20 01:22:44.853 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-20 01:22:44.855 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 117820ms, 生成路线: 125, 总工作时间: 53067.4分钟
2025-08-20 01:38:06.237 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 7, 调试会话: debug_20250820_013806
2025-08-20 01:38:06.237 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-20 01:38:12.567 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 7 个中转站分组
2025-08-20 01:38:12.567 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250820_013806
2025-08-20 01:38:12.567 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-20 01:38:12.567 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县对接点 分配 118 个聚集区到 10 条路线
2025-08-20 01:38:12.618 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.618 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇对接点 分配 45 个聚集区到 9 条路线
2025-08-20 01:38:12.626 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.626 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县对接点 分配 168 个聚集区到 10 条路线
2025-08-20 01:38:12.648 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.648 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-20 01:38:12.693 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.693 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-20 01:38:12.749 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.749 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-20 01:38:12.780 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.780 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 乐昌对接点 分配 192 个聚集区到 10 条路线
2025-08-20 01:38:12.809 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-20 01:38:12.809 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-20 01:38:12.809 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 7, 会话ID: debug_20250820_013806
2025-08-20 01:38:12.841 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-20 01:38:12.842 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-20 01:38:15.198 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-20 01:38:17.543 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-20 01:38:20.070 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-20 01:38:22.456 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-20 01:38:24.728 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-20 01:38:27.040 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-20 01:38:29.298 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-20 01:38:31.522 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-20 01:38:33.746 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-20 01:38:35.952 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-20 01:38:38.145 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-20 01:38:40.374 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-20 01:38:41.483 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-20 01:38:41.487 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-20 01:38:41.521 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-20 01:38:41.521 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 27, 超30分钟差距中转站: 7
2025-08-20 01:38:41.521 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-20 01:38:41.521 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-20 01:38:41.523 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-20 01:38:41.541 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-20 01:38:41.545 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-20 01:39:56.309 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 150, 中转站调整: 1
2025-08-20 01:39:56.310 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-20 01:39:56.313 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 110075ms, 生成路线: 125, 总工作时间: 53067.4分钟
