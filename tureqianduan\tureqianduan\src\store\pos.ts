import { defineStore } from "pinia";
import type {
  IAddLocation,
  IGetLocationList,
  IUpdateLocation,
  ILogLocationImport,
  IFromDownload,
  IEditLocation,
  ILogDownload,
  IDeleteLog,
} from "@/types/pos";
import {
  addLocation,
  getLocationList,
  updateLocation,
  importLocation,
  exportLocation,
  importLocationLog,
  downloadTemplate,
  editLocation,
  downloadLog,
  accurate,
  deleteLog,
  getUnassignedList,
} from "@/service/modules/pos";

export const useLocationStore = defineStore("location", () => {
  //待分配列表
  async function getUnassignedListData() {
    const res = await getUnassignedList();
    return res;
  }

  //添加位置
  async function addLocationData(params: IAddLocation) {
    const res = await addLocation(params);
    return res;
  }

  //获取位置列表
  async function getLocationListData(params: IGetLocationList) {
    const res = await getLocationList(params);
    return res.data;
  }

  //修改位置信息
  async function updateLocationData(params: IUpdateLocation) {
    const res = await updateLocation(params);
    return res;
  }

  //导入日志
  async function importLog(params: ILogLocationImport) {
    const res = await importLocationLog(params);
    return res.data;
  }

  //导出表格
  async function exportLocationData() {
    const res = await exportLocation();
    return res;
  }

  //下载表格
  async function downloadNullForm(params: IFromDownload) {
    const res = await downloadTemplate(params);
    return res;
  }

  //下载日志
  async function downloadLocationLog(params: ILogDownload) {
    const res: any = await downloadLog(params);
    return res;
  }

  //删除日志
  async function deleteLocationLog(params: IDeleteLog) {
    const res: any = await deleteLog(params);
    return res;
  }

  //upload
  async function uploadFile(formData: FormData, config: any) {
    const res = await importLocation(formData, config);
    return res;
  }

  //编辑
  async function editLocationData(params: IEditLocation) {
    const res = await editLocation(params);
    return res;
  }

  //重新计算
  async function calculate() {
    const res = await accurate();
    return res;
  }

  return {
    addLocationData,
    getUnassignedListData,
    getLocationListData,
    updateLocationData,
    importLog,
    downloadNullForm,
    uploadFile,
    editLocationData,
    downloadLocationLog,
    deleteLocationLog,
    exportLocationData,
    calculate,
  };
});
