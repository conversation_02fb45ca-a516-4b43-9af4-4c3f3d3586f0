package com.ict.ycwl.pathcalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pathcalculate.pojo.TravelTime;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 行驶时间数据访问接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface TravelTimeMapper extends BaseMapper<TravelTime> {

    /**
     * 获取总记录数
     */
    Long getTotalCount();

    /**
     * 根据偏移量获取样本数据
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 样本数据列表
     */
    List<TravelTime> getSampleByOffset(int offset, int limit);
}
