package com.ict.ycwl.clustercalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistanceEntry implements Comparable<DistanceEntry>{
    Accumulation accumulation;
    double distance;

    @Override
    public int compareTo(DistanceEntry other) {
        return Double.compare(this.distance, other.distance);
    }
}
