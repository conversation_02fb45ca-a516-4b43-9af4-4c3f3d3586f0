package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.dto.ColourConvexResponse;
import com.ict.ycwl.pathcalculate.form.GetColourConvexHullFrom;
import com.ict.ycwl.pathcalculate.pojo.LngAndLat;
import com.ict.ycwl.pathcalculate.pojo.ResultRoute;
import com.ict.ycwl.pathcalculate.vo.ConvexPointVO;
import com.ict.ycwl.pathcalculate.vo.TransitDepotVO;
import org.locationtech.jts.geom.Geometry;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MapDisplayService {

    /**
     * 获取班组地图分割线
     *
     * @return 分割线集合
     */
//    List<List<LngAndLat>> getSplitLines();

    /**
     * 获取所有班组地图分割线
     *
     * @param groupOrder 班组顺序
     * @return 分割线集合
     */
    List<List<LngAndLat>> getSplitLines(String groupOrder);

    /**
     * 获取该班组的着色分块
     *
     * @param getColourConvexHullFrom 获取单个班组着色分块请求数据
     * @return 该班组的着色分块
     */
    List<List<LngAndLat>> getColourConvexHull(GetColourConvexHullFrom getColourConvexHullFrom);

    /**
     * 获取所有凸包的着色分块
     *
     * @return 所有凸包的着色分块
     */
    List<ArrayList<String>> getAllColourConvex() throws Exception;

    /**
     * 获取所有凸包的着色分块（包含颜色信息）
     *
     * @return 包含班组颜色信息的色块数据列表
     */
    List<ColourConvexResponse> getAllColourConvexWithColor() throws Exception;

    /**
     * 获取所有凸包所包含的打卡点
     *
     * @return 所有打卡点的集合
     */
    List<ConvexPointVO> getConvexPoint();

    /**
     * 获取已保存的路径数据
     *
     * @return 已保存的路径数据
     */
    List<ResultRoute> getConvex(String apiKey) throws Exception;

    /**
     * 获取启用的所有中转站名字
     *
     * @return 启用的所有中转站名字
     */
    List<TransitDepotVO> getTransitDepotName();
}
