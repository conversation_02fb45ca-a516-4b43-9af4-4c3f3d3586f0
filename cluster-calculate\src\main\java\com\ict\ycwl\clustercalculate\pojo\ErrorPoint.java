package com.ict.ycwl.clustercalculate.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("error_point")
public class ErrorPoint {
    @TableId
    private Long errorPointId;

    private boolean isDelete;

    private double currentStoreLongitude;

    private double currentStoreLatitude;

    private double pairingStoreLongitude;

    private double pairingStoreLatitude;

    private Timestamp createTime;

    private Timestamp updateTime;



//    private boolean isExist;
}
