<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.RouteDetailMapper">

    <resultMap id="BaseResultMap" type="com.ict.ycwl.pathcalculate.pojo.RouteDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="accumulationCount" column="accumulation_count" jdbcType="INTEGER"/>
            <result property="cityCount" column="city_count" jdbcType="INTEGER"/>
            <result property="countryCount" column="country_count" jdbcType="INTEGER"/>
            <result property="loadingTime" column="loading_time" jdbcType="DOUBLE"/>
            <result property="transitTime" column="transit_time" jdbcType="DOUBLE"/>
            <result property="deliveryTime" column="delivery_time" jdbcType="DOUBLE"/>
            <result property="totalTime" column="total_time" jdbcType="DOUBLE"/>
            <result property="routeId" column="route_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,accumulation_count,city_count,
        country_count,loading_time,transit_time,
        delivery_time,total_time,route_id
    </sql>

    <select id="selectByRouteId" resultMap="BaseResultMap">
        select * from route_detail where route_id=#{routeId}
    </select>
</mapper>
