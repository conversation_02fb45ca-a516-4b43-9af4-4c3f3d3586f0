<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.GroupMapper">

    <resultMap id="BaseResultMap" type="com.ict.datamanagement.domain.entity.Group">
            <id property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="colour" column="colour" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        group_id,group_name,colour
    </sql>
    <insert id="insert">
        INSERT INTO `group` ( `group_id`, `group_name`, `colour` ) VALUES ( #{id}, #{teamName}, #{colour} )
    </insert>
    <insert id="myInsert">
        INSERT INTO `group` ( `group_id`, `group_name`, `colour` ) VALUES ( #{groupId}, #{groupName}, #{colour} )
    </insert>
    <delete id="deleteById">
        delete from `group` where group_id=#{teamId}
    </delete>
    <select id="selectGroupList"  parameterType="Long" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"></include>  FROM `group`
    </select>
    <select id="selectById" resultType="com.ict.datamanagement.domain.entity.Group">
        SELECT `group_id`, `group_name`, `colour` FROM `group` WHERE `group_id` = #{Id}
    </select>
</mapper>
