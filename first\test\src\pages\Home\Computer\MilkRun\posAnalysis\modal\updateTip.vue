<template>
  <el-dialog
    v-model="confirmDialogVis"
    title="提示"
    class="transform"
    width="40%"
    align-center
    :close-on-click-modal="false"
  >
    <div class="content">选择之后会将所有关联的商户由已分配设置为未分配</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog" type="primary">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  //弹窗的可视化
  const confirmDialogVis = ref<boolean>(false);
  defineExpose({
    confirmDialogVis,
  });

  function confirmDialog() {
    confirmDialogVis.value = false;
  }

  //取消变更
  function cancelDialog() {
    confirmDialogVis.value = false;
  }
</script>

<style scoped></style>
