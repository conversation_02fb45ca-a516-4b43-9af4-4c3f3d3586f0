@echo off
echo ========================================
echo 粤北卷烟物流管理系统 - 新算法测试脚本
echo ========================================
echo.

set BASE_URL=http://localhost:8080/api/new-algorithm

echo 1. 检查应用是否启动...
curl -s %BASE_URL%/health > nul
if %errorlevel% neq 0 (
    echo 错误: 应用未启动或无法访问 %BASE_URL%
    echo 请确保应用已启动并监听在8080端口
    pause
    exit /b 1
)
echo ✓ 应用已启动

echo.
echo 2. 执行健康检查...
curl -s -X GET %BASE_URL%/health
echo.

echo.
echo 3. 测试数据加载功能...
curl -s -X GET %BASE_URL%/test-data-loading
echo.

echo.
echo 4. 获取算法配置...
curl -s -X GET %BASE_URL%/config
echo.

echo.
echo 5. 执行完整算法流程（这可能需要几分钟）...
echo 请耐心等待...
curl -s -X POST "%BASE_URL%/test-full-execution?apiKey=test-api-key"
echo.

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如果看到上述所有接口都返回了JSON响应，说明新算法对接成功。
echo.
echo 接下来你可以：
echo 1. 查看详细的测试结果
echo 2. 在前端调用 /calculateall 接口测试完整功能
echo 3. 根据需要调整算法配置参数
echo.
pause
