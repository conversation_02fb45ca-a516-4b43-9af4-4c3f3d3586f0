package com.ict.ycwl.clustercalculate.pojo.vo;

import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class PerviewPointVo {
    private String accName;
    private double accLongitude;
    private double accLatitude;

    private List<PerviewPoint> perviewPoints;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PerviewPointVo that = (PerviewPointVo) o;
        return Objects.equals(accName, that.accName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accName);
    }
}

