<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单路线可视化</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            background: white;
            border-radius: 12px;
            margin: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        #map {
            flex: 1;
            height: 100%;
            position: relative;
        }
        
        .sidebar {
            width: 380px;
            background: white;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #e0e6ed;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .upload-section {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #667eea;
            background: #f7faff;
            transform: translateY(-2px);
        }

        .upload-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .controls-section {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .control-group {
            margin-bottom: 25px;
        }

        .control-label {
            display: block;
            margin-bottom: 15px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 8px;
            font-weight: 500;
        }

        .filter-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
        }

        .filter-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #374151;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .filter-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            font-size: 13px;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .clear-btn {
            padding: 12px 16px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .toggle-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .toggle-btn {
            padding: 10px 16px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .routes-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
        }

        .route-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .route-item:hover {
            background: #f8fafc;
            transform: translateX(3px);
        }

        .route-item.selected {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-left: 4px solid #3b82f6;
        }

        .route-item.highlighted {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
        }

        .route-color {
            width: 16px;
            height: 4px;
            border-radius: 2px;
            flex-shrink: 0;
        }

        .route-info {
            flex: 1;
        }

        .route-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .route-meta {
            font-size: 12px;
            color: #64748b;
        }

        .action-btn {
            padding: 6px 12px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .legend {
            background: #f8fafc;
            padding: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .legend-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #374151;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .legend-color {
            width: 20px;
            height: 4px;
            margin-right: 12px;
            border-radius: 2px;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 13px;
        }

        .message.success {
            background: #f0fdf4;
            color: #166534;
            border-left: 4px solid #22c55e;
        }

        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #ef4444;
        }

        .filter-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 1000;
            display: none;
            backdrop-filter: blur(10px);
        }

        .filter-indicator.active {
            display: block;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
                margin: 4px;
            }
            
            .sidebar {
                width: 100%;
                height: 50%;
            }
            
            #map {
                height: 50%;
            }
        }
    </style>
</head>
<body>
    <div class="loading" id="loading" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>正在解析路线数据...</div>
        </div>
    </div>
    
    <div class="app-container">
        <div id="map">
            <div class="filter-indicator" id="filterIndicator">
                <i class="fas fa-filter"></i>
                <span id="filterText"></span>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="header">
                <h1><i class="fas fa-route"></i> 简单路线可视化</h1>
                <div class="subtitle">Simple Route Visualization</div>
            </div>
            
            <div class="upload-section">
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 24px; color: #667eea;"></i>
                    </div>
                    <div class="upload-text">点击选择或拖拽 TXT 文件</div>
                    <button type="button" class="upload-button">
                        <i class="fas fa-file-upload"></i> 选择文件
                    </button>
                    <input type="file" id="fileInput" style="display: none;" accept=".txt" />
                    <div class="file-info" id="fileInfo" style="margin-top: 10px; font-size: 12px; color: #64748b;"></div>
                </div>
                <div id="uploadMessage"></div>
            </div>

            <div class="controls-section" id="controlsSection" style="display: none;">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalRoutes">0</div>
                        <div class="stat-label">总路线数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="totalPoints">0</div>
                        <div class="stat-label">总坐标点数</div>
                    </div>
                </div>

                <div class="filter-section">
                    <div class="filter-title">
                        <i class="fas fa-search"></i> 路线筛选
                    </div>
                    <div class="filter-controls">
                        <div class="filter-row">
                            <input type="text" id="routeSearch" class="search-input" placeholder="搜索路线编号..." />
                            <button class="clear-btn" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="filter-row">
                            <input type="number" id="minPoints" class="search-input" placeholder="最小点数" />
                            <input type="number" id="maxPoints" class="search-input" placeholder="最大点数" />
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <label class="control-label">显示选项</label>
                    <div class="toggle-group">
                        <button class="toggle-btn active" id="showRoutes">
                            <i class="fas fa-route"></i> 路线
                        </button>
                        <button class="toggle-btn active" id="showMarkers">
                            <i class="fas fa-map-marker-alt"></i> 起点终点
                        </button>
                        <button class="toggle-btn" id="showNumbers">
                            <i class="fas fa-list-ol"></i> 编号
                        </button>
                    </div>
                </div>

                <div class="control-group">
                    <label class="control-label">路线列表</label>
                    <div class="routes-list" id="routesList">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>

            <div class="legend">
                <div class="legend-title">图例说明</div>
                <div id="legendContent">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #3b82f6;"></div>
                        <span>路线轨迹</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #22c55e; border-radius: 50%; width: 12px; height: 12px;"></div>
                        <span>起点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ef4444; border-radius: 50%; width: 12px; height: 12px;"></div>
                        <span>终点</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let routeData = [];
        let filteredRoutes = [];
        let routeLayer = L.layerGroup();
        let markerLayer = L.layerGroup();
        let selectedRoute = null;
        
        // 颜色方案
        const colors = [
            '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6', 
            '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1',
            '#10b981', '#f43f5e', '#14b8a6', '#f59e0b', '#8b5cf6'
        ];

        // 初始化地图
        function initMap() {
            map = L.map('map', {
                zoomControl: false
            }).setView([24.5, 113.5], 10);
            
            L.control.zoom({
                position: 'bottomright'
            }).addTo(map);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            routeLayer.addTo(map);
            markerLayer.addTo(map);
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('uploadMessage');
            const className = type === 'error' ? 'error' : 'success';
            messageDiv.innerHTML = `<div class="message ${className}">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'}"></i>
                ${message}
            </div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }

        // 解析LINESTRING数据
        function parseLineString(lineString) {
            // 移除 "LINESTRING (" 和 ")"
            const coordsText = lineString.replace(/^LINESTRING\s*\(/, '').replace(/\)$/, '');
            const coordPairs = coordsText.split(',');
            
            return coordPairs.map(pair => {
                const [lng, lat] = pair.trim().split(' ').map(Number);
                return [lat, lng]; // Leaflet使用 [lat, lng] 格式
            });
        }

        // 处理文件上传
        function handleFile(file) {
            if (!file) return;
            
            if (!file.name.toLowerCase().endsWith('.txt')) {
                showMessage('请选择TXT文件', 'error');
                return;
            }

            document.getElementById('loading').style.display = 'flex';
            document.getElementById('fileInfo').innerHTML = `
                <i class="fas fa-file-alt"></i>
                ${file.name} (${(file.size / 1024).toFixed(1)} KB)
            `;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const lines = e.target.result.split('\n').filter(line => line.trim());
                    routeData = [];
                    
                    lines.forEach((line, index) => {
                        const trimmedLine = line.trim();
                        if (trimmedLine.startsWith('LINESTRING')) {
                            try {
                                const coordinates = parseLineString(trimmedLine);
                                if (coordinates.length >= 2) {
                                    routeData.push({
                                        id: index + 1,
                                        coordinates: coordinates,
                                        pointCount: coordinates.length,
                                        color: colors[index % colors.length]
                                    });
                                }
                            } catch (err) {
                                console.warn(`解析第${index + 1}行失败:`, err);
                            }
                        }
                    });
                    
                    if (routeData.length === 0) {
                        throw new Error('未找到有效的LINESTRING数据');
                    }

                    filteredRoutes = [...routeData];
                    processData();
                    showMessage(`成功加载 ${routeData.length} 条路线！`, 'success');
                    document.getElementById('controlsSection').style.display = 'block';
                } catch (error) {
                    showMessage(`文件解析失败: ${error.message}`, 'error');
                    console.error('解析错误:', error);
                } finally {
                    document.getElementById('loading').style.display = 'none';
                }
            };

            reader.onerror = function() {
                showMessage('文件读取失败', 'error');
                document.getElementById('loading').style.display = 'none';
            };

            reader.readAsText(file);
        }

        // 渲染路线
        function renderRoutes() {
            routeLayer.clearLayers();
            markerLayer.clearLayers();
            
            const showRoutes = document.getElementById('showRoutes').classList.contains('active');
            const showMarkers = document.getElementById('showMarkers').classList.contains('active');
            const showNumbers = document.getElementById('showNumbers').classList.contains('active');
            
            filteredRoutes.forEach(route => {
                // 渲染路线
                if (showRoutes) {
                    const polyline = L.polyline(route.coordinates, {
                        color: route.color,
                        weight: 4,
                        opacity: 0.8,
                        routeId: route.id
                    }).addTo(routeLayer);

                    polyline.bindPopup(`
                        <div style="font-family: sans-serif;">
                            <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                <i class="fas fa-route"></i> 路线 ${route.id}
                            </h4>
                            <p><strong>坐标点数:</strong> ${route.pointCount}</p>
                            <p><strong>起点:</strong> ${route.coordinates[0][0].toFixed(6)}, ${route.coordinates[0][1].toFixed(6)}</p>
                            <p><strong>终点:</strong> ${route.coordinates[route.coordinates.length-1][0].toFixed(6)}, ${route.coordinates[route.coordinates.length-1][1].toFixed(6)}</p>
                        </div>
                    `);

                    polyline.on('click', () => selectRoute(route));
                    
                    polyline.on('mouseover', function() {
                        if (selectedRoute?.id !== route.id) {
                            this.setStyle({weight: 6, opacity: 1});
                        }
                    });

                    polyline.on('mouseout', function() {
                        if (selectedRoute?.id !== route.id) {
                            this.setStyle({weight: 4, opacity: 0.8});
                        }
                    });
                }

                // 渲染起点终点标记
                if (showMarkers) {
                    const startPoint = route.coordinates[0];
                    const endPoint = route.coordinates[route.coordinates.length - 1];
                    
                    // 起点
                    const startMarker = L.circleMarker(startPoint, {
                        radius: 8,
                        fillColor: '#22c55e',
                        color: 'white',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.9
                    }).addTo(markerLayer);
                    
                    startMarker.bindPopup(`路线 ${route.id} - 起点`);
                    
                    // 终点
                    const endMarker = L.circleMarker(endPoint, {
                        radius: 8,
                        fillColor: '#ef4444',
                        color: 'white',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.9
                    }).addTo(markerLayer);
                    
                    endMarker.bindPopup(`路线 ${route.id} - 终点`);
                }

                // 显示路线编号
                if (showNumbers) {
                    const midIndex = Math.floor(route.coordinates.length / 2);
                    const midPoint = route.coordinates[midIndex];
                    
                    const numberMarker = L.marker(midPoint, {
                        icon: L.divIcon({
                            className: 'route-number',
                            html: `<div style="background: ${route.color}; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">${route.id}</div>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 12]
                        })
                    }).addTo(markerLayer);
                    
                    numberMarker.bindPopup(`路线 ${route.id}`);
                }
            });
            
            // 调整地图视野
            if (filteredRoutes.length > 0) {
                const allPoints = [];
                filteredRoutes.forEach(route => {
                    allPoints.push(...route.coordinates);
                });
                if (allPoints.length > 0) {
                    const bounds = L.latLngBounds(allPoints);
                    map.fitBounds(bounds, { padding: [20, 20] });
                }
            }
        }

        // 选择路线
        function selectRoute(route) {
            selectedRoute = route;
            
            // 清除之前的选中状态
            document.querySelectorAll('.route-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加新的选中状态
            const routeItem = document.querySelector(`[data-route-id="${route.id}"]`);
            if (routeItem) {
                routeItem.classList.add('selected');
                routeItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            
            // 高亮地图上的路线
            routeLayer.eachLayer(layer => {
                if (layer.options.routeId === route.id) {
                    layer.setStyle({
                        weight: 8,
                        opacity: 1,
                        zIndexOffset: 1000
                    });
                    layer.bringToFront();
                } else {
                    layer.setStyle({
                        weight: 3,
                        opacity: 0.3,
                        zIndexOffset: 0
                    });
                }
            });
            
            // 显示筛选指示器
            showFilterIndicator(`已选中路线 ${route.id}`);
        }

        // 生成路线列表
        function generateRoutesList() {
            const routesList = document.getElementById('routesList');
            
            routesList.innerHTML = filteredRoutes.map(route => `
                <div class="route-item" data-route-id="${route.id}" onclick="selectRoute(${JSON.stringify(route).replace(/"/g, '&quot;')})">
                    <div class="route-color" style="background: ${route.color};"></div>
                    <div class="route-info">
                        <div class="route-name">路线 ${route.id}</div>
                        <div class="route-meta">${route.pointCount} 个坐标点</div>
                    </div>
                    <button class="action-btn" onclick="event.stopPropagation(); focusOnRoute(${route.id})">
                        <i class="fas fa-search-plus"></i>
                    </button>
                </div>
            `).join('');
        }

        // 聚焦到指定路线
        function focusOnRoute(routeId) {
            const route = filteredRoutes.find(r => r.id === routeId);
            if (route) {
                const bounds = L.latLngBounds(route.coordinates);
                map.fitBounds(bounds, { padding: [50, 50] });
                selectRoute(route);
            }
        }

        // 更新统计信息
        function updateStats() {
            const totalRoutes = filteredRoutes.length;
            const totalPoints = filteredRoutes.reduce((sum, route) => sum + route.pointCount, 0);
            
            document.getElementById('totalRoutes').textContent = totalRoutes;
            document.getElementById('totalPoints').textContent = totalPoints;
        }

        // 应用筛选
        function applyFilters() {
            const searchText = document.getElementById('routeSearch').value.toLowerCase();
            const minPoints = parseInt(document.getElementById('minPoints').value) || 0;
            const maxPoints = parseInt(document.getElementById('maxPoints').value) || Infinity;
            
            filteredRoutes = routeData.filter(route => {
                const matchesSearch = searchText === '' || route.id.toString().includes(searchText);
                const matchesPointRange = route.pointCount >= minPoints && route.pointCount <= maxPoints;
                
                return matchesSearch && matchesPointRange;
            });
            
            renderRoutes();
            generateRoutesList();
            updateStats();
            
            if (filteredRoutes.length !== routeData.length) {
                showFilterIndicator(`显示 ${filteredRoutes.length} / ${routeData.length} 条路线`);
            } else {
                hideFilterIndicator();
            }
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('routeSearch').value = '';
            document.getElementById('minPoints').value = '';
            document.getElementById('maxPoints').value = '';
            
            filteredRoutes = [...routeData];
            renderRoutes();
            generateRoutesList();
            updateStats();
            hideFilterIndicator();
            
            // 清除选中状态
            selectedRoute = null;
            document.querySelectorAll('.route-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 显示筛选指示器
        function showFilterIndicator(text) {
            const indicator = document.getElementById('filterIndicator');
            document.getElementById('filterText').textContent = text;
            indicator.classList.add('active');
        }

        // 隐藏筛选指示器
        function hideFilterIndicator() {
            document.getElementById('filterIndicator').classList.remove('active');
        }

        // 处理数据
        function processData() {
            renderRoutes();
            generateRoutesList();
            updateStats();
        }

        // 切换显示选项
        function toggleDisplay(buttonId) {
            const button = document.getElementById(buttonId);
            button.classList.toggle('active');
            renderRoutes();
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 初始化文件上传
        function initFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');

            fileInput.addEventListener('change', function(e) {
                handleFile(e.target.files[0]);
            });

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFile(e.dataTransfer.files[0]);
            });
        }

        // 初始化
        function init() {
            initMap();
            initFileUpload();

            // 绑定显示选项事件
            document.getElementById('showRoutes').addEventListener('click', () => toggleDisplay('showRoutes'));
            document.getElementById('showMarkers').addEventListener('click', () => toggleDisplay('showMarkers'));
            document.getElementById('showNumbers').addEventListener('click', () => toggleDisplay('showNumbers'));

            // 绑定筛选器事件
            const debouncedFilter = debounce(applyFilters, 300);
            document.getElementById('routeSearch').addEventListener('input', debouncedFilter);
            document.getElementById('minPoints').addEventListener('input', debouncedFilter);
            document.getElementById('maxPoints').addEventListener('input', debouncedFilter);

            // 点击筛选指示器清除筛选
            document.getElementById('filterIndicator').addEventListener('click', clearSearch);
        }

        // 启动应用
        init();
    </script>
</body>
</html>