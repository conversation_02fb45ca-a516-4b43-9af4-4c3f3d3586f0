#!/usr/bin/env python3
"""
数据库数据提取脚本
从MySQL数据库中提取路径规划算法所需的测试数据，并生成JSON文件

使用方法:
python extract_data.py --scale small --version v1.0
python extract_data.py --scale medium --version v1.1
python extract_data.py --scale full --version latest
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict

import pymysql
from pymysql.cursors import DictCursor

from config import DATABASE_CONFIG, EXTRACTION_CONFIG, SQL_QUERIES, LOGGING_CONFIG


class DataExtractor:
    """数据提取器主类"""
    
    def __init__(self, scale: str = 'small', version: str = None):
        """
        初始化数据提取器
        
        Args:
            scale: 数据规模 ('small', 'medium', 'large', 'full')
            version: 数据版本
        """
        self.scale = scale
        self.version = version or EXTRACTION_CONFIG['default_version']
        self.scale_config = EXTRACTION_CONFIG['data_scales'][scale]
        self.quality_filters = EXTRACTION_CONFIG['quality_filters']
        
        # 设置日志
        self._setup_logging()
        
        # 数据库连接
        self.connection = None
        
        # 数据存储
        self.accumulations = []
        self.transit_depots = []
        self.teams = []
        self.time_matrix = {}
        self.depot_team_relations = {}
        
    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG['level']),
            format=LOGGING_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=DATABASE_CONFIG['host'],
                port=DATABASE_CONFIG['port'],
                user=DATABASE_CONFIG['user'],
                password=DATABASE_CONFIG['password'],
                database=DATABASE_CONFIG['database'],
                charset=DATABASE_CONFIG['charset'],
                cursorclass=DictCursor
            )
            self.logger.info("数据库连接成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
            
    def close_database(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.logger.info("数据库连接已关闭")
            
    def extract_accumulations(self) -> List[Dict]:
        """提取聚集区数据 - 基于已提取的中转站关系，包含实际卸货时间"""
        self.logger.info("开始提取聚集区数据...")
        
        if not self.transit_depots:
            self.logger.error("必须先提取中转站数据才能提取聚集区数据")
            return []
            
        # 获取有效的中转站ID列表
        valid_depot_ids = [depot['transitDepotId'] for depot in self.transit_depots]
        depot_ids_str = ','.join(map(str, valid_depot_ids))
        
        # 为每个中转站分配聚集区，确保数量平衡
        accumulations_per_depot = {}
        if self.scale_config['max_accumulations']:
            # 计算每个中转站应分配的聚集区数量
            total_depots = len(valid_depot_ids)
            base_count = self.scale_config['max_accumulations'] // total_depots
            remainder = self.scale_config['max_accumulations'] % total_depots
            
            for i, depot_id in enumerate(valid_depot_ids):
                count = base_count + (1 if i < remainder else 0)
                accumulations_per_depot[depot_id] = count
        else:
            # 全量提取，不限制数量
            for depot_id in valid_depot_ids:
                accumulations_per_depot[depot_id] = 99999
        
        # 构建基于关系的查询SQL - 连接unloading_time表获取实际卸货时间
        accumulation_query = f"""
            SELECT 
                a.accumulation_id,
                a.accumulation_name,
                a.longitude,
                a.latitude,
                a.transit_depot_id,
                COALESCE(ut.unloading_time, %(default_delivery_time)s) as delivery_time
            FROM accumulation a
            LEFT JOIN unloading_time ut ON a.accumulation_id = ut.acc_id
                AND ABS(a.longitude - CAST(ut.acclongitude AS DOUBLE)) < 0.000001
                AND ABS(a.latitude - CAST(ut.acclatitude AS DOUBLE)) < 0.000001
            WHERE a.is_delete = 0 
                AND a.longitude IS NOT NULL 
                AND a.latitude IS NOT NULL
                AND a.longitude BETWEEN %(lng_min)s AND %(lng_max)s
                AND a.latitude BETWEEN %(lat_min)s AND %(lat_max)s
                AND a.transit_depot_id IN ({depot_ids_str})
            ORDER BY a.transit_depot_id, a.accumulation_id
        """
        
        params = {
            'lng_min': self.quality_filters['coordinate_bounds']['longitude_min'],
            'lng_max': self.quality_filters['coordinate_bounds']['longitude_max'],
            'lat_min': self.quality_filters['coordinate_bounds']['latitude_min'],
            'lat_max': self.quality_filters['coordinate_bounds']['latitude_max'],
            'default_delivery_time': self.quality_filters['default_delivery_time']
        }
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(accumulation_query, params)
                results = cursor.fetchall()
                
                # 按中转站分组并限制数量
                depot_accumulation_count = defaultdict(int)
                
                for row in results:
                    depot_id = int(row['transit_depot_id'])
                    
                    # 检查该中转站的聚集区数量是否已达上限
                    if depot_accumulation_count[depot_id] >= accumulations_per_depot.get(depot_id, 0):
                        continue
                        
                    # 使用从数据库查询的实际delivery_time，已经通过COALESCE处理了NULL值
                    actual_delivery_time = float(row['delivery_time'])
                    
                    accumulation = {
                        'accumulationId': int(row['accumulation_id']),
                        'accumulationName': row['accumulation_name'] or f"聚集区{row['accumulation_id']}",
                        'longitude': float(row['longitude']),
                        'latitude': float(row['latitude']),
                        'transitDepotId': depot_id,
                        'deliveryTime': actual_delivery_time
                    }
                    self.accumulations.append(accumulation)
                    depot_accumulation_count[depot_id] += 1
                    
            # 记录每个中转站的聚集区分配情况和配送时间统计
            total_unloading_time_found = 0
            total_default_used = 0
            delivery_times = []
            missing_unloading_data = []  # 记录缺失卸货时间的聚集区
            depot_delivery_stats = {}    # 按中转站统计配送时间情况
            
            for accumulation in self.accumulations:
                delivery_times.append(accumulation['deliveryTime'])
                depot_id = accumulation['transitDepotId']
                
                if accumulation['deliveryTime'] != self.quality_filters['default_delivery_time']:
                    total_unloading_time_found += 1
                    # 统计各中转站的实际数据情况
                    if depot_id not in depot_delivery_stats:
                        depot_delivery_stats[depot_id] = {'found': 0, 'missing': 0, 'missing_accs': []}
                    depot_delivery_stats[depot_id]['found'] += 1
                else:
                    total_default_used += 1
                    # 记录缺失卸货时间的聚集区详细信息
                    missing_info = {
                        'accumulationId': accumulation['accumulationId'],
                        'accumulationName': accumulation['accumulationName'],
                        'longitude': accumulation['longitude'],
                        'latitude': accumulation['latitude'],
                        'transitDepotId': depot_id
                    }
                    missing_unloading_data.append(missing_info)
                    
                    # 统计各中转站的缺失情况
                    if depot_id not in depot_delivery_stats:
                        depot_delivery_stats[depot_id] = {'found': 0, 'missing': 0, 'missing_accs': []}
                    depot_delivery_stats[depot_id]['missing'] += 1
                    depot_delivery_stats[depot_id]['missing_accs'].append(missing_info)
            
            for depot_id, count in depot_accumulation_count.items():
                self.logger.info(f"中转站 {depot_id} 分配聚集区: {count} 个")
            
            # 配送时间统计
            if delivery_times:
                avg_delivery_time = sum(delivery_times) / len(delivery_times)
                min_delivery_time = min(delivery_times)
                max_delivery_time = max(delivery_times)
                
                self.logger.info("=" * 50)
                self.logger.info("配送时间数据统计:")
                self.logger.info(f"  使用实际卸货时间: {total_unloading_time_found} 个聚集区")
                self.logger.info(f"  使用默认配送时间: {total_default_used} 个聚集区")
                self.logger.info(f"  实际数据覆盖率: {total_unloading_time_found/len(delivery_times)*100:.1f}%")
                self.logger.info(f"  平均配送时间: {avg_delivery_time:.1f} 分钟")
                self.logger.info(f"  最短配送时间: {min_delivery_time:.1f} 分钟")
                self.logger.info(f"  最长配送时间: {max_delivery_time:.1f} 分钟")
                
                # 按中转站分析配送时间数据完整性
                self.logger.info("")
                self.logger.info("按中转站统计配送时间数据完整性:")
                for depot_id in sorted(depot_delivery_stats.keys()):
                    stats = depot_delivery_stats[depot_id]
                    depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
                    depot_name = depot_info['transitDepotName'] if depot_info else f"中转站{depot_id}"
                    
                    total_accs = stats['found'] + stats['missing']
                    coverage_rate = stats['found'] / total_accs * 100 if total_accs > 0 else 0
                    
                    self.logger.info(f"  {depot_name}: {stats['found']}/{total_accs} = {coverage_rate:.1f}% 覆盖率")
                
                # 报告缺失卸货时间的聚集区详情
                if missing_unloading_data:
                    self.logger.warning(f"⚠️  发现 {len(missing_unloading_data)} 个聚集区缺失卸货时间数据:")
                    
                    # 按中转站分组显示缺失详情
                    for depot_id in sorted(depot_delivery_stats.keys()):
                        stats = depot_delivery_stats[depot_id]
                        if stats['missing'] > 0:
                            depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
                            depot_name = depot_info['transitDepotName'] if depot_info else f"中转站{depot_id}"
                            
                            self.logger.warning(f"  {depot_name} 缺失 {stats['missing']} 个:")
                            
                            # 显示前5个缺失的聚集区作为示例
                            for i, missing_acc in enumerate(stats['missing_accs'][:5]):
                                self.logger.warning(f"    {i+1}. 聚集区{missing_acc['accumulationId']} ({missing_acc['accumulationName']})")
                                self.logger.warning(f"       坐标: ({missing_acc['longitude']:.6f}, {missing_acc['latitude']:.6f})")
                            
                            if len(stats['missing_accs']) > 5:
                                self.logger.warning(f"       ... 还有 {len(stats['missing_accs']) - 5} 个缺失")
                            self.logger.warning("")
                    
                    # 总体建议
                    coverage_rate = total_unloading_time_found / len(delivery_times) * 100
                    if coverage_rate < 20.0:
                        self.logger.error("❌ 严重: 卸货时间数据覆盖率极低，建议检查unloading_time表数据完整性")
                    elif coverage_rate < 50.0:
                        self.logger.warning("⚠️ 警告: 卸货时间数据覆盖率较低，算法精度可能受影响")
                    elif coverage_rate < 80.0:
                        self.logger.info("ℹ️ 提示: 卸货时间数据覆盖率中等，建议完善缺失数据")
                    else:
                        self.logger.info("✅ 卸货时间数据覆盖率良好")
                        
                else:
                    self.logger.info("✅ 所有聚集区都找到了实际卸货时间数据")
                
                self.logger.info("=" * 50)
                    
            self.logger.info(f"提取聚集区数据完成，共 {len(self.accumulations)} 条记录")
            return self.accumulations
            
        except Exception as e:
            self.logger.error(f"提取聚集区数据失败: {e}")
            return []
            
    def extract_transit_depots(self) -> List[Dict]:
        """提取中转站数据 - 基于已提取的班组关系"""
        self.logger.info("开始提取中转站数据...")
        
        if not self.teams:
            self.logger.error("必须先提取班组数据才能提取中转站数据")
            return []
            
        # 获取有效的班组ID列表
        valid_team_ids = [team['teamId'] for team in self.teams]
        team_ids_str = ','.join(map(str, valid_team_ids))
        
        # 为每个班组分配中转站，确保数量平衡
        depots_per_team = {}
        if self.scale_config['max_transit_depots']:
            # 计算每个班组应分配的中转站数量
            total_teams = len(valid_team_ids)
            base_count = self.scale_config['max_transit_depots'] // total_teams
            remainder = self.scale_config['max_transit_depots'] % total_teams
            
            for i, team_id in enumerate(valid_team_ids):
                count = base_count + (1 if i < remainder else 0)
                depots_per_team[team_id] = count
        else:
            # 全量提取，不限制数量
            for team_id in valid_team_ids:
                depots_per_team[team_id] = 99999
        
        # 构建基于关系的查询SQL
        depot_query = f"""
            SELECT 
                transit_depot_id,
                transit_depot_name,
                longitude,
                latitude,
                group_id
            FROM transit_depot 
            WHERE is_delete = 0 
                AND status = '1'
                AND longitude IS NOT NULL 
                AND latitude IS NOT NULL
                AND group_id IN ({team_ids_str})
            ORDER BY group_id, transit_depot_id
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(depot_query)
                results = cursor.fetchall()
                
                # 按班组分组并限制数量
                team_depot_count = defaultdict(int)
                
                for row in results:
                    team_id = int(row['group_id'])
                    
                    # 检查该班组的中转站数量是否已达上限
                    if team_depot_count[team_id] >= depots_per_team.get(team_id, 0):
                        continue
                    
                    # 处理坐标类型转换（数据库中可能是字符串）
                    try:
                        longitude = float(row['longitude'])
                        latitude = float(row['latitude'])
                    except (ValueError, TypeError):
                        self.logger.warning(f"中转站 {row['transit_depot_id']} 坐标无效，跳过")
                        continue
                        
                    transit_depot = {
                        'transitDepotId': int(row['transit_depot_id']),
                        'transitDepotName': row['transit_depot_name'] or f"中转站{row['transit_depot_id']}",
                        'longitude': longitude,
                        'latitude': latitude,
                        'groupId': team_id,
                        'routeCount': self.quality_filters['default_route_count']
                    }
                    self.transit_depots.append(transit_depot)
                    team_depot_count[team_id] += 1
                    
            # 记录每个班组的中转站分配情况
            for team_id, count in team_depot_count.items():
                self.logger.info(f"班组 {team_id} 分配中转站: {count} 个")
                    
            self.logger.info(f"提取中转站数据完成，共 {len(self.transit_depots)} 条记录")
            return self.transit_depots
            
        except Exception as e:
            self.logger.error(f"提取中转站数据失败: {e}")
            return []
            
    def extract_teams(self) -> List[Dict]:
        """提取班组数据 - 作为数据层级的根节点"""
        self.logger.info("开始提取班组数据...")
        
        params = {
            'limit': self.scale_config['max_teams'] or 99999
        }
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(SQL_QUERIES['teams'], params)
                team_results = cursor.fetchall()
                
            # 构建班组数据（transitDepotIds将在提取中转站后填充）
            for row in team_results:
                team_id = int(row['team_id'])
                team = {
                    'teamId': team_id,
                    'teamName': row['team_name'] or f"班组{team_id}",
                    'transitDepotIds': []  # 稍后填充
                }
                self.teams.append(team)
                
            self.logger.info(f"提取班组数据完成，共 {len(self.teams)} 条记录")
            return self.teams
            
        except Exception as e:
            self.logger.error(f"提取班组数据失败: {e}")
            return []
            
    def update_team_relations(self):
        """更新班组与中转站的关系 - 在提取中转站后调用"""
        self.logger.info("更新班组关系数据...")
        
        # 构建中转站到班组的映射
        team_depot_map = defaultdict(list)
        for depot in self.transit_depots:
            team_id = depot['groupId']
            depot_id = depot['transitDepotId']
            team_depot_map[team_id].append(depot_id)
            
        # 更新班组数据中的中转站ID列表
        for team in self.teams:
            team_id = team['teamId']
            team['transitDepotIds'] = team_depot_map.get(team_id, [])
            
        self.logger.info("班组关系数据更新完成")
            
    def _remove_trailing_zeros(self, num: float) -> str:
        """
        移除小数点后的尾随零，不进行四舍五入
        例如: 113.460609 -> "113.460609", 113.500000 -> "113.5"
        """
        # 先转换为字符串，保持原始精度
        str_num = str(num)
        
        # 如果包含小数点，移除尾随零
        if '.' in str_num:
            str_num = str_num.rstrip('0').rstrip('.')
        
        return str_num

    def extract_time_matrix(self) -> Dict[str, Dict]:
        """提取时间矩阵数据 - 仅限同一中转站内的聚集区之间，符合算法约束"""
        self.logger.info("开始提取时间矩阵数据...")
        
        if not self.accumulations or not self.transit_depots:
            self.logger.error("必须先提取聚集区和中转站数据才能提取时间矩阵")
            return {}
            
        # 按中转站分组聚集区
        depot_accumulations = {}
        for acc in self.accumulations:
            depot_id = acc['transitDepotId']
            if depot_id not in depot_accumulations:
                depot_accumulations[depot_id] = []
            depot_accumulations[depot_id].append(acc)
        
        # 统计每个中转站需要的时间矩阵点对
        total_expected_pairs = 0
        depot_expected_pairs = {}
        all_valid_points = set()
        all_point_sources = {}
        
        for depot_id, accumulations in depot_accumulations.items():
            # 找到对应的中转站信息
            depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
            if not depot_info:
                continue
                
            # 该中转站内的所有坐标点（中转站 + 其下属聚集区）
            depot_points = set()
            depot_point_sources = {}
            
            # 添加中转站坐标
            lng_str = self._remove_trailing_zeros(depot_info['longitude'])
            lat_str = self._remove_trailing_zeros(depot_info['latitude'])
            depot_point_key = f"{lng_str},{lat_str}"
            depot_points.add(depot_point_key)
            depot_point_sources[depot_point_key] = f"中转站{depot_id}"
            
            # 添加该中转站下属聚集区坐标
            for acc in accumulations:
                lng_str = self._remove_trailing_zeros(acc['longitude'])
                lat_str = self._remove_trailing_zeros(acc['latitude'])
                acc_point_key = f"{lng_str},{lat_str}"
                depot_points.add(acc_point_key)
                depot_point_sources[acc_point_key] = f"聚集区{acc['accumulationId']}"
            
            # 计算该中转站内需要的点对数（完全图，不包括自环）
            point_count = len(depot_points)
            expected_pairs = point_count * (point_count - 1)
            depot_expected_pairs[depot_id] = expected_pairs
            total_expected_pairs += expected_pairs
            
            # 合并到全局集合
            all_valid_points.update(depot_points)
            all_point_sources.update(depot_point_sources)
        
        self.logger.info(f"需要查找时间矩阵的坐标点: {len(all_valid_points)} 个 (分布在 {len(depot_accumulations)} 个中转站)")
        self.logger.info(f"预期时间矩阵点对数: {total_expected_pairs} 个 (仅限中转站内部)")
        for depot_id, pairs in depot_expected_pairs.items():
            acc_count = len(depot_accumulations.get(depot_id, []))
            self.logger.info(f"  中转站{depot_id}: {acc_count+1}个点 -> {pairs}个点对")
        self.logger.info(f"坐标点样例: {list(all_valid_points)[:3]}")
        
        try:
            # 收集所有坐标点的数值列表
            all_lngs = []
            all_lats = []
            
            # 添加聚集区坐标
            for acc in self.accumulations:
                all_lngs.append(acc['longitude'])
                all_lats.append(acc['latitude'])
            
            # 添加中转站坐标    
            for depot in self.transit_depots:
                all_lngs.append(depot['longitude'])
                all_lats.append(depot['latitude'])
            
            # 构建数值匹配查询（使用浮点数比较，允许微小误差）
            lng_conditions = []
            lat_conditions = []
            
            for lng in all_lngs:
                lng_conditions.append(f"ABS(longitude_start - {lng}) < 0.000001")
                lng_conditions.append(f"ABS(longitude_end - {lng}) < 0.000001")
            
            for lat in all_lats:
                lat_conditions.append(f"ABS(latitude_start - {lat}) < 0.000001")
                lat_conditions.append(f"ABS(latitude_end - {lat}) < 0.000001")
            
            # 按中转站分别查询，确保只获取中转站内部的时间数据
            all_results = []
            
            for depot_id, accumulations in depot_accumulations.items():
                # 找到对应的中转站信息
                depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
                if not depot_info:
                    continue
                
                # 该中转站内的所有坐标点
                depot_lngs = [depot_info['longitude']]
                depot_lats = [depot_info['latitude']]
                
                for acc in accumulations:
                    depot_lngs.append(acc['longitude'])
                    depot_lats.append(acc['latitude'])
                
                # 构建该中转站内部的查询
                lng_values = ','.join(str(lng) for lng in depot_lngs)
                lat_values = ','.join(str(lat) for lat in depot_lats)
                
                depot_query = f"""
                    SELECT 
                        longitude_start,
                        latitude_start,
                        longitude_end,
                        latitude_end,
                        travel_time
                    FROM travel_time
                    WHERE travel_time > 0
                        AND longitude_start IN ({lng_values})
                        AND latitude_start IN ({lat_values})
                        AND longitude_end IN ({lng_values})
                        AND latitude_end IN ({lat_values})
                """
                
                with self.connection.cursor() as cursor:
                    cursor.execute(depot_query)
                    depot_results = cursor.fetchall()
                    all_results.extend(depot_results)
                    self.logger.info(f"中转站{depot_id}查询返回记录数: {len(depot_results)}")
            
            self.logger.info(f"总查询返回记录数: {len(all_results)}")
            
            valid_entries = 0
            db_points = set()
            
            for row in all_results:
                try:
                    from_lng = float(row['longitude_start'])
                    from_lat = float(row['latitude_start'])
                    to_lng = float(row['longitude_end'])
                    to_lat = float(row['latitude_end'])
                    travel_time = float(row['travel_time'])
                    
                    # 格式化坐标点（正确地去除后缀零，不四舍五入）
                    from_point = f"{self._remove_trailing_zeros(from_lng)},{self._remove_trailing_zeros(from_lat)}"
                    to_point = f"{self._remove_trailing_zeros(to_lng)},{self._remove_trailing_zeros(to_lat)}"
                    
                    db_points.add(from_point)
                    db_points.add(to_point)
                    
                    # 生成标准化的矩阵键（使用6位小数格式）
                    from_standard = f"{from_lng:.6f},{from_lat:.6f}"
                    to_standard = f"{to_lng:.6f},{to_lat:.6f}"
                    key = f"{from_standard}->{to_standard}"
                    
                    time_info = {
                        'fromLongitude': from_lng,
                        'fromLatitude': from_lat,
                        'toLongitude': to_lng,
                        'toLatitude': to_lat,
                        'travelTime': travel_time
                    }
                    
                    self.time_matrix[key] = time_info
                    valid_entries += 1
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"时间矩阵数据无效，跳过: {e}")
                    continue
                    
            # 分析结果
            coverage_rate = valid_entries / total_expected_pairs if total_expected_pairs > 0 else 0
            unmatched_points = all_valid_points - db_points
            
            self.logger.info("=" * 50)
            self.logger.info("时间矩阵提取结果 (仅限中转站内部):")
            self.logger.info(f"  算法需要坐标点: {len(all_valid_points)} 个 (分布在 {len(depot_accumulations)} 个中转站)")
            self.logger.info(f"  数据库中坐标点: {len(db_points)} 个") 
            self.logger.info(f"  期望记录数(中转站内部): {total_expected_pairs}")
            self.logger.info(f"  实际获得记录数: {valid_entries}")
            self.logger.info(f"  覆盖率: {coverage_rate:.1%}")
            
            # 分中转站报告覆盖情况
            for depot_id in depot_expected_pairs:
                expected = depot_expected_pairs[depot_id]
                acc_count = len(depot_accumulations.get(depot_id, []))
                depot_name = f"中转站{depot_id}({acc_count+1}点)"
                # 统计该中转站的实际获得点对数
                depot_actual = 0
                depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
                if depot_info:
                    depot_points = {self._remove_trailing_zeros(depot_info['longitude']) + "," + self._remove_trailing_zeros(depot_info['latitude'])}
                    for acc in depot_accumulations[depot_id]:
                        depot_points.add(self._remove_trailing_zeros(acc['longitude']) + "," + self._remove_trailing_zeros(acc['latitude']))
                    
                    # 统计该中转站内的点对数
                    for key in self.time_matrix:
                        from_key = key.split('->')[0]
                        to_key = key.split('->')[1]
                        from_parts = from_key.split(',')
                        to_parts = to_key.split(',')
                        from_point = self._remove_trailing_zeros(float(from_parts[0])) + "," + self._remove_trailing_zeros(float(from_parts[1]))
                        to_point = self._remove_trailing_zeros(float(to_parts[0])) + "," + self._remove_trailing_zeros(float(to_parts[1]))
                        
                        if from_point in depot_points and to_point in depot_points:
                            depot_actual += 1
                
                depot_coverage = depot_actual / expected if expected > 0 else 0
                self.logger.info(f"  {depot_name}: {depot_actual}/{expected} = {depot_coverage:.1%}")
            
            # 报告未匹配的坐标点
            if unmatched_points:
                self.logger.warning(f"⚠️  {len(unmatched_points)} 个坐标点在数据库中无时间数据:")
                for i, point in enumerate(sorted(unmatched_points)):
                    if i < 5:
                        source = all_point_sources.get(point, "未知")
                        self.logger.warning(f"    缺失: {point} ({source})")
                    elif i == 5:
                        self.logger.warning(f"    ... 还有 {len(unmatched_points) - 5} 个")
                        break
            else:
                self.logger.info("✅ 所有坐标点都在数据库中找到时间数据")

            # 分析并报告缺失的点对（仅限中转站内部）
            if coverage_rate < 1.0:
                self.logger.info("🔍 分析缺失的时间矩阵点对 (仅限中转站内部)...")
                missing_pairs = []
                cross_depot_skipped = 0

                # 按中转站分析缺失点对
                for depot_id, accumulations in depot_accumulations.items():
                    depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
                    if not depot_info:
                        continue

                    # 构建该中转站内的所有点
                    depot_points = [(depot_info['longitude'], depot_info['latitude'], f"中转站{depot_id}({depot_info['transitDepotName']})")]
                    for acc in accumulations:
                        depot_points.append((acc['longitude'], acc['latitude'], f"聚集区{acc['accumulationId']}({acc['accumulationName']})"))

                    # 分析该中转站内的缺失点对
                    for i, (lng1, lat1, source1) in enumerate(depot_points):
                        for j, (lng2, lat2, source2) in enumerate(depot_points):
                            if i != j:  # 不是自环
                                key = f"{lng1:.6f},{lat1:.6f}->{lng2:.6f},{lat2:.6f}"
                                if key not in self.time_matrix:
                                    distance = self._calculate_distance(lng1, lat1, lng2, lat2)
                                    missing_pairs.append((key, source1, source2, lng1, lat1, lng2, lat2, distance, depot_id))

                # 显示前8个缺失的点对作为示例
                if missing_pairs:
                    self.logger.warning(f"❌ 发现 {len(missing_pairs)} 个缺失的中转站内部点对，以下是前8个示例:")
                    for i, (key, from_source, to_source, lng1, lat1, lng2, lat2, distance, depot_id) in enumerate(missing_pairs[:8]):
                        self.logger.warning(f"  {i + 1}. {from_source} -> {to_source} (中转站{depot_id})")
                        self.logger.warning(f"     坐标: ({lng1:.6f},{lat1:.6f}) -> ({lng2:.6f},{lat2:.6f})")
                        self.logger.warning(f"     距离: {distance:.2f}km")
                        self.logger.warning("")

                    if len(missing_pairs) > 8:
                        self.logger.warning(f"  ... 还有 {len(missing_pairs) - 8} 个缺失点对未显示")
                else:
                    self.logger.info("✅ 所有中转站内部点对都有时间数据")

            self.logger.info("=" * 50)
            return self.time_matrix
            
        except Exception as e:
            self.logger.error(f"提取时间矩阵数据失败: {e}")
            return {}
            
    def generate_missing_time_matrix(self):
        """为缺失的中转站内部点对生成简化的时间矩阵"""
        self.logger.info("生成缺失的时间矩阵数据 (仅限中转站内部)...")
        
        # 按中转站分组聚集区
        depot_accumulations = {}
        for acc in self.accumulations:
            depot_id = acc['transitDepotId']
            if depot_id not in depot_accumulations:
                depot_accumulations[depot_id] = []
            depot_accumulations[depot_id].append(acc)
        
        # 为每个中转站内部生成缺失的时间数据
        generated_count = 0
        for depot_id, accumulations in depot_accumulations.items():
            # 找到对应的中转站信息
            depot_info = next((d for d in self.transit_depots if d['transitDepotId'] == depot_id), None)
            if not depot_info:
                continue
            
            # 构建该中转站内的所有点
            depot_points = [(depot_info['longitude'], depot_info['latitude'])]
            for acc in accumulations:
                depot_points.append((acc['longitude'], acc['latitude']))
            
            # 为该中转站内的每对点生成时间数据（如果不存在）
            for i, (from_lng, from_lat) in enumerate(depot_points):
                for j, (to_lng, to_lat) in enumerate(depot_points):
                    if i != j:  # 不同点之间
                        key = f"{from_lng:.6f},{from_lat:.6f}->{to_lng:.6f},{to_lat:.6f}"
                        
                        if key not in self.time_matrix:
                            # 使用简化的距离计算生成大概的行驶时间
                            distance = self._calculate_distance(from_lng, from_lat, to_lng, to_lat)
                            # 假设平均速度 40 km/h，转换为分钟
                            travel_time = distance / 40.0 * 60.0
                            
                            time_info = {
                                'fromLongitude': from_lng,
                                'fromLatitude': from_lat,
                                'toLongitude': to_lng,
                                'toLatitude': to_lat,
                                'travelTime': round(travel_time, 2)
                            }
                            
                            self.time_matrix[key] = time_info
                            generated_count += 1
                            
        self.logger.info(f"生成缺失时间矩阵数据完成，新增 {generated_count} 条记录 (仅限中转站内部)")
        
    def _calculate_distance(self, lng1: float, lat1: float, lng2: float, lat2: float) -> float:
        """
        使用Haversine公式计算两点间距离
        
        Returns:
            距离（公里）
        """
        import math
        
        # 地球半径（公里）
        R = 6371.0
        
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat_rad = math.radians(lat2 - lat1)
        delta_lng_rad = math.radians(lng2 - lng1)
        
        # Haversine公式
        a = (math.sin(delta_lat_rad / 2) ** 2 +
             math.cos(lat1_rad) * math.cos(lat2_rad) *
             math.sin(delta_lng_rad / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
        
    def save_json_files(self):
        """保存JSON文件"""
        self.logger.info("开始保存JSON文件...")
        
        # 创建输出目录
        output_dir = os.path.join(
            EXTRACTION_CONFIG['output_base_dir'],
            self.version
        )
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成时间戳
        timestamp = datetime.now().isoformat()
        
        # 保存各个数据文件
        files_to_save = [
            ('accumulations.json', {
                'description': '聚集区数据，包含坐标、配送时间等信息',
                'version': '1.0.0',
                'generatedAt': timestamp,
                'scale': self.scale,
                'scaleDescription': self.scale_config['description'],
                'data': self.accumulations
            }),
            ('transit_depots.json', {
                'description': '中转站数据，包含坐标、所属班组、路线数量等信息',
                'version': '1.0.0',
                'generatedAt': timestamp,
                'scale': self.scale,
                'scaleDescription': self.scale_config['description'],
                'data': self.transit_depots
            }),
            ('teams.json', {
                'description': '班组数据，包含班组层级关系',
                'version': '1.0.0',
                'generatedAt': timestamp,
                'scale': self.scale,
                'scaleDescription': self.scale_config['description'],
                'data': self.teams
            }),
            ('time_matrix.json', {
                'description': '时间矩阵数据，包含所有点对点的行驶时间',
                'version': '1.0.0',
                'generatedAt': timestamp,
                'scale': self.scale,
                'scaleDescription': self.scale_config['description'],
                'data': self.time_matrix
            })
        ]
        
        for filename, data in files_to_save:
            filepath = os.path.join(output_dir, filename)
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                self.logger.info(f"保存文件成功: {filepath}")
            except Exception as e:
                self.logger.error(f"保存文件失败 {filepath}: {e}")
                
        # 保存数据摘要
        summary = {
            'version': self.version,
            'scale': self.scale,
            'scaleDescription': self.scale_config['description'],
            'generatedAt': timestamp,
            'statistics': {
                'accumulations': len(self.accumulations),
                'transitDepots': len(self.transit_depots),
                'teams': len(self.teams),
                'timeMatrixEntries': len(self.time_matrix)
            },
            'qualityFilters': self.quality_filters
        }
        
        summary_path = os.path.join(output_dir, 'data_summary.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"数据提取完成，文件保存到: {output_dir}")
        
    def run_extraction(self):
        """运行完整的数据提取流程 - 确保关系一致性"""
        self.logger.info(f"开始数据提取 - 规模: {self.scale}, 版本: {self.version}")
        
        try:
            # 连接数据库
            if not self.connect_database():
                return False
                
            # 按照层级关系顺序提取数据，确保关系一致性
            self.logger.info("=== 第1步：提取班组数据（顶层） ===")
            teams = self.extract_teams()
            if not teams:
                self.logger.error("班组数据提取失败，终止流程")
                return False
                
            self.logger.info("=== 第2步：提取中转站数据（基于班组关系） ===")
            depots = self.extract_transit_depots()
            if not depots:
                self.logger.error("中转站数据提取失败，终止流程")
                return False
                
            self.logger.info("=== 第3步：更新班组关系数据 ===")
            self.update_team_relations()
            
            self.logger.info("=== 第4步：提取聚集区数据（基于中转站关系） ===")
            accumulations = self.extract_accumulations()
            if not accumulations:
                self.logger.error("聚集区数据提取失败，终止流程")
                return False
                
            self.logger.info("=== 第5步：提取时间矩阵数据（基于所有坐标点） ===")
            self.extract_time_matrix()
            
            self.logger.info("=== 第6步：生成缺失的时间矩阵 ===")
            self.generate_missing_time_matrix()
            
            self.logger.info("=== 第7步：验证数据完整性 ===")
            if not self.validate_data_integrity():
                self.logger.warning("数据完整性验证失败，但将继续保存")
            
            self.logger.info("=== 第8步：保存JSON文件 ===")
            self.save_json_files()
            
            self.logger.info("数据提取流程完成")
            return True
            
        except Exception as e:
            self.logger.error(f"数据提取流程失败: {e}")
            return False
            
        finally:
            self.close_database()
            
    def validate_data_integrity(self) -> bool:
        """验证提取数据的完整性"""
        self.logger.info("开始验证数据完整性...")
        
        issues = []
        
        # 验证聚集区与中转站的关系
        depot_ids = {depot['transitDepotId'] for depot in self.transit_depots}
        orphan_accumulations = []
        for acc in self.accumulations:
            if acc['transitDepotId'] not in depot_ids:
                orphan_accumulations.append(acc['accumulationId'])
                
        if orphan_accumulations:
            issues.append(f"发现 {len(orphan_accumulations)} 个聚集区找不到对应的中转站")
            
        # 验证中转站与班组的关系
        team_ids = {team['teamId'] for team in self.teams}
        orphan_depots = []
        for depot in self.transit_depots:
            if depot['groupId'] not in team_ids:
                orphan_depots.append(depot['transitDepotId'])
                
        if orphan_depots:
            issues.append(f"发现 {len(orphan_depots)} 个中转站找不到对应的班组")
            
        # 验证时间矩阵覆盖度 - 仅计算中转站内部的点对数
        depot_accumulations = {}
        for acc in self.accumulations:
            depot_id = acc['transitDepotId']
            if depot_id not in depot_accumulations:
                depot_accumulations[depot_id] = []
            depot_accumulations[depot_id].append(acc)
        
        # 计算中转站内部期望的点对数
        expected_pairs = 0
        for depot_id, accumulations in depot_accumulations.items():
            # 该中转站内的点数（中转站 + 聚集区）
            point_count = len(accumulations) + 1  # +1 为中转站本身
            # 该中转站内的期望点对数（不包括自环）
            expected_pairs += point_count * (point_count - 1)
        
        actual_pairs = len(self.time_matrix)
        coverage = actual_pairs / expected_pairs if expected_pairs > 0 else 0
        
        if coverage < 0.8:
            issues.append(f"时间矩阵覆盖率较低: {coverage:.2%} ({actual_pairs}/{expected_pairs}) - 仅限中转站内部")
            
        # 记录验证结果
        if issues:
            self.logger.warning("数据完整性验证发现问题:")
            for issue in issues:
                self.logger.warning(f"  - {issue}")
            return False
        else:
            self.logger.info("数据完整性验证通过")
            return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从数据库提取路径规划算法测试数据')
    parser.add_argument('--scale', 
                        choices=['small', 'medium', 'large', 'full'],
                        default='full',
                        help='数据规模')
    parser.add_argument('--version',
                        default=None,
                        help='数据版本')
    
    args = parser.parse_args()
    
    # 创建数据提取器并运行
    extractor = DataExtractor(scale=args.scale, version=args.version)
    success = extractor.run_extraction()
    
    if success:
        print(f"数据提取成功! 规模: {args.scale}, 版本: {args.version or EXTRACTION_CONFIG['default_version']}")
        sys.exit(0)
    else:
        print("数据提取失败!")
        sys.exit(1)


if __name__ == '__main__':
    main() 