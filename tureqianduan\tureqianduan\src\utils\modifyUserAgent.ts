/**由于兼容 */
function createProperty(value) {
    var _value = value;
    function _get() {
      return _value;
    }
    // 重写setter函数
    function _set(v) {
      _value = v;
    }
    return {
      get: _get,
      set: _set
    };
  }
  
  /**
   * 给定对象，创建或替换它的可写属性
   * @param {Object} objBase  e.g. window
   * @param {String} objScopeName    e.g. "navigator"
   * @param {String} propName    e.g. "userAgent"
   * @param {Any} initValue (optional)   e.g. window.navigator.userAgent
   */
  export function makePropertyWritable(
    objBase: object,
    objScopeName: string,
    propName: string,
    initValue?: any
  ) {
    let newProp, initObj;
  
    if (objBase && objScopeName in objBase && propName in objBase[objScopeName]) {
      if (typeof initValue === "undefined") {
        initValue = objBase[objScopeName][propName];
      }
      newProp = createProperty(initValue);
      try {
        Object.defineProperty(objBase[objScopeName], propName, newProp);
      } catch (e) {
        initObj = {};
        initObj[propName] = newProp;
        try {
          objBase[objScopeName] = Object.create(objBase[objScopeName], initObj);
        } catch (e) {
          console.error(e);
        }
      }
    }
  }
  
  /**
   * 判断如果是统信系统浏览器就修改userAgent
   * @param str
   * @returns
   */
  export function modifyUserAgent() {
    let str = window.navigator.userAgent;
    // console.log(str)
    // 判断字符串中是否包含 "UOS"
    if (str.includes("UOS") || str.includes("Linux")) {
      // 使用正则表达式匹配第一个括号中的内容
      // str = str.replace(/\(.*?\)/, "(Windows NT 10.0; Win64; X64 aarch64)");
      str = str.replace(/\(.*?\)/, "(Windows NT 10.0; Win64;x64)");
      makePropertyWritable(window, "navigator", "userAgent", str);
      console.log(window.navigator.userAgent);
    }
  }
  