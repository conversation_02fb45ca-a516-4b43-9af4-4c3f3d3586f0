-- 为group表添加colour字段的数据库迁移脚本
-- 执行时间：2025-08-19

-- 1. 添加colour字段
ALTER TABLE `group` ADD COLUMN `colour` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '班组颜色';

-- 2. 为现有班组分配颜色
-- 定义一组颜色，确保每个班组有不同的颜色
UPDATE `group` SET `colour` = '#FF6B6B' WHERE `group_id` = 1;  -- 红色
UPDATE `group` SET `colour` = '#4ECDC4' WHERE `group_id` = 2;  -- 青色
UPDATE `group` SET `colour` = '#45B7D1' WHERE `group_id` = 3;  -- 蓝色
UPDATE `group` SET `colour` = '#96CEB4' WHERE `group_id` = 4;  -- 绿色
UPDATE `group` SET `colour` = '#FFEAA7' WHERE `group_id` = 5;  -- 黄色
UPDATE `group` SET `colour` = '#DDA0DD' WHERE `group_id` = 6;  -- 紫色
UPDATE `group` SET `colour` = '#FFB347' WHERE `group_id` = 7;  -- 橙色
UPDATE `group` SET `colour` = '#98D8C8' WHERE `group_id` = 8;  -- 薄荷绿
UPDATE `group` SET `colour` = '#F7DC6F' WHERE `group_id` = 9;  -- 浅黄色
UPDATE `group` SET `colour` = '#BB8FCE' WHERE `group_id` = 10; -- 浅紫色

-- 3. 为其他可能存在的班组设置默认颜色
UPDATE `group` SET `colour` = '#95A5A6' WHERE `colour` IS NULL; -- 灰色作为默认颜色

-- 4. 验证更新结果
SELECT group_id, group_name, colour FROM `group` ORDER BY group_id;
