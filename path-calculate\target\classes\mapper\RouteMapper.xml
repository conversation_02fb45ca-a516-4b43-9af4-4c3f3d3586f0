<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.RouteMapper">

    <select id="selectTransitDepotId" resultType="java.lang.Long">
        select DISTINCT transit_depot_id from route where is_delete=0
    </select>

    <select id="selectTranByRoutId" resultType="java.lang.Long">
        select transit_depot_id from route where route_id=#{routeId}
    </select>

    <select id="myUpdate" >
        update route set work_time=#{workTime} where route_id=#{routeId}
    </select>

    <select id="selectRouteNumByTanId" resultType="int">
        select SUM(route_number) AS route_number from delivery_area where transit_depot_id=#{transitDepotId}
    </select>
</mapper>