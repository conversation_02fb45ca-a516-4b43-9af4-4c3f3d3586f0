/*
package com.ict.ycwl.clustercalculate.mapper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

*/
/**
 * <AUTHOR>
 *//*

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("store")
public class Store {

    @TableId(type = IdType.ASSIGN_ID)
    private Long storeId;

    private String customerCode;

    private String storeName;

    private String storeAddress;

    private Double longitude;

    private Double latitude;

    private String type;

    private String orderCycle;

    private String district;

    private String areaName;

    private String contactName;

    private String contactPhone;

    private String status;

    private Long customerManagerId;

    private String customerManagerName;

    private Long accumulationId;

    private Long areaId;

    private Long routeId;

    private String routeName;

    private String isDelete;

    private Timestamp createTime;

    private Timestamp updateTime;

    private String gear;

    private Long createBy;

    private Long updateBy;

    private Long groupId;

    private String locationType;
}
*/
