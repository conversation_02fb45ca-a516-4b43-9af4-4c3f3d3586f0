# 算法调试数据导出功能说明

## 功能概述

本模块为路径规划算法提供全面的调试数据导出功能，可以导出算法各个阶段的中间结果，便于分析和优化算法性能。

## 自动调试数据生成

当执行 `PathPlanningUtils.calculate()` 时，系统会自动生成以下调试数据：

### 输出目录
```
target/test-results/algorithm/debug/
```

### 生成的文件

1. **聚类阶段结果** - `clustering_results_{sessionId}.json`
   - 聚集区分配到路线的结果
   - 包含每个中转站的聚类详情
   - 统计信息：总中转站数、总路线数、总聚集区数

2. **TSP优化结果** - `tsp_results_{sessionId}.json`
   - 路线内访问顺序优化结果
   - 包含每条路线的优化序列
   - 统计信息：总路线数、平均工作时间

3. **凸包处理结果** - `convex_hull_results_{sessionId}.json`
   - 凸包生成和冲突解决结果
   - 包含凸包坐标点和冲突解决记录
   - 冲突类型和解决策略

4. **时间均衡结果** - `time_balance_results_{sessionId}.json`
   - 多层级时间均衡优化结果
   - 包含均衡统计和调整记录
   - 路线、中转站、班组时间方差

5. **最终算法结果** - `final_results_{sessionId}.json`
   - 完整的路径规划算法输出
   - 执行时间、总路线数、总工作时间
   - 时间均衡统计信息

6. **会话摘要** - `session_summary_{sessionId}.json`
   - 调试会话的完整摘要
   - 各阶段执行时间统计
   - 导出文件列表

## 使用示例

### 基本使用
```java
// 执行算法（会自动生成调试数据）
PathPlanningResult result = PathPlanningUtils.calculate(request);

// 调试数据自动保存到 target/test-results/algorithm/debug/
```

### 运行测试验证
```bash
# 运行完整测试套件
mvn test -Dtest=PathPlanningUtilsTest

# 只运行调试数据验证测试
mvn test -Dtest=PathPlanningUtilsTest#testDebugDataValidation
```

## 调试数据结构

### 会话ID格式
```
debug_yyyyMMdd_HHmmss
例如：debug_20250117_143022
```

### JSON数据结构示例

#### 聚类结果
```json
{
  "timestamp": "2025-01-17T14:30:22",
  "sessionId": "debug_20250117_143022",
  "stage": "clustering",
  "description": "聚类阶段：聚集区分配到路线的结果",
  "results": {
    "depot_1": {
      "transitDepotId": 1,
      "transitDepotName": "中转站A",
      "totalClusters": 3,
      "clusters": {
        "cluster_1": {
          "accumulationCount": 15,
          "totalWorkTime": 240.5,
          "accumulations": [...]
        }
      }
    }
  },
  "statistics": {
    "totalDepots": 3,
    "totalRoutes": 12,
    "totalAccumulations": 150
  }
}
```

#### 会话摘要
```json
{
  "sessionId": "debug_20250117_143022",
  "timestamp": "2025-01-17T14:30:25",
  "totalExecutionTime": 2840,
  "stageExecutionTimes": {
    "preprocessing": 120,
    "clustering": 680,
    "tsp": 1200,
    "convexHull": 340,
    "timeBalance": 380,
    "resultBuilding": 120
  },
  "exportedFiles": {
    "clustering": "clustering_results_debug_20250117_143022.json",
    "tsp": "tsp_results_debug_20250117_143022.json",
    "convexHull": "convex_hull_results_debug_20250117_143022.json",
    "timeBalance": "time_balance_results_debug_20250117_143022.json",
    "final": "final_results_debug_20250117_143022.json"
  }
}
```

## 性能分析

### 执行时间分析
通过 `session_summary` 文件可以分析：
- 总执行时间
- 各阶段耗时占比
- 性能瓶颈识别

### 算法效果评估
通过各阶段调试数据可以评估：
- 聚类质量（时间均衡、地理紧凑性）
- TSP优化效果（路线长度、访问顺序）
- 凸包冲突频率
- 时间均衡改善程度

## 注意事项

1. **存储空间**：调试数据会占用一定磁盘空间，建议定期清理旧的调试文件
2. **性能影响**：调试数据导出会增加少量执行时间（通常<5%）
3. **并发安全**：每个算法执行会生成独立的会话ID，支持并发调试
4. **数据格式**：所有调试数据都是JSON格式，便于程序化分析

## 故障排除

### 调试文件未生成
1. 检查输出目录权限
2. 确认算法执行成功
3. 查看日志错误信息

### 调试数据不完整
1. 检查算法是否完整执行
2. 确认没有异常中断
3. 验证JSON文件格式正确性

## 开发者信息

- **模块版本**：1.0.0
- **创建日期**：2025-01-17
- **维护团队**：算法优化团队