/**
 * 地图问题修复脚本
 * 用于自动检测和修复常见的地图显示问题
 */

const fs = require('fs');
const path = require('path');

class MapIssueFixer {
    constructor() {
        this.issues = [];
        this.fixes = [];
    }

    /**
     * 检查环境变量文件
     */
    checkEnvFiles() {
        console.log('🔍 检查环境变量文件...');
        
        const envFiles = [
            'tureqianduan/tureqianduan/.env.prd',
            'first/test/.env.development'
        ];

        for (const envFile of envFiles) {
            if (fs.existsSync(envFile)) {
                const content = fs.readFileSync(envFile, 'utf8');
                console.log(`✅ ${envFile} 存在`);
                
                // 检查必要的环境变量
                const requiredVars = ['VITE_MAP_KEY', 'VITE_SECURITY_CODE', 'VITE_BASE_URL'];
                for (const varName of requiredVars) {
                    if (!content.includes(varName)) {
                        this.issues.push(`${envFile} 缺少 ${varName}`);
                    }
                }
            } else {
                this.issues.push(`环境文件不存在: ${envFile}`);
            }
        }
    }

    /**
     * 检查package.json启动脚本
     */
    checkPackageScripts() {
        console.log('🔍 检查package.json启动脚本...');
        
        const packageFiles = [
            'tureqianduan/tureqianduan/package.json',
            'first/test/package.json'
        ];

        for (const packageFile of packageFiles) {
            if (fs.existsSync(packageFile)) {
                const content = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
                console.log(`✅ ${packageFile} 存在`);
                
                if (content.scripts && content.scripts.dev) {
                    console.log(`  启动脚本: ${content.scripts.dev}`);
                } else {
                    this.issues.push(`${packageFile} 缺少dev启动脚本`);
                }
            }
        }
    }

    /**
     * 检查地图相关文件
     */
    checkMapFiles() {
        console.log('🔍 检查地图相关文件...');
        
        const mapFiles = [
            'tureqianduan/tureqianduan/src/utils/getMapKey.ts',
            'tureqianduan/tureqianduan/src/utils/modifyUserAgent.ts',
            'tureqianduan/tureqianduan/src/pages/Home/Computer/Route/Route.vue'
        ];

        for (const mapFile of mapFiles) {
            if (fs.existsSync(mapFile)) {
                console.log(`✅ ${mapFile} 存在`);
            } else {
                this.issues.push(`地图文件不存在: ${mapFile}`);
            }
        }
    }

    /**
     * 修复环境变量问题
     */
    fixEnvIssues() {
        console.log('🔧 修复环境变量问题...');
        
        // 确保tureqianduan项目有正确的.env.prd文件
        const envPrdPath = 'tureqianduan/tureqianduan/.env.prd';
        const envPrdContent = `VITE_BASE_URL="http://localhost:8080"
VITE_MAP_KEY = '004bb8268ef0cff3264e9b2a8816e29c'
VITE_MAP_API_KEY = '3729e38b382749ba3a10bae7539e0d9a'
VITE_SECURITY_CODE = '92707992ffdad18bfbf2f31e9bd158bd'
`;

        if (!fs.existsSync(envPrdPath)) {
            fs.writeFileSync(envPrdPath, envPrdContent);
            this.fixes.push(`创建了 ${envPrdPath}`);
        }

        // 确保first项目有正确的.env.development文件
        const envDevPath = 'first/test/.env.development';
        const envDevContent = `VITE_APP_BASE_API = 'http://localhost:8080'
VITE_MAP_KEY = '004bb8268ef0cff3264e9b2a8816e29c'
VITE_MAP_API_KEY = '3729e38b382749ba3a10bae7539e0d9a'
VITE_SECURITY_CODE = '92707992ffdad18bfbf2f31e9bd158bd'
`;

        if (!fs.existsSync(envDevPath)) {
            fs.writeFileSync(envDevPath, envDevContent);
            this.fixes.push(`创建了 ${envDevPath}`);
        }
    }

    /**
     * 创建地图调试页面
     */
    createDebugPage() {
        console.log('🔧 创建地图调试页面...');
        
        const debugPagePath = 'tureqianduan/tureqianduan/public/debug-map.html';
        
        if (!fs.existsSync(debugPagePath)) {
            // 复制调试页面
            if (fs.existsSync('debug-map.html')) {
                fs.copyFileSync('debug-map.html', debugPagePath);
                this.fixes.push(`创建了地图调试页面: ${debugPagePath}`);
            }
        }
    }

    /**
     * 检查并修复vite配置
     */
    fixViteConfig() {
        console.log('🔧 检查vite配置...');
        
        const viteConfigPath = 'tureqianduan/tureqianduan/vite.config.ts';
        
        if (fs.existsSync(viteConfigPath)) {
            const content = fs.readFileSync(viteConfigPath, 'utf8');
            
            // 检查是否包含loadEnv
            if (!content.includes('loadEnv')) {
                console.log('⚠️ vite.config.ts 可能需要更新以正确加载环境变量');
                this.issues.push('vite.config.ts 需要更新环境变量加载逻辑');
            }
        }
    }

    /**
     * 生成修复报告
     */
    generateReport() {
        console.log('\n📊 修复报告');
        console.log('='.repeat(50));
        
        if (this.issues.length > 0) {
            console.log('\n❌ 发现的问题:');
            this.issues.forEach((issue, index) => {
                console.log(`  ${index + 1}. ${issue}`);
            });
        }

        if (this.fixes.length > 0) {
            console.log('\n✅ 已应用的修复:');
            this.fixes.forEach((fix, index) => {
                console.log(`  ${index + 1}. ${fix}`);
            });
        }

        if (this.issues.length === 0 && this.fixes.length === 0) {
            console.log('\n🎉 未发现问题，系统配置正常！');
        }

        console.log('\n🔧 建议的下一步操作:');
        console.log('  1. 重启前端开发服务器');
        console.log('  2. 清除浏览器缓存');
        console.log('  3. 检查浏览器控制台是否有错误信息');
        console.log('  4. 访问 http://localhost:5173/debug-map.html 进行详细诊断');
    }

    /**
     * 运行完整修复流程
     */
    async run() {
        console.log('🚀 开始地图问题修复流程...\n');

        this.checkEnvFiles();
        this.checkPackageScripts();
        this.checkMapFiles();
        
        this.fixEnvIssues();
        this.createDebugPage();
        this.fixViteConfig();
        
        this.generateReport();
        
        console.log('\n✨ 修复流程完成！');
    }
}

// 运行修复脚本
if (require.main === module) {
    const fixer = new MapIssueFixer();
    fixer.run().catch(console.error);
}

module.exports = MapIssueFixer;
