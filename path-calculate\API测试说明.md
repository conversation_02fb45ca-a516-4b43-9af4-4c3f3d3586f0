# 工作参数配置API测试说明

## 功能概述

已成功为粤北卷烟物流管理系统的path-calculate模块新增了工作参数配置管理功能。

## 数据库验证

数据表已创建成功，包含7条记录：

```sql
SELECT * FROM work_parameter;
```

结果：
```
+----+--------------------+----------------+----------------+--------------------+----------------+---------------------+---------------------+------------+
| id | name               | max_work_hours | flexible_range | optimal_work_hours | stop_threshold | create_time         | update_time         | is_deleted |
+----+--------------------+----------------+----------------+--------------------+----------------+---------------------+---------------------+------------+
|  1 | 韶关市             |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
|  2 | 新丰县中转站       |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
|  3 | 坪石镇中转站       |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
|  4 | 翁源县中转站       |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
|  5 | 马市烟叶工作站     |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
|  6 | 班组一物流配送中心 |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
|  7 | 班组二物流配送中心 |           8.00 |           0.50 |               6.50 |           0.85 | 2025-08-15 00:20:41 | 2025-08-15 00:20:41 |          0 |
+----+--------------------+----------------+----------------+--------------------+----------------+---------------------+---------------------+------------+
```

## API接口列表

### 1. 获取所有工作参数配置
- **URL**: `GET /path/workParameter/list`
- **描述**: 获取所有工作参数配置列表
- **测试命令**:
```bash
curl -X GET "http://localhost:8080/path/workParameter/list"
```

### 2. 根据站点名称获取工作参数
- **URL**: `GET /path/workParameter/getByName?name={站点名称}`
- **描述**: 根据站点名称获取对应的工作参数，如果不存在则返回全局参数
- **测试命令**:
```bash
# 获取韶关市（全局参数）
curl -X GET "http://localhost:8080/path/workParameter/getByName?name=韶关市"

# 获取班组一物流配送中心
curl -X GET "http://localhost:8080/path/workParameter/getByName?name=班组一物流配送中心"

# 获取不存在的站点（应该返回全局参数）
curl -X GET "http://localhost:8080/path/workParameter/getByName?name=不存在的站点"
```

### 3. 更新工作参数
- **URL**: `POST /path/workParameter/update`
- **描述**: 更新单个工作参数配置
- **测试命令**:
```bash
curl -X POST "http://localhost:8080/path/workParameter/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "韶关市",
    "maxWorkHours": 8.50,
    "flexibleRange": 0.60,
    "optimalWorkHours": 7.00,
    "stopThreshold": 0.90
  }'
```

### 4. 批量更新工作参数
- **URL**: `POST /path/workParameter/batchUpdate`
- **描述**: 批量更新多个工作参数配置
- **测试命令**:
```bash
curl -X POST "http://localhost:8080/path/workParameter/batchUpdate" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "id": 1,
      "name": "韶关市",
      "maxWorkHours": 8.50,
      "flexibleRange": 0.60,
      "optimalWorkHours": 7.00,
      "stopThreshold": 0.90
    },
    {
      "id": 2,
      "name": "新丰县中转站",
      "maxWorkHours": 8.00,
      "flexibleRange": 0.50,
      "optimalWorkHours": 6.50,
      "stopThreshold": 0.85
    }
  ]'
```

### 5. 获取全局工作参数
- **URL**: `GET /path/workParameter/global`
- **描述**: 获取全局工作参数（韶关市）
- **测试命令**:
```bash
curl -X GET "http://localhost:8080/path/workParameter/global"
```

## 独立的工作参数管理接口

除了在PathController中集成的接口外，还提供了独立的WorkParameterController：

- **基础路径**: `/workParameter`
- **接口列表**:
  - `GET /workParameter/list` - 获取所有参数
  - `GET /workParameter/getByName` - 根据名称获取参数
  - `POST /workParameter/update` - 更新参数
  - `POST /workParameter/batchUpdate` - 批量更新参数
  - `GET /workParameter/global` - 获取全局参数

## 预期响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "韶关市",
      "maxWorkHours": 8.00,
      "flexibleRange": 0.50,
      "optimalWorkHours": 6.50,
      "stopThreshold": 0.85,
      "createTime": "2025-08-15T00:20:41",
      "updateTime": "2025-08-15T00:20:41",
      "isDeleted": 0
    }
  ]
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "获取工作参数配置失败: 错误详情"
}
```

## 启动应用测试

1. 启动path-calculate应用
2. 访问Swagger文档：`http://localhost:8080/swagger-ui.html`
3. 在"工作参数配置API"或"路径计算API"分组中找到相关接口
4. 使用上述curl命令或Swagger界面进行测试

## 功能特点

1. **站点名称唯一性**: 每个站点名称在系统中是唯一的
2. **全局参数**: "韶关市"作为全局参数，当特定站点没有配置时会使用全局参数
3. **数据验证**: 所有数值参数都有合理的默认值和验证
4. **软删除**: 使用逻辑删除，不会物理删除数据
5. **事务支持**: 更新操作支持事务回滚
6. **双重接口**: 既集成在PathController中，也提供独立的WorkParameterController

## 文件清单

- `WorkParameter.java` - 实体类
- `WorkParameterMapper.java` - Mapper接口
- `WorkParameterMapper.xml` - MyBatis映射文件
- `WorkParameterService.java` - 服务接口
- `WorkParameterServiceImpl.java` - 服务实现
- `WorkParameterController.java` - 独立控制器
- `PathController.java` - 集成接口（已修改）
- `create_work_parameter_table.sql` - 建表SQL
- `工作参数配置功能说明.md` - 功能文档
