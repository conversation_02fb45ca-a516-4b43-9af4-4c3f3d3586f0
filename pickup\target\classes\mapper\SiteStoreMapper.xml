<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pickup.mapper.SiteStoreMapper">

    <select id="selectBySiteId" resultType="com.ict.ycwl.pickup.pojo.vo.pickupLocation.SiteStoreVo">
        select s.contact_name as storeName, s.id as storeId
        from site_store
                 JOIN pickup_user s on site_store.store_id = s.id
        where site_store.site_selection_id = #{id}
    </select>

    <delete id="delete">
        delete from site_store where store_id =#{storeId} and site_selection_id=#{id}
    </delete>

    <select id="selectByStoreId" resultType="com.ict.ycwl.pickup.pojo.entity.SiteStore">
        select * from site_store where store_id=#{id}
    </select>

    <delete id="deleteBySiteSelectionId">
        delete from site_store where site_selection_id=#{id1}
    </delete>

    <delete id="deleteAll">
        delete from site_store
    </delete>

    <select id="selectCountBySiteId" resultType="int">
        select COUNT(*) from site_store where site_selection_id=#{id}
    </select>
</mapper>