<template>
  <el-tooltip
    effect="dark"
    placement="top"
    :content-style="{ width: '220px' }"
    :show-after="100"
  >
    <template #content>
      <div class="tooltip-content">
        <div><strong>版本名:</strong> {{ versionName }}</div>
        <div><strong>备注:</strong> {{ versionInfo }}</div>
        <div><strong>更新时间:</strong> {{ formattedTime }}</div>
      </div>
    </template>

    <div class="t-item" :class="{ clicked: isSelected }" @click="handleClick">
      <div class="info">
        <div class="stat">
          <div
            class="dot"
            :style="{ backgroundColor: status ? '#44C444' : '#f85b5b' }"
          ></div>
          <div class="name">{{ versionName }}</div>
        </div>
        <div class="update-time">更新时间: {{ formattedTime }}</div>
      </div>
      <div class="black"></div>
      <div class="iconImage" @click.stop="toggleActionMenu">
        <el-icon size="20"><More /></el-icon>
      </div>

      <!-- 操作浮窗 -->
      <div v-if="showActionMenu" class="action-menu">
        <div 
          v-if="hasOp('version-management:rename')"
          class="action-item" 
          @click.stop="handleRename"
        >
          <el-icon><EditPen /></el-icon>
          <span>重命名</span>
        </div>
        <div 
          v-if="hasOp('version-management:delete')"
          class="action-item delete" 
          @click.stop="handleDelete"
        >
          <el-icon><Delete /></el-icon>
          <span>删除版本</span>
        </div>
      </div>
    </div>
  </el-tooltip>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted, onUnmounted } from "vue";
  import { More, EditPen, Delete } from "@element-plus/icons-vue";
  import { ElTooltip } from "element-plus";
  import { hasOp } from "@/op";

  const props = defineProps({
    versionId: {
      type: Number,
    },
    versionName: {
      type: String,
      default: "测试",
    },
    updateTime: {
      type: [Number, Date],
      required: true,
    },
    status: {
      type: Boolean,
      required: true,
    },
    isShow: {
      type: Number,
    },
    versionInfo: {
      type: String,
    },
    versionDb: {
      type: String,
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(["rename", "delete", "click"]);

  // 控制操作菜单显示
  const showActionMenu = ref<boolean>(false);

  // 处理点击事件
  const handleClick = () => {
    // 触发 click 事件并传递数据
    emit("click", getCurrentItemData());
  };

  // 切换操作菜单显示状态
  const toggleActionMenu = (event: Event) => {
    event.stopPropagation();
    showActionMenu.value = !showActionMenu.value;
  };

  // 处理重命名
  const handleRename = () => {
    emit("rename", getCurrentItemData());
    showActionMenu.value = false;
  };

  // 处理删除
  const handleDelete = () => {
    emit("delete", getCurrentItemData());
    showActionMenu.value = false;
  };

  // 获取当前项数据
  const getCurrentItemData = () => {
    return {
      versionId: props.versionId,
      versionName: props.versionName,
      versionInfo: props.versionInfo,
      versionDb: props.versionDb,
      status: props.status,
      isShow: props.isShow,
      isSelected: props.isSelected,
      formattedTime: formattedTime.value,
    };
  };

  // 点击外部关闭菜单
  const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    const iconElement = document.querySelector(".iconImage");
    const menuElement = document.querySelector(".action-menu");

    if (
      showActionMenu.value &&
      iconElement &&
      menuElement &&
      !iconElement.contains(target) &&
      !menuElement.contains(target)
    ) {
      showActionMenu.value = false;
    }
  };

  // 监听全局点击事件
  onMounted(() => {
    document.addEventListener("click", handleClickOutside);
  });

  onUnmounted(() => {
    document.removeEventListener("click", handleClickOutside);
  });

  const formattedTime = computed(() => {
    let date: Date;

    if (props.updateTime instanceof Date) {
      date = props.updateTime;
    } else {
      date = new Date(props.updateTime);
    }

    // 格式化为 YYYY-MM-DD 格式
    return (
      date.getFullYear() +
      "-" +
      String(date.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(date.getDate()).padStart(2, "0")
    );
  });
</script>

<style lang="less" scoped>
  .t-item {
    margin-left: 1vw;
    padding-top: 1vh;
    padding-bottom: 1vh;
    padding-bottom: 1vh;
    box-sizing: border-box;
    padding-left: 1vw;
    width: 92.6%;
    height: max-content;
    display: flex;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;

    .black {
      flex: 1;
    }

    // 点击后的样式
    &.clicked {
      background-color: #0377a8;
    }

    .info {
      display: flex;
      flex-direction: column;

      .stat {
        display: flex;
        align-items: center;

        .dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }

      .name {
        color: #a9ced9;
        margin-bottom: 4px;
      }

      .update-time {
        margin-left: 1vw;
        font-size: 12px;
      }
    }

    .iconImage {
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    // 操作浮窗样式
    .action-menu {
      position: absolute;
      right: 0;
      top: 100%;
      margin-top: 5px;
      background-color: #011733;
      border: 1px solid #1e3a5f;
      border-radius: 4px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
      z-index: 1000;
      min-width: 120px;

      .action-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        color: #a9ced9;
        transition: background-color 0.2s;

        &:hover {
          background-color: #0377a8;
        }

        .el-icon {
          margin-right: 8px;
          font-size: 16px;
        }

        &.delete {
          color: #f56c6c;
        }
      }
    }
  }

  .tooltip-content {
    :global(.el-popper.is-dark) {
      /* Set padding to ensure the height is 32px */
      padding: 10px 12px !important;
      background: #011733;
      border: 1px #7ef4ff solid;
    }
    div {
      margin-bottom: 5px;
      font-size: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
