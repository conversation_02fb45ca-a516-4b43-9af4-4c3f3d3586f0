<!DOCTYPE html>
<html>
<head>
    <title>测试聚集区微调修复</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>测试聚集区微调修复</h1>
    <button onclick="testOriginal()">测试原始方法</button>
    <button onclick="testFixed()">测试修复后方法</button>
    <div id="result"></div>

    <script>
        const baseURL = 'http://localhost:8080';
        
        // 原始方法（有问题的）
        async function testOriginal() {
            try {
                const data = {
                    longitude: 113.285277,
                    latitude: 25.010874,
                    accumulationId: "7049"
                };
                
                const response = await axios.post(
                    `${baseURL}/clustercalculate/cluster/updateStoreAccumulationId`,
                    data,
                    {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    }
                );
                
                document.getElementById('result').innerHTML = 
                    '<h3>原始方法结果:</h3><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3>原始方法错误:</h3><pre>' + error.message + '</pre>';
            }
        }
        
        // 修复后的方法
        async function testFixed() {
            try {
                const data = {
                    longitude: 113.285277,
                    latitude: 25.010874,
                    accumulationId: "7049"
                };
                
                // 将对象转换为URL编码格式
                const formData = new URLSearchParams();
                formData.append('longitude', data.longitude.toString());
                formData.append('latitude', data.latitude.toString());
                formData.append('accumulationId', data.accumulationId.toString());
                
                const response = await axios.post(
                    `${baseURL}/clustercalculate/cluster/updateStoreAccumulationId`,
                    formData,
                    {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    }
                );
                
                document.getElementById('result').innerHTML = 
                    '<h3>修复后方法结果:</h3><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3>修复后方法错误:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
