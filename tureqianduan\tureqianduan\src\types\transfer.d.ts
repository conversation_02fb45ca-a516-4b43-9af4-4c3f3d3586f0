//添加中转点
export interface ITransitDepotAddData {
    deliveryName: string,
    deliveryType: string,
    latitude: string,
    longitude: string,
    status: string,
    teamName: string,
    transitDepotName: string
}

//删除中转点
export interface ITransitDepotDeleteData {
    id: number
}

//获得中转点
export interface ITransitDepotGetData {
    pageNum: number,
    pageSize: number
}

//更新中转点
export interface ITransitDepotUpdateData {
    deliveryName: string,
    deliveryType: string,
    latitude: string,
    longitude: string,
    status: string,
    teamName: string,
    transitDepotId: number,
    transitDepotName: string
}