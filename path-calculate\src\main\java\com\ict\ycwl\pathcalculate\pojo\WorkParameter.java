package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工作参数配置实体类
 * 
 * <AUTHOR>
 * @since 2024-08-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("work_parameter")
@ApiModel(value = "WorkParameter对象", description = "工作参数配置表")
public class WorkParameter {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "站点名称（对应team表的transit_depot_name，韶关市为全局参数）")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "最大工作时间（小时）")
    @TableField("max_work_hours")
    private BigDecimal maxWorkHours;

    @ApiModelProperty(value = "灵活范围（小时）")
    @TableField("flexible_range")
    private BigDecimal flexibleRange;

    @ApiModelProperty(value = "最优工作时间（小时）")
    @TableField("optimal_work_hours")
    private BigDecimal optimalWorkHours;

    @ApiModelProperty(value = "停止阈值")
    @TableField("stop_threshold")
    private BigDecimal stopThreshold;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除（0：未删除，1：已删除）")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}
