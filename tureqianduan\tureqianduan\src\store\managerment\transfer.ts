import { defineStore } from "pinia";
import type {
    ITransitDepotAddData,
    ITransitDepotDeleteData,
    ITransitDepotGetData,
    ITransitDepotUpdateData
} from '@/types/transfer';

import { addTransitDepot, deleteTransitDepot,updateTransitDepot,getTransitDepotInfo, getTransitDepot } from "@/service/modules/management/transfer";

export const useTransferStore = defineStore('transfer', () => {
    
    const transferTotalList = ref<any>([])
    const transferInfo = ref<any>()

    async function addTransitDepotData(params: ITransitDepotAddData) {
         const res = await addTransitDepot(params)
         return res
    }

    async function deleteTransitDepotData(params: ITransitDepotDeleteData) {
        const res = await deleteTransitDepot(params)
        return res
    }

    async function updateTransitDepotData(params: ITransitDepotUpdateData) {
        const res = await updateTransitDepot(params)
        return res
    }

    async function getTransitDepotData(params: ITransitDepotGetData) {
        const res :any = await getTransitDepot(params)
        transferTotalList.value = res.data.records
    }

    async function getTransitDepotTotalData(pageSize: number) {
        let pageNum = 1;
        let hasMoreData = true;
        transferTotalList.value = [];
        while (hasMoreData) {
            try {
                const res :any= await getTransitDepot({pageNum, pageSize})
                transferTotalList.value = [...transferTotalList.value,...res.data.records]
            // 检查是否还有更多数据
                hasMoreData = res.data.records.length === pageSize;
                pageNum++;
            } catch (error) {
                console.error('请求数据时出错:', error);
                hasMoreData = false; // 出现错误时停止请求
            }
        }
    }

    async function getTransitDepotDataInfo() {
        const res :any = await getTransitDepotInfo()
        transferInfo.value = res.data
    }

    return {
        addTransitDepotData,
        deleteTransitDepotData,
        updateTransitDepotData,
        getTransitDepotData,
        getTransitDepotDataInfo,
        getTransitDepotTotalData,
        transferTotalList,
        transferInfo
    }
})