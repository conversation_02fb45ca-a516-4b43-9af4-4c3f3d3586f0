<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>色块数据调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>🔍 色块数据调试工具</h1>
    
    <div class="section">
        <h2>1. 检查localStorage数据</h2>
        <button class="button" onclick="checkLocalStorage()">检查localStorage</button>
        <div id="localStorage-result"></div>
    </div>
    
    <div class="section">
        <h2>2. 测试API接口</h2>
        <button class="button" onclick="testAPI()">测试getAllColourConvex接口</button>
        <div id="api-result"></div>
    </div>
    
    <div class="section">
        <h2>3. 清除缓存</h2>
        <button class="button" onclick="clearCache()">清除localStorage缓存</button>
        <div id="clear-result"></div>
    </div>

    <script>
        function checkLocalStorage() {
            const result = document.getElementById('localStorage-result');
            try {
                const convexColor = localStorage.getItem('convexColor');
                const convex = localStorage.getItem('convex');
                
                let html = '<h3>localStorage数据:</h3>';
                html += '<p><strong>convexColor:</strong></p>';
                if (convexColor && convexColor !== 'undefined') {
                    const parsed = JSON.parse(convexColor);
                    html += `<pre>${JSON.stringify(parsed, null, 2)}</pre>`;
                    html += `<p class="success">✅ 数据存在，包含 ${parsed?.length || 0} 个班组的色块数据</p>`;
                } else {
                    html += '<p class="error">❌ convexColor数据为空或undefined</p>';
                }
                
                html += '<p><strong>convex:</strong></p>';
                if (convex && convex !== 'undefined') {
                    const parsed = JSON.parse(convex);
                    html += `<p class="success">✅ convex数据存在，包含 ${parsed?.length || 0} 条路线</p>`;
                } else {
                    html += '<p class="error">❌ convex数据为空或undefined</p>';
                }
                
                result.innerHTML = html;
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 检查localStorage时出错: ${error.message}</p>`;
            }
        }
        
        async function testAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '<p>🔄 正在测试API...</p>';
            
            try {
                const response = await fetch('http://localhost:8084/path/getAllColourConvex');
                const data = await response.json();
                
                let html = '<h3>API响应:</h3>';
                html += `<p><strong>状态码:</strong> ${response.status}</p>`;
                html += `<p><strong>响应数据:</strong></p>`;
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                if (data.code === 200 && data.data) {
                    html += `<p class="success">✅ API调用成功，返回 ${data.data.length} 个班组的色块数据</p>`;
                    
                    // 检查每个班组的数据
                    data.data.forEach((group, index) => {
                        html += `<p>班组${index + 1}: ${group.length} 个色块</p>`;
                    });
                } else {
                    html += '<p class="error">❌ API返回数据异常</p>';
                }
                
                result.innerHTML = html;
            } catch (error) {
                result.innerHTML = `<p class="error">❌ API调用失败: ${error.message}</p>`;
            }
        }
        
        function clearCache() {
            const result = document.getElementById('clear-result');
            try {
                localStorage.removeItem('convexColor');
                localStorage.removeItem('convex');
                result.innerHTML = '<p class="success">✅ localStorage缓存已清除</p>';
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 清除缓存失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
