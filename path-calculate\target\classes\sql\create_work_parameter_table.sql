-- 创建工作参数表
CREATE TABLE IF NOT EXISTS `work_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '站点名称（对应team表的transit_depot_name，韶关市为全局参数）',
  `max_work_hours` decimal(5,2) NOT NULL DEFAULT 8.00 COMMENT '最大工作时间（小时）',
  `flexible_range` decimal(5,2) NOT NULL DEFAULT 0.50 COMMENT '灵活范围（小时）',
  `optimal_work_hours` decimal(5,2) NOT NULL DEFAULT 6.50 COMMENT '最优工作时间（小时）',
  `stop_threshold` decimal(5,2) NOT NULL DEFAULT 0.85 COMMENT '停止阈值',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除（0：未删除，1：已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`) COMMENT '站点名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作参数配置表';

-- 插入初始数据（根据图片显示的7条记录）
INSERT INTO `work_parameter` (`name`, `max_work_hours`, `flexible_range`, `optimal_work_hours`, `stop_threshold`) VALUES
('韶关市', 8.00, 0.50, 6.50, 0.85),
('新丰县中转站', 8.00, 0.50, 6.50, 0.85),
('坪石镇中转站', 8.00, 0.50, 6.50, 0.85),
('翁源县中转站', 8.00, 0.50, 6.50, 0.85),
('马市烟叶工作站', 8.00, 0.50, 6.50, 0.85),
('班组一物流配送中心', 8.00, 0.50, 6.50, 0.85),
('班组二物流配送中心', 8.00, 0.50, 6.50, 0.85);
