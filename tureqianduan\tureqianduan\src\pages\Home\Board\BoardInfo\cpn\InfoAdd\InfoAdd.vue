<template>
  <div class="InfoAdd">
    <el-dialog
      v-model="isOpen"
      width="90%"
      @closed="resetForm(ruleFormRef, ruleFormRef2)"
    >
      <div class="dialog-content">
        <div class="form-content">
          <el-form
            :inline="true"
            :model="addForm"
            label-width="130"
            :rules="rules"
            ref="ruleFormRef"
          >
            <el-form-item label="客户编码" prop="customerCode">
              <el-input
                type="text"
                v-model="addForm.customerCode"
                @blur="selectChange(addForm.customerCode)"
              ></el-input>
            </el-form-item>
            <el-form-item label="送货员" prop="deliveryName">
              <el-select
                v-model="addForm.deliveryName"
                placeholder="请选择送货员名称"
              >
                <el-option
                  v-for="item in boardStore.cond?.deliveryUserList"
                  :key="item.workNumber"
                  :value="item.userName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="订单日期" prop="orderDate">
              <el-date-picker
                v-model="addForm.orderDate"
                type="datetime"
                placeholder="选择日期时间"
                value-format="YYYY-MM-DD hh:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="客户名称" prop="customerCode">
              <el-select
                disabled
                v-model="addForm.contactName"
                placeholder="请选择客户名称"
              >
                <el-option
                  v-for="item in boardStore.cond?.customerManagerList"
                  :key="item.userName"
                  :label="item.userName"
                  :value="item.userName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="路线名称" prop="routeName">
              <el-select
                disabled
                v-model="addForm.routeName"
                placeholder="请选择路线名称"
              >
                <el-option
                  v-for="item in boardStore.cond?.areaList"
                  :key="item.areaName"
                  :value="item.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="客户专员" prop="customerManagerName">
              <el-select
                disabled
                v-model="addForm.customerManagerName"
                placeholder="请选择客户专员名称"
              >
                <el-option
                  v-for="item in boardStore.cond?.customerManagerList"
                  :key="item.workNumber"
                  :value="item.userName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-form :rules="rules" :model="addForm" ref="ruleFormRef2">
          <el-form-item
            class="item"
            label="编辑异常信息"
            prop="feedbackInformation"
          >
            <el-input
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ minRows: 5, maxRows: 20 }"
              v-model="addForm.feedbackInformation"
            ></el-input>
          </el-form-item>
          <el-form-item class="item" label="上传签收照片">
            <el-upload
              accept="image/*"
              multiple
              list-type="picture-card"
              :auto-upload="false"
              v-model:file-list="addForm.fileList"
            >
              <el-icon>
                <Plus />
              </el-icon>

              <template #file="{ file }">
                <div>
                  <img
                    class="el-upload-list__item-thumbnail"
                    :src="file.url"
                    style="width: 100%; height: 100%"
                    alt=""
                  />
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                    >
                      <el-icon><ZoomIn /></el-icon>
                    </span>
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleRemove"
                    >
                      <el-icon><Delete /></el-icon>
                    </span>
                  </span>
                </div>
              </template>
            </el-upload>
          </el-form-item>
          <el-button
            class="btn"
            size="large"
            @click="submitForm(ruleFormRef, ruleFormRef2)"
            >提交</el-button
          >
          <el-button
            class="btn"
            size="large"
            @click="resetForm(ruleFormRef, ruleFormRef2)"
            >清空</el-button
          >
        </el-form>
      </div>
    </el-dialog>
  </div>
  <el-dialog v-model="dialogVisible">
    <img width="100%" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { useBoardStore } from "@/store/board";
  import { storeToRefs } from "pinia";
  import { IAddData, ICond } from "@/types/board";
  import { Plus, ZoomIn, Delete } from "@element-plus/icons-vue";
  import type { FormInstance, FormRules } from "element-plus";
  const boardStore = useBoardStore();
  const { singleCondData } = storeToRefs(boardStore);
  // 打开
  const isOpen = ref<boolean>(false);
  function handleOpen(feedbackType: "1" | "2") {
    isOpen.value = true;
    addForm.value.feedbackType = feedbackType;
    boardStore.getCondAction();
  }
  defineExpose({
    handleOpen,
  });

  const emit = defineEmits(["addSuccess"]);
  const ruleFormRef = ref<FormInstance>();
  const ruleFormRef2 = ref<FormInstance>();
  //校验
  const rules = reactive<FormRules<IAddData>>({
    customerCode: [
      { required: true, message: "请选择客户编码", trigger: "blur" },
    ],
    areaName: [{ required: true, message: "请选择大区名称", trigger: "blur" }],
    routeName: [{ required: true, message: "请选择路线名称", trigger: "blur" }],
    orderDate: [{ required: true, message: "请选择日期", trigger: "blur" }],
    deliveryName: [
      { required: true, message: "请选择送货员", trigger: "blur" },
    ],
    customerManagerName: [
      { required: true, message: "请选择客户专员", trigger: "blur" },
    ],
    feedbackInformation: [
      { required: true, message: "请填写异常信息", trigger: "blur" },
    ],
  });

  // 添加
  const addForm = ref<IAddData>({
    areaName: "",
    customerCode: "",
    deliveryName: "",
    deliveryWorkNumber: "",
    feedbackInformation: "",
    feedbackType: "1",
    orderDate: "",
    routeId: 0,
    routeName: "",
    contactName: "",
    customerManagerName: "",
    fileList: [],
  });

  //预览
  const dialogImageUrl = ref("");
  const dialogVisible = ref(false);

  // 选项修改
  async function selectChange(e: string) {
    boardStore.singleCondDataAction(e).then(() => {
      addForm.value.areaName = singleCondData.value.areaName;
      addForm.value.contactName = singleCondData.value.contactName;
      addForm.value.customerManagerName =
        singleCondData.value.customerManagerName;
      addForm.value.routeId = Number(singleCondData.value.routeId);
      addForm.value.routeName = singleCondData.value.routeName;
    });
  }

  // 提交
  const submitForm = async (
    formEl: FormInstance | undefined,
    formEl2: FormInstance | undefined
  ) => {
    if (!(formEl && formEl2)) return;
    formEl
      .validate(() => {})
      .then((res) => {
        formEl2.validate((valid, fields) => {
          if (valid) {
            if (res) {
              const loading = ElLoading.service({
                lock: true,
                text: "正在提交中",
                background: "rgba(0, 0, 0, 0.7)",
              });
              // 发请求
              boardStore.getCondAction().then(() => {
                const { customerManagerList, deliveryUserList } =
                  boardStore.cond as ICond;
                for (const obj of deliveryUserList) {
                  if (obj.userName == addForm.value.deliveryName) {
                    addForm.value.deliveryWorkNumber = obj.workNumber;
                  }
                }
                for (const obj of customerManagerList) {
                  if (obj.userName == addForm.value.customerManagerName) {
                    addForm.value.managerWorkNumber = obj.workNumber;
                  }
                }
                const formData = new FormData();
                formData.append("areaName", addForm.value.areaName);
                formData.append("customerCode", addForm.value.customerCode);
                formData.append("deliveryName", addForm.value.deliveryName);
                formData.append(
                  "deliveryWorkNumber",
                  addForm.value.deliveryWorkNumber
                );
                formData.append(
                  "managerWorkNumber",
                  addForm.value.managerWorkNumber as any
                );
                formData.append(
                  "feedbackInformation",
                  addForm.value.feedbackInformation
                );
                formData.append("feedbackType", addForm.value.feedbackType);
                formData.append(
                  "routeId",
                  addForm.value.routeId as unknown as string
                );
                formData.append("routeName", addForm.value.routeName);
                formData.append(
                  "customerManagerName",
                  addForm.value.customerManagerName as unknown as string
                );
                formData.append(
                  "orderDate",
                  addForm.value.orderDate as unknown as string
                );
                addForm.value.fileList?.forEach((item) => {
                  formData.append("fileList", item.raw);
                });
                boardStore
                  .addFeedbackAction(formData as unknown as IAddData)
                  .then(() => {
                    loading.close();
                    isOpen.value = false;
                    emit("addSuccess");
                    boardStore.UnhandledAmountAction();
                  });
              });
            }
          } else {
            console.log("error submit!", fields);
          }
        });
      });
  };
  const resetForm = (
    formEl: FormInstance | undefined,
    formEl2: FormInstance | undefined
  ) => {
    if (!(formEl && formEl2)) return;
    formEl.resetFields();
    formEl2.resetFields();
    addForm.value.fileList = [];
  };

  const handleRemove = (file: any) => {
    console.log(file);
    addForm.value.fileList.splice(addForm.value.fileList.indexOf(file), 1);
  };

  const handlePictureCardPreview = (file: any) => {
    dialogImageUrl.value = file.url!;
    dialogVisible.value = true;
  };
</script>
<style lang="scss" scoped>
  .InfoAdd {
    .dialog-content {
      margin: 40px;

      .form-content {
        padding: 30px;
        border: 1px solid $processed;
      }
      .el-form-item {
        width: 29% !important;
      }

      // 添加以下样式修复el-select宽度问题
      :deep(.el-select) {
        width: 100%;
      }

      :deep(.el-input) {
        width: 100%;
      }

      :deep(.el-date-picker) {
        width: 100%;
      }

      .item {
        margin-top: 50px;
      }

      .btn {
        position: relative;
        left: 50%;
      }
    }

    .el-form-item__label {
      font-size: 15px !important;
    }
  }
</style>
