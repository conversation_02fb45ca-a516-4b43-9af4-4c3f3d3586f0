<template>
  <div class="pickup">
    <div class="dataSection">
      <div class="calculate">
        <div class="calData">
          <el-form
            :model="balanceForm"
            class="calForm"
            ref="balanceRef"
            :rules="formRules"
          >
            <el-form-item prop="gear">
              <template #label>
                <el-tooltip placement="top" effect="dark">
                  <img
                    src="../../../../../assets/images/空心问号.png"
                    alt=""
                    width="16px"
                    height="16px"
                    style="padding-right: 4px"
                  />
                  <template #content
                    >客户档位越高,<br />要求配送到店,<br />权重设置越低.</template
                  >
                </el-tooltip>
                <span class="calFont">客户档位</span>
              </template>
              <el-input
                v-model="balanceForm.gear"
                @input="handleNumberInput('gear')"
                class="calInput"
              />
            </el-form-item>
            <el-form-item prop="avgDistance">
              <template #label>
                <el-tooltip placement="top" effect="dark">
                  <img
                    src="../../../../../assets/images/空心问号.png"
                    alt=""
                    width="16px"
                    height="16px"
                    style="padding-right: 4px; margin-left: 1vw"
                  />
                  <template #content
                    >是指商铺距离它所属的打<br />卡点的距离. 这个距离越<br />远,
                    说明越应该定点取货<br
                  /></template>
                </el-tooltip>
                <span class="calFont">平均送货距离</span>
              </template>
              <el-input
                class="calInput"
                @input="handleNumberInput('avgDistance')"
                v-model="balanceForm.avgDistance"
              />
            </el-form-item>
            <el-form-item prop="roadGrade">
              <template #label>
                <el-tooltip placement="top" effect="dark">
                  <img
                    src="../../../../../assets/images/空心问号.png"
                    alt=""
                    width="16px"
                    height="16px"
                    style="padding-right: 4px"
                  />
                  <template #content
                    >客户所在地理位置道路<br />等级越低, 权重越高</template
                  >
                </el-tooltip>
                <span class="calFont">道路等级</span>
              </template>
              <el-input
                class="calInput"
                @input="handleNumberInput('roadGrade')"
                v-model="balanceForm.roadGrade"
              />
            </el-form-item>
            <el-form-item prop="levelParam">
              <template #label>
                <el-tooltip placement="top" effect="dark">
                  <img
                    src="../../../../../assets/images/空心问号.png"
                    alt=""
                    width="16px"
                    height="16px"
                    style="padding-right: 4px; margin-left: 3.6vw"
                  />
                  <template #content
                    >进行权重计算后,若<br />权值 ≥ 输入的定级参数值,<br />则该商户变为定点取货户</template
                  >
                </el-tooltip>
                <span class="calFont">定级参数</span>
              </template>
              <el-input
                class="calInput"
                @input="handleNumberInput('levelParam')"
                v-model="balanceForm.levelParam"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="calBtn">
          <el-button
            type="primary"
            style="color: #003766; height: 25px; width: 80px"
            @click="resetWeights()"
            v-op="'pickup:user:setWeights'"
            >重置权重</el-button
          >
          <el-button
            type="primary"
            style="
              color: #003766;
              margin-top: 5px;
              margin-left: 0;
              height: 25px;
              width: 80px;
            "
            v-op="'pickup:user:setWeights'"
            @click="Recalculate"
            >重新计算</el-button
          >
        </div>
      </div>
      <div class="section">
        <div class="searchContent">
          <div class="circle" ref="circle"></div>
          <div class="range">
            <el-select
              v-model="searchFromData.color"
              placeholder="全部"
              style="width: 140px"
              @change="getCircleColor"
            >
              <el-option label="定点取货户(已分配)" :value="4">
                <div class="shopType">
                  <div class="ci" style="background-color: #4e8a38"></div>
                  <span>定点取货户(已分配)</span>
                </div>
              </el-option>
              <el-option label="定点取货户(未分配)" :value="3">
                <div class="shopType">
                  <div class="ci" style="background-color: #8b843a"></div>
                  <span>定点取货户(未分配)</span>
                </div>
              </el-option>
              <el-option label="普通商户(已分配)" :value="2">
                <div class="shopType">
                  <div class="ci" style="background-color: #76469c"></div>
                  <span>普通商户(已分配)</span>
                </div>
              </el-option>
              <el-option label="普通商户(未分配)" :value="1">
                <div class="shopType">
                  <div
                    class="ci"
                    style="
                      background-color: transparent;
                      border: white 1.6px solid;
                    "
                  ></div>
                  <span>普通商户(未分配)</span>
                </div>
              </el-option>
              <el-option label="普通商户(待分配)" :value="5">
                <div class="shopType">
                  <div class="ci" style="background-color: #969398"></div>
                  <span>普通商户(待分配)</span>
                </div>
              </el-option>
              <el-option label="全部" :value="0">
                <div class="shopType">
                  <div class="ci" style="background-color: transparent"></div>
                  <span>全部</span>
                </div>
              </el-option>
            </el-select>
          </div>
          <div class="search">
            <el-input
              placeholder="请点击搜索"
              v-model="displayValue"
              @click="toggleSearchView"
            />
            <div class="searchView" v-if="searchView">
              <div class="content">
                <div class="group">
                  <div class="closeBold" @click="toggleSearchView">x</div>
                  <div class="flex-center">
                    <el-form
                      label-width="auto"
                      :model="searchFromData"
                      ref="searchModal"
                      class="searchForm"
                    >
                      <el-form-item label="档位" prop="gear">
                        <el-select
                          placeholder="请选择"
                          v-model="searchFromData.gear"
                        >
                          <el-option
                            v-for="item in downData.gears"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="客户编码" prop="customerCode">
                        <el-input
                          v-model="searchFromData.customerCode"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <el-form-item label="配送距离" prop="deliveryDistance">
                        <el-input
                          v-model="searchFromData.deliveryDistance"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <el-form-item label="取货柜类型" prop="type">
                        <el-select
                          placeholder="请选择"
                          v-model="searchFromData.type"
                        >
                          <el-option label="01" value="01" />
                          <el-option label="02" value="02" />
                          <el-option label="03" value="03" />
                          <el-option label="04" value="04" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="地址" prop="storeAddress">
                        <el-input
                          v-model="searchFromData.storeAddress"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <el-form-item label="取货柜地址" prop="pickupContainers">
                        <el-input
                          v-model="searchFromData.pickupContainers"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <el-form-item label="打卡点" prop=" accumulationName">
                        <el-input
                          v-model="searchFromData.accumulationName"
                          placeholder="请输入"
                        />
                      </el-form-item>
                    </el-form>
                  </div>

                  <div class="btns">
                    <el-button @click="resetSearch" type="primary"
                      >清空</el-button
                    >
                    <el-button @click="confirmSearch" type="primary"
                      >搜索</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="butContent">
          <el-button
            :icon="UploadFilled"
            type="primary"
            class="elb"
            @click="openTable"
            v-op="'pickup:user:importForm'"
            >导入表格</el-button
          >
          <el-button
            :icon="Memo"
            type="primary"
            class="elb"
            @click="openNote"
            v-op="'pickup:user:importLogs'"
            >导入日志</el-button
          >
          <el-button
            :icon="SetUp"
            type="primary"
            class="elb"
            @click="exportTable"
            v-op="'pickup:user:exportForm'"
            >导出表格</el-button
          >
          <el-button
            :icon="EditPen"
            type="primary"
            @click="changeInfo"
            class="elb"
            v-op="'pickup:user:update'"
            >修改信息</el-button
          >
          <el-button
            :icon="Refresh"
            type="primary"
            class="elb"
            @click="refreshWeights"
            v-op="'pickup:user:exportForm'"
            >全局刷新</el-button
          >
          <el-button
            :icon="Collection"
            type="primary"
            class="elb"
            @click="openConfirm"
            v-op="'pickup:user:toBeAssigned'"
            >待分配</el-button
          >
        </div>
      </div>
      <div class="table">
        <el-table
          v-loading="pickupStore.loading"
          v-if="tableData"
          :data="tableData"
          @row-click="handleRowClick"
          ref="tableRef"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{
            height: '4vh',
            'text-align': 'center',
          }"
          :row-class-name="tableRowClassName"
          size="small"
          :row-style="{ height: '3.9vh' }"
          style="font-size: 0.8vw; width: 100%"
          @selection-change="handleSelect"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="序号"
            type="index"
            :index="Aindex"
          ></el-table-column>
          <el-table-column prop="customerCode" label="客户编码">
            <template #default="scope">
              <div class="flex-center">
                <el-icon v-if="scope.row.locks == 1" :size="16">
                  <Lock />
                </el-icon>
                <p>
                  {{ scope.row.customerCode ? scope.row.customerCode : "无" }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="contactName"
            label="客户名称"
          ></el-table-column>

          <el-table-column
            prop="customerManagerName"
            label="负责人"
          ></el-table-column>
          <el-table-column prop="contactPhone" label="订货电话"
            ><template #default="scope">
              {{ scope.row.contactPhone ? scope.row.contactPhone : "无" }}
            </template></el-table-column
          >
          <el-table-column
            :show-overflow-tooltip="true"
            prop="storeAddress"
            min-width="100"
            label="地址"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="accumulationName"
            label="打卡点"
          ></el-table-column>
          <el-table-column prop="roadGrade" label="道路等级"
            ><template #default="scope">
              {{ scope.row.roadGrade === "0" ? "城区" : "乡镇" }}
            </template>
          </el-table-column>
          <el-table-column prop="gear" label="档位"></el-table-column>
          <el-table-column
            prop="deliveryDistance"
            label="配送距离(km)"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="pickupContainers"
            label="取货柜地址"
          >
            <template #default="scope">
              {{
                scope.row.pickupContainers ? scope.row.pickupContainers : "无"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="取货柜类型">
            <template #default="scope">
              {{ scope.row.type ? scope.row.type : "无" }}
            </template>
          </el-table-column>
          <el-table-column prop="weights" label="权值"></el-table-column>
        </el-table>
      </div>
    </div>
    <div
      class="mapSection"
      :style="{ height: dropDownButton ? '22vh' : '70vh' }"
    >
      <div class="mapSectionContent">
        <div class="map">
          <pickMap
            ref="pickUpMapRef"
            :list="points"
            :loading="isLoading"
          ></pickMap>
        </div>
        <div class="mapButton">
          <ElButton
            v-if="dropDownButton"
            type="primary"
            :icon="ArrowDownBold"
            @click="toggleDrop"
          ></ElButton>
          <ElButton
            v-else
            type="primary"
            :icon="ArrowUpBold"
            @click="toggleDrop"
          ></ElButton>
        </div>
      </div>
    </div>
    <div class="pageDivide">
      <el-pagination
        v-if="currentData"
        layout="prev, pager, next"
        :current-page="searchData.pageNum"
        :page-size="searchData.pageSize"
        :total="Number(currentData.total)"
        @current-change="handlePageChange"
      />
    </div>
    <ChangeMilk
      ref="changeModalRef"
      :data="change"
      @confirm-change="toChange"
    ></ChangeMilk>
    <uploadNote ref="uploadNoteRef"></uploadNote>
    <uploadTable ref="uploadTableRef"></uploadTable>
    <confirm ref="confirmRef" :list="list" @wait="wait"></confirm>
  </div>
</template>

<script setup lang="ts">
  import {
    ArrowDownBold,
    ArrowUpBold,
    Collection,
    EditPen,
    Lock,
    Memo,
    Refresh,
    SetUp,
    UploadFilled,
  } from "@element-plus/icons-vue";
  import pickMap from "../pickMap.vue";
  import ChangeMilk from "./modal/changeMilk.vue";
  import uploadTable from "./modal/uploadTable.vue";
  import uploadNote from "./modal/uploadNote.vue";
  import confirm from "./modal/confirm.vue";
  import { usePickStore } from "@/store/pick";
  const pickupStore = usePickStore();
  const uploadNoteRef = ref<any>();
  const uploadTableRef = ref<any>();
  const changeModalRef = ref<any>();
  const pickUpMapRef = ref<any>();
  const balanceRef = ref<any>();
  const confirmRef = ref<any>();
  const tableData = ref<any>([]);
  const circle = ref<any>();
  const currentData = ref<any>(null);
  const tableRef = ref<any>();
  const change = ref<any>();
  const list = ref<Number[]>([]);
  const dropDownButton = ref<boolean>(true);
  const searchData = reactive({
    pageNum: 1,
    pageSize: 12,
  });
  const points = ref<any>([]);
  const balanceForm = reactive<any>({
    gear: "",
    avgDistance: "",
    roadGrade: "",
    levelParam: "",
  });
  const isLoading = ref(true);
  // 计算显示在输入框中的值
  const displayValue = ref("");
  const searchView = ref<boolean>(false);
  const searchModal = ref<any>();
  const downData = ref<any>({});
  const searchButton = ref<boolean>(false);
  const searchFromData = reactive<any>({
    color: "",
    gear: "",
    customerCode: "",
    deliveryDistance: "",
    type: "",
    pickupContainers: "",
    storeAddress: "",
    pageNum: 1,
    pageSize: 12,
    accumulationName: "",
  });

  // 属性名称映射表（英文属性名 -> 中文名称）
  const propertyNameMap = {
    customerCode: "客户编码",
    deliveryDistance: "配送距离",
    gear: "档位",
    type: "取货柜类型",
    pickupContainers: "取货柜地址",
    storeAddress: "地址",
    color: "颜色",
  };

  // 自定义验证规则
  const validateNumber = (rule, value, callback) => {
    if (!/^-?\d+\.?\d*$/.test(value)) {
      // 验证数字和小数格式
      callback(new Error("必须为数字且可含小数点"));
    } else if (value.split(".").length > 2) {
      // 禁止多个小数点[2]()
      callback(new Error("小数格式错误"));
    } else if (value.split(".")[1]?.length > 2) {
      // 限制4位小数[1]()
      callback(new Error("最多保留2位小数"));
    } else {
      callback();
    }
  };
  const formRules = reactive({
    levelParam: [{ required: true, message: "", trigger: "blur" }],
    avgDistance: [
      { required: true, message: "", trigger: "blur" },
      { validator: validateNumber, trigger: "blur" },
    ],
    roadGrade: [
      { required: true, message: "", trigger: "blur" },
      { validator: validateNumber, trigger: "blur" },
    ],
    gear: [
      { required: true, message: "", trigger: "blur" },
      { validator: validateNumber, trigger: "blur" },
    ],
  });

  //处理分页
  function handleSelect(val: any) {
    change.value = val[0];
  }

  function getCircleColor(e: any) {
    searchFromData.pageNum = 1;
    searchData.pageNum = 1;
    searchButton.value = true;
    confirmSearch();
    if (e === 1) {
      circle.value.style = "background-color: transparent";
      return;
    } else if (e === 2) {
      circle.value.style = "background-color: rgb(118, 70, 156)";
      return;
    } else if (e === 3) {
      circle.value.style = "background-color: rgb(139, 132, 58)";
      return;
    } else if (e === 4) {
      circle.value.style = "background-color: rgb(72, 144, 76)";
      return;
    } else if (e === 5) {
      circle.value.style = "background-color: rgb(150, 147, 152)";
      return;
    } else {
      circle.value.style = "background-color: transparent";
      return;
    }
  }

  const handleRowClick = (row: any) => {
    if (!pickUpMapRef.value.getMap()) {
      return;
    }
    if (row.color == 0 || row.color == 1 || row.color == 5) {
      return;
    }
    // 执行地图定位
    pickUpMapRef.value
      .getMap()
      .setZoomAndCenter(16, [row.longitude, row.latitude]);
  };

  const tableRowClassName = ({ row }: any) => {
    if (row.color === 5) {
      return "gray";
    } else if (row.color === 2) {
      return "purple";
    } else if (row.color === 3) {
      return "yellow";
    } else if (row.color === 4) {
      return "green";
    }
  };
  // 输入过滤（自动去除非法字符）
  const handleNumberInput = (field: any) => {
    balanceForm[field] = balanceForm[field]
      .replace(/[^\d.]/g, "") // 去除非数字和小数点
      .replace(/\.{2,}/g, ".") // 限制多个小数点
      .replace(/(\..*)\./g, "$1"); // 禁止重复输入小数点
  };

  function handlePageChange(num: number = 1) {
    if (searchButton.value) {
      searchFromData.pageNum = num;
      confirmSearch();
      return;
    }
    searchData.pageNum = num;
    getData();
  }
  function toggleDrop() {
    dropDownButton.value = !dropDownButton.value;
  }

  async function getData() {
    getMapData();

    pickupStore.getUserList({ ...searchData }).then((res: any) => {
      currentData.value = res;
      searchData.pageNum = res.current;
      tableData.value = res.records;
    });
  }

  function getMapData() {
    try {
      // 异步获取数据
      isLoading.value = true;
      pickupStore.getMap().then((res: any) => {
        points.value = res.pickupUsers.map((item: any) => {
          const point: any = {
            lnglat: [item.longitude, item.latitude],
            info: {
              name: item.contactName || item.customerCode || "无",
              address: item.pickupContainers ? item.pickupContainers : "无",
              distance: item.deliveryDistance,
            },
          };
          if (item.color === 3) {
            point.type = "B";
          } else if (item.color === 4) {
            point.type = "C";
          } else if (item.color === 2) {
            point.type = "D";
          }
          if (item.pickupContainers) {
            point.info.dian = res.pickupLocationVos
              .filter((vos: any) => vos.pickupName === item.pickupContainers)
              .map((vos: any) => [vos.longitude, vos.latitude]);
          }
          return point;
        });
        points.value = [
          ...points.value,
          ...res.pickupLocationVos.map((item: any) => {
            if (item.status == 1) {
              return {
                lnglat: [item.longitude, item.latitude],
                type: "E",
                info: {
                  name: item.pickupName,
                },
              };
            } else if (item.status == 3) {
              return {
                lnglat: [item.longitude, item.latitude],
                type: "F",
                info: {
                  name: item.pickupName,
                  stores:
                    item.stores.length > 0
                      ? item.stores
                          .map(
                            (item: any) =>
                              item.contactName || item.customerCode || "无"
                          )
                          .join(",")
                      : "无",
                  posList: res.pickupUsers
                    .filter(
                      (user: any) => user.pickupContainers == item.pickupName
                    )
                    .map((user: any) => [user.longitude, user.latitude]),
                },
              };
            }
            return {
              lnglat: [item.longitude, item.latitude],
              type: "A",
              info: {
                name: item.pickupName,
                stores:
                  item.stores.length > 0
                    ? item.stores
                        .map(
                          (item: any) =>
                            item.contactName || item.customerCode || "无"
                        )
                        .join(",")
                    : "无",
                posList: res.pickupUsers
                  .filter(
                    (user: any) => user.pickupContainers == item.pickupName
                  )
                  .map((user: any) => [user.longitude, user.latitude]),
              },
            };
          }),
        ];
        isLoading.value = false;
      });
    } catch (error) {}
  }

  function toggleSearchView() {
    // 获取所有非空属性，并以分号分隔
    const nonEmptyProps = Object.entries(searchFromData)
      .filter(([key, value]) => {
        // 过滤掉空值和pageNum、pageSize属性
        return (
          value !== "" &&
          value !== null &&
          value !== undefined &&
          key !== "pageNum" &&
          key !== "pageSize"
        );
      })
      .map(([key, value]) => {
        // 使用中文属性名
        const displayName: any = propertyNameMap[key] || key;
        return `${displayName}:${value}`;
      })
      .join("; ");

    // 更新显示值
    displayValue.value = nonEmptyProps;
    pickupStore.getSelectList().then((res: any) => {
      downData.value = res;
    });
    searchView.value = !searchView.value;
  }
  function resetSearch() {
    searchButton.value = false;
    circle.value.style = "";
    resetProperties();
    searchFromData.color = "";
    searchFromData.pageNum = 1;
    searchData.pageNum = 1;
    getData();
  }
  // 重置属性值方法
  const resetProperties = () => {
    // 重置所有属性值为空字符串，但保留pageNum和pageSize
    Object.keys(searchFromData).forEach((key) => {
      if (key !== "pageNum" && key !== "pageSize") {
        searchFromData[key] = "";
      }
    });

    // 清空显示值
    displayValue.value = "";
  };

  function confirmSearch() {
    if (!searchFromData.color) {
      searchFromData.color = 0;
    }
    pickupStore.getUserList({ ...searchFromData }).then((res: any) => {
      currentData.value = res;
      if (currentData.value.records.length == 0) {
        ElMessage.error("没有查询到数据");
      }
      searchButton.value = true;
      searchData.pageNum = res.current;
      tableData.value = res.records;
    });
  }

  async function changeInfo() {
    if (tableRef.value?.getSelectionRows().length > 1) {
      ElMessage.error("只能选择一条");
      return;
    } else if (tableRef.value?.getSelectionRows().length == 0) {
      ElMessage.error("没有选择");
      return;
    }

    // 获取选中行的ID
    const selectedRow = tableRef.value.getSelectionRows()[0];
    const pickupUserId = selectedRow.id;
    pickupStore.getRouteao(pickupUserId).then((res: any) => {
      if (res.code === 200) {
        change.value = {
          ...selectedRow,
          routeName: res.data.routeName,
        };
      }
    });
    changeModalRef.value.MilkOpen = true;
  }
  function toChange(e: any) {
    const { locks, deliveryDistance, pickupContainers, type } = e;
    // console.log(e);
    pickupStore
      .updateUser({
        locks: Number(locks),
        deliveryDistance: Number(deliveryDistance),
        pickupContainers,
        type,
        id: tableRef.value.getSelectionRows()[0].id,
      })
      .then((res: any) => {
        if (res.status == 200 || res.code == 200) {
          ElMessage.success(res.msg);
          if (searchButton.value) {
            confirmSearch();
            return;
          }
          getData();
        } else {
          ElMessage.error("修改失败");
        }
      });
  }
  function wait(e: any) {
    pickupStore.beAssigned({ ids: e }).then((res: any) => {
      console.log(res);

      if (res.code === 200) {
        ElMessage.success(res.msg);
        getData();
      } else {
        ElMessage.error("分配异常");
      }
    });
  }
  function openNote() {
    nextTick(() => {
      uploadNoteRef.value.onOpenDialog();
      uploadNoteRef.value.noteVisible = true;
    });
  }
  function openTable() {
    uploadTableRef.value.uploadVisible = true;
  }
  function openConfirm() {
    if (tableRef.value.getSelectionRows().length <= 0) {
      ElMessage.error("没有选择");
      return;
    }
    list.value = tableRef.value.getSelectionRows().map((item: any) => item.id);
    confirmRef.value.confirmDialogVis = true;
  }
  function refreshWeights() {
    window.location.reload();
    ElMessage.success("刷新成功");
  }
  function exportTable() {
    //导出表格
    pickupStore.exportUser().then((res: any) => {
      const url = window.URL.createObjectURL(res);
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "商户信息表.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
  function Recalculate() {
    const { gear, avgDistance, roadGrade, levelParam } = balanceForm;
    if (Number(gear) + Number(avgDistance) + Number(roadGrade) != 1) {
      resetWeights();
      ElMessage.error("前3个相加不为1");
      return;
    }
    balanceRef.value.validate((valid: any, fields: any) => {
      if (valid) {
        pickupStore
          .calculateUser({
            gear: Number(gear),
            avgDistance: Number(avgDistance),
            roadGrade: Number(roadGrade),
            levelParam: Number(levelParam),
          })
          .then((res: any) => {
            searchData.pageNum = 1;
            getData();
            ElMessage.success(res.msg);
          });
      } else {
        resetWeights();
        ElMessage.error("表单验证未通过");
      }
    });
  }
  function resetWeights() {
    balanceRef.value.resetFields();
  }
  onMounted(() => {
    pickupStore.getParams().then((res: any) => {
      const { gear, avgDistance, roadGrade, levelParam } = res;
      balanceForm.gear = `${gear}`;
      balanceForm.avgDistance = `${avgDistance}`;
      balanceForm.roadGrade = `${roadGrade}`;
      balanceForm.levelParam = levelParam;
    });
    getData();
  });

  function Aindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="less" scoped>
  .shopType {
    display: flex;
    align-items: center;
    color: white;
    font-size: 16px;
    .ci {
      width: 17px;
      height: 17px;
      border-radius: 50%;
      margin-right: 5px;
    }
  }

  .flex-center {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .pickup {
    :deep(.el-table__row) {
      &.green {
        background-color: rgba(78, 138, 56, 0.8);
      }
      &.yellow {
        background-color: rgba(139, 132, 58, 0.8);
      }
      &.gray {
        background-color: rgba(150, 147, 152, 0.8);
      }
      &.purple {
        background-color: rgba(118, 70, 156, 0.8);
      }
    }
    :deep(.el-table-column--selection) {
      background-color: #00294a;
    }
    position: relative;
    width: 100%;
    height: 86vh;

    :deep(.el-icon) {
      font-size: 16px;
    }
    .el-button {
      background-color: #97c7e7;
      color: #003766;
      font-size: 14px;
    }

    .dataSection {
      width: 52%;
      height: 80vh;
      position: absolute;
      left: 0;
      top: 0;
      .calculate {
        box-sizing: border-box;
        width: 100%;
        height: 10vh;
        display: flex;
        background-color: #011733;
        border: #fff 2px solid;

        .calData {
          overflow: hidden;
          .calFont {
            font-size: 16px;
          }
          .calInput {
            width: 7vw;
            height: 60%;
          }
          width: max-content;
          height: 100%;
          display: flex;
          .calForm {
            padding-left: 1vw;
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            :deep(.el-form-item__label:before) {
              display: none;
            }
            :deep(.el-form-item__label) {
              align-items: center;
            }
            :global(.el-popper.is-dark) {
              /* Set padding to ensure the height is 32px */
              padding: 10px 12px !important;
              background: #011733;
              border: 1px #7ef4ff solid;
            }
          }
          .el-form-item {
            margin: 0;
          }
        }

        .calBtn {
          box-sizing: border-box;
          width: 30%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }
      }

      .section {
        .searchContent {
          margin-top: 1vh;
          width: 100%;
          height: 5vh;
          display: flex;
          align-items: center;
          .circle {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            border: white 1.6px solid;
          }
          .range {
            margin-left: 1vw;
            :deep(.el-input__inner) {
              font-size: 12px;
            }
            :deep(.el-select) {
              --el-input-bg-color: transparent !important;
              background-color: transparent !important;
            }
            :deep(.el-input) {
              --el-input-bg-color: transparent !important;
              background-color: transparent !important;
            }
          }
          .search {
            flex: 1;
            margin-left: 1vw;
            position: relative;

            .searchView {
              :deep(.el-form-item__label) {
                font-size: 14px;
              }
              :deep(.el-select) {
                width: 6vw;
                background-color: transparent !important;
                height: 3.6vh;
              }
              :deep(.el-input) {
                --el-input-bg-color: transparent !important;
                background-color: transparent !important;
                width: 6vw;
                height: 3.6vh;
              }

              :deep(.el-form-item) {
                margin-bottom: 4px;
              }
              position: absolute;
              background-color: #000032;
              border: 1px solid #fff;
              z-index: 999;
              top: 5vh;
              .btns {
                display: flex;
                justify-content: center;
                margin-bottom: 12px;
              }
              .content {
                width: 100%;
                display: flex;
                justify-content: center;
              }
              .group {
                .searchForm {
                  display: flex;
                  flex-wrap: wrap;

                  max-width: 80%; // 控制表单最大宽度
                  padding-top: 6vh;
                  padding-bottom: 1vh;
                  :deep(.el-form-item) {
                    margin-right: 20px; // 调整表单项间距
                    // &:last-child {
                    //   flex-basis: 100%; // 移动端单列
                    //   margin: 0;
                    // }
                  }
                  :deep(.el-form-item__content),
                  :deep(.el-form-item__label) {
                    width: 6vw; // 统一标签和内容宽度
                  }
                }
                position: relative;
                .closeBold {
                  position: absolute;
                  cursor: pointer;
                  text-align: center;
                  width: 40px;
                  height: 36px;
                  right: 0px;
                  top: 0px;
                  background-color: #97c7e7;
                  font-size: 30px;
                  color: black;
                }
              }
            }
          }
        }
        .butContent {
          margin-top: 2vh;
          width: max-content;
          height: 4vh;
          display: flex;
          justify-content: space-around;
        }
        .elb {
          width: 6.6vw;
          font-size: 12px;
        }
        width: 100%;
      }
      .table {
        width: 88vw;
        position: absolute;
        top: 24.4vh;
      }
    }

    .mapSection {
      width: 45.5%;
      height: 22vh;
      overflow: hidden;
      position: absolute;
      z-index: 999;
      right: 1%;
      top: 0;
      border: #6e91c4 4px solid;

      .mapSectionContent {
        width: 100%;
        height: 100%;
        position: relative;
        .map {
          position: relative;
          width: 100%;
          height: 70vh;
        }
      }
      .mapButton {
        width: 100%;
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: -8px;
        :deep(.el-button) {
          background-color: transparent;
          color: #76d2ee;
          border: 0 !important;
        }
        .el-button:hover {
          border: 0 !important;
        }
        :deep(.el-button.active) {
          border: 0 !important;
        }
        :deep(.el-icon) {
          font-size: 28px;
        }
      }
    }

    .pageDivide {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      :deep(.el-pagination) {
        margin-bottom: 0;
      }
    }
  }
</style>
