# 简化版Travel Time验证测试使用说明

## 问题解决
由于原版测试类遇到MyBatis版本兼容性问题，我创建了一个简化版本 `SimpleTravelTimeValidationTest`，它：
- ✅ 不依赖Spring Boot上下文
- ✅ 直接使用JDBC连接数据库
- ✅ 避免了MyBatis版本冲突
- ✅ 功能完全相同

## 使用前配置

### 1. 配置数据库连接
在 `SimpleTravelTimeValidationTest.java` 中修改数据库连接信息：

```java
// 数据库连接配置 - 请根据实际情况修改
private static final String DB_URL = "****************************************************************************************************";
private static final String DB_USERNAME = "root";
private static final String DB_PASSWORD = "aA13717028793#";
```

### 2. 配置高德API Key
```java
private static final String AMAP_API_KEY = "your_actual_amap_api_key_here";
```

### 3. 调整测试参数（可选）
```java
private static final int SAMPLE_SIZE = 20;           // 抽样数量，建议20条以内
private static final int REQUEST_DELAY_MS = 300;     // 请求间隔300ms
private static final double TOLERANCE_PERCENTAGE = 20.0; // 允许误差20%
```

## 运行测试

### 方法1：IDEA运行
1. 打开 `SimpleTravelTimeValidationTest.java`
2. 右键点击 `testTravelTimeAccuracy()` 方法
3. 选择 "Run 'testTravelTimeAccuracy()'"

### 方法2：Maven命令
```bash
mvn test -Dtest=SimpleTravelTimeValidationTest#testTravelTimeAccuracy
```

## 高德API申请步骤

### 1. 注册高德开发者账号
- 访问：https://lbs.amap.com/
- 点击"注册"创建开发者账号

### 2. 创建应用
- 登录后进入控制台
- 点击"创建新应用"
- 应用类型选择：Web服务
- 服务平台选择：Web端

### 3. 获取API Key
- 在应用管理中找到你的应用
- 复制"Key"字段的值
- 粘贴到测试类的 `AMAP_API_KEY` 常量中

### 4. 配额说明
- **个人开发者**：每日5000次免费调用
- **企业开发者**：根据套餐不同
- **建议**：测试控制在20-50次以内

## 测试流程

### 1. 数据抽样
- 从travel_time表中随机抽取有效数据
- 自动过滤异常坐标（0,0或超出中国范围）
- 确保样本数据质量

### 2. API对比
- 调用高德路径规划API获取实际行驶时间
- 与OSRM数据进行对比
- 计算误差百分比

### 3. 结果评估
- **通过**：误差≤20%
- **失败**：误差>20%
- **错误**：API调用失败

### 4. 生成报告
- 文件位置：`target/test-results/simple_travel_time_validation_yyyyMMdd_HHmmss.txt`
- 包含详细的对比结果和统计分析

## 示例输出

### 控制台日志
```
🧪 开始Travel Time数据准确性验证测试（简化版）
📊 配置信息: 抽样20条, 请求间隔300ms, 允许误差20.0%
🎯 开始从数据库抽取样本数据...
📊 数据库有效记录数: 2873030
✅ 成功抽取20条有效样本
🔍 验证第1/20条: (114.124922,23.996005) -> (114.120966,23.999628)
✅ 验证通过: OSRM=15.2分钟, 高德=14.8分钟, 误差=2.7%
...
📊 验证结果摘要: 总数20, 通过17, 失败2, 错误1, 通过率85.0%
```

### 报告文件示例
```
Travel Time数据验证报告（简化版）
==================================================
生成时间: 2025-08-18T01:30:00
抽样数量: 20
验证通过: 17
验证失败: 2
请求错误: 1
通过率: 85.0%
允许误差: 20.0%

详细验证结果:
序号	OSRM时间	高德时间	误差%	状态	起点坐标	终点坐标
1	15.2	14.8	2.7	通过	114.124922,23.996005	114.120966,23.999628
2	45.6	52.3	14.7	通过	113.596766,24.810403	113.598123,24.812456
...

统计摘要:
平均误差: 12.3%
最大误差: 28.5%
最小误差: 1.1%

结论:
✅ OSRM数据质量良好，通过率85.0%
```

## 质量评估标准

### 通过率判断
- **≥80%**：数据质量良好 ✅
- **60-80%**：数据质量一般，建议检查 ⚠️
- **<60%**：数据质量较差，建议重新生成 ❌

### 常见误差原因
1. **算法差异**：OSRM与高德使用不同的路径规划算法
2. **实时路况**：高德考虑实时路况，OSRM使用静态数据
3. **地图版本**：底层地图数据版本不同
4. **计算模型**：时间估算的数学模型差异

## 故障排除

### 1. 数据库连接失败
```
❌ 数据库查询失败
```
**解决方案**：
- 检查数据库连接参数是否正确
- 确认数据库服务是否启动
- 验证用户名密码是否正确

### 2. API调用失败
```
❌ 调用高德API失败: xxx
```
**解决方案**：
- 检查API Key是否正确配置
- 确认网络连接是否正常
- 验证API配额是否充足

### 3. 没有获取到样本数据
```
⚠️ 没有获取到有效样本数据
```
**解决方案**：
- 检查travel_time表是否存在数据
- 确认数据格式是否正确（坐标不为0）

### 4. 编译错误
**解决方案**：
```bash
mvn clean compile test-compile
```

## 注意事项

### 1. API配额管理
- 个人开发者每日5000次免费
- 建议测试控制在50次以内
- 可以分多次运行测试

### 2. 请求频率控制
- 默认300ms间隔，避免被限流
- 如果遇到限流，可以增加间隔时间

### 3. 结果分析
- 关注系统性偏差而非个别异常
- 结合实际业务场景评估数据可用性
- 20%误差在合理范围内

### 4. 网络要求
- 确保能够访问高德API服务
- 网络连接稳定

## 优势对比

| 特性 | 原版测试类 | 简化版测试类 |
|------|------------|--------------|
| Spring依赖 | 需要完整Spring上下文 | 无需Spring上下文 |
| MyBatis依赖 | 依赖MyBatis Plus | 直接使用JDBC |
| 兼容性 | 可能有版本冲突 | 兼容性好 |
| 运行速度 | 较慢（需要启动Spring） | 较快 |
| 功能完整性 | 完全相同 | 完全相同 |
| 配置复杂度 | 较复杂 | 简单 |

## 总结

简化版测试类解决了MyBatis版本兼容性问题，提供了相同的功能：
- ✅ 随机抽样验证
- ✅ 高德API对比
- ✅ 详细报告生成
- ✅ 质量评估分析

现在你可以：
1. 配置数据库连接和API Key
2. 直接运行测试
3. 获得OSRM数据质量评估结果

**建议先用小样本（5-10条）测试连通性，确认无误后再进行完整测试。**
