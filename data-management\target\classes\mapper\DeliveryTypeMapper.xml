<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.DeliveryTypeMapper">

    <select id="selectIdByName" resultType="com.ict.datamanagement.domain.entity.DeliveryType">
        select * from delivery_type where delivery_name=#{name}
    </select>
    <select id="selectById" resultType="com.ict.datamanagement.domain.entity.DeliveryType">
        select * from delivery_type where delivery_type_id=#{deliverId}
    </select>
</mapper>