export interface IDeliveryGetList {
    pageNum: number;
    pageSize: number;
}

export interface IDeliveryData {
    deliveryId: number,
    deliverName: string,
    teamName: string,
    deliveryAreaType: string,
    carNumber: number,
    transitDepotName: string
    routeNumber: number
}

export interface IDeliveryAddList {
    areaName: string,
    carNumber: number,
    deliveryName: string,
    deliveryType: string,
    routeNumber: number,
    teamName: string,
    transitDepotName: string;
}

export interface IDeliveryDeleteList {
    id: number
}

export interface IDeliveryUpdateList {
    areaName: string,
    carNumber: number,
    deliveryId: number,
    deliveryName: string,
    deliveryType: string,
    routeNumber: number,
    teamName: string,
    transitDepotName: string;
}