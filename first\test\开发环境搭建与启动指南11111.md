# 粤北卷烟物流管理平台 - 开发环境搭建与启动指南

## 📋 目录

1. [环境准备](#环境准备)
2. [数据库配置](#数据库配置)
3. [Nacos配置中心搭建](#nacos配置中心搭建)
4. [后端微服务环境搭建](#后端微服务环境搭建)
5. [前端环境搭建](#前端环境搭建)
6. [启动流程](#启动流程)
7. [配置文件详解](#配置文件详解)
8. [常见问题解决](#常见问题解决)

---

## 🛠️ 环境准备

### 必需软件环境

| 软件 | 版本要求 | 说明 |
|-----|---------|------|
| **Java** | JDK 1.8+ | 后端微服务运行环境 |
| **MySQL** | 5.7+ | 数据库 |
| **Node.js** | 14.0+ | 前端开发环境 |
| **Maven** | 3.6+ | Java项目构建工具 |
| **Nacos** | 2.0+ | 配置中心和服务发现 |
| **Redis** | 6.0+ | 缓存中间件 |
| **IDE** | IntelliJ IDEA / VSCode | 开发工具 |

### 开发工具推荐

- **后端开发**: IntelliJ IDEA Ultimate
- **前端开发**: VSCode + Vue 3 插件
- **数据库管理**: Navicat / DBeaver
- **API测试**: Postman / Apifox

---

## 🗄️ 数据库配置

### 数据库架构设计逻辑

本项目采用**主从数据库架构**，设计思路如下：

1. **主从分离的原因**：
   - **读写分离**：主库负责写操作，从库负责读操作，提高系统性能
   - **数据备份**：从库作为主库的备份，提高数据安全性
   - **负载均衡**：分散数据库压力，支持高并发访问

2. **数据库分布策略**：
   - `ycdb` (主库)：存储所有核心业务数据
   - `ycwl_slave1-3` (从库)：用于读操作的负载均衡

3. **数据一致性保证**：
   - 使用MySQL的主从复制机制
   - 写操作统一走主库，读操作分散到从库
   - 通过动态数据源路由实现读写分离

### 1. MySQL 安装与配置

#### 安装 MySQL 5.7+
```bash
# Windows: 下载MySQL安装包
# macOS: 使用Homebrew
brew install mysql

# Linux: 使用包管理器
sudo apt-get install mysql-server
```

#### 配置 MySQL
```sql
-- 创建数据库
CREATE DATABASE ycdb CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 创建从库（可选，用于读写分离）
CREATE DATABASE ycwl_slave1 CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE ycwl_slave2 CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE ycwl_slave3 CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 创建用户并授权
CREATE USER 'root'@'%' IDENTIFIED BY '123';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
```

#### 导入数据
```bash
# 导入项目数据库文件
mysql -u root -p123 ycdb < ycdb(1).sql
```

### 2. 数据库连接配置

项目使用Druid连接池，支持主从数据库配置：

```yaml
# shared-jdbc.yaml 配置说明
spring:
  datasource:
    druid:
      master:
        url: ********************************
        username: root
        password: 123
      slave1:
        url: ***************************************
        username: root
        password: 123
      # 连接池配置
      initial-size: 15      # 初始连接数
      min-idle: 15          # 最小空闲连接数
      max-active: 200       # 最大连接数
      max-wait: 60000       # 获取连接等待超时时间
```

---

## 🎯 Nacos配置中心搭建

### 配置中心架构设计逻辑

本项目采用**Nacos作为配置中心和服务注册中心**，设计思路如下：

1. **配置中心的作用**：
   - **统一配置管理**：所有微服务的配置集中管理，避免配置文件分散
   - **动态配置更新**：支持配置热更新，无需重启服务
   - **环境隔离**：通过Group和Namespace实现不同环境配置隔离
   - **配置版本控制**：支持配置历史版本管理和回滚

2. **服务注册发现机制**：
   - **自动服务注册**：微服务启动时自动注册到Nacos
   - **健康检查**：实时监控服务状态，自动剔除不健康服务
   - **负载均衡**：配合Spring Cloud LoadBalancer实现服务调用负载均衡

3. **配置文件组织结构**：
   - `DEFAULT_GROUP`：默认组，存放所有微服务配置
   - `shared-jdbc.yaml`：共享数据库配置，被多个服务引用
   - 各服务独立配置文件：避免配置冲突，便于维护

4. **为什么选择Nacos**：
   - 阿里巴巴开源，社区活跃，文档完善
   - 同时支持配置中心和服务注册中心
   - 提供可视化管理界面
   - 与Spring Cloud深度集成

### 1. Nacos 安装

#### 下载 Nacos
```bash
# 下载 Nacos 2.0+
wget https://github.com/alibaba/nacos/releases/download/2.0.3/nacos-server-2.0.3.tar.gz
tar -xzf nacos-server-2.0.3.tar.gz
cd nacos/bin
```

#### 配置 Nacos
```bash
# 修改 conf/application.properties
server.port=8848
nacos.core.auth.enabled=false
spring.datasource.platform=mysql
db.num=1
db.url.0=*************************************************************************************************************************************************************************
db.user.0=root
db.password.0=123
```

#### 启动 Nacos
```bash
# 单机模式启动
  startup.cmd -m standalone
# 访问控制台
http://localhost:8848/nacos
# 默认用户名/密码: nacos/nacos
```

### 2. 配置文件上传

将 `DEFAULT_GROUP` 目录下的配置文件上传到 Nacos：

#### 通过 Nacos 控制台上传
1. 访问 `http://localhost:8848/nacos`
2. 进入 "配置管理" -> "配置列表"
3. 点击 "+" 创建配置
4. 分别创建以下配置：

| Data ID | Group | 格式 | 说明 |
|---------|--------|------|------|
| `gateway.yaml` | DEFAULT_GROUP | YAML | 网关服务配置 |
| `userservice.yaml` | DEFAULT_GROUP | YAML | 用户服务配置 |
| `guestbook.yaml` | DEFAULT_GROUP | YAML | 留言板服务配置 |
| `clustercalculate.yaml` | DEFAULT_GROUP | YAML | 聚集区计算服务配置 |
| `pathcalculate.yaml` | DEFAULT_GROUP | YAML | 路径计算服务配置 |
| `datamanagement.yaml` | DEFAULT_GROUP | YAML | 数据管理服务配置 |
| `pickup.yaml` | DEFAULT_GROUP | YAML | 定点取货服务配置 |
| `shared-jdbc.yaml` | DEFAULT_GROUP | YAML | 共享数据库配置 |

---

## ⚙️ 后端微服务环境搭建

### 微服务架构设计逻辑

本项目采用**Spring Cloud微服务架构**，设计思路如下：

1. **微服务拆分原则**：
   - **业务边界清晰**：按业务功能拆分，每个服务职责单一
   - **数据隔离**：每个服务管理自己的数据，避免数据耦合
   - **独立部署**：每个服务可以独立开发、测试、部署
   - **技术栈灵活**：不同服务可以选择不同技术栈

2. **服务划分逻辑**：
   ```
   Gateway (8080)           # 统一入口，路由转发
   ├── User-Service (8081)  # 用户认证授权，核心基础服务
   ├── Data-Management (8085) # 数据管理，业务核心服务
   ├── Guestbook (8082)     # 客户反馈，独立业务模块
   ├── Cluster-Calculate (8083) # 聚集区计算，算法服务
   ├── Path-Calculate (8084)    # 路径计算，算法服务
   └── Pickup (8086)        # 定点取货，业务扩展服务
   ```

3. **服务间通信机制**：
   - **同步调用**：使用Feign进行服务间REST调用
   - **负载均衡**：Spring Cloud LoadBalancer自动负载均衡
   - **熔断降级**：集成Hystrix实现服务容错
   - **链路追踪**：可集成Sleuth进行分布式链路追踪

4. **数据访问层设计**：
   - **MyBatis-Plus**：简化数据库操作，提供通用CRUD
   - **动态数据源**：支持读写分离，提高性能
   - **连接池管理**：使用Druid连接池，支持监控和统计

5. **服务治理策略**：
   - **配置中心**：Nacos统一配置管理
   - **服务发现**：自动服务注册与发现
   - **健康检查**：实时监控服务状态
   - **优雅关闭**：支持服务平滑停机

### 1. 项目导入

#### 使用 IntelliJ IDEA
1. 打开 IntelliJ IDEA
2. 选择 "Open or Import"
3. 选择 `ycwl-ms-v3.0` 目录
4. 选择 "Import project from external model" -> "Maven"
5. 点击 "Next" 直到完成导入

#### Maven 依赖下载
```bash
# 在项目根目录执行
mvn clean install -DskipTests
```

### 2. 环境变量配置

#### 创建文件目录
```bash
# Windows 环境
mkdir E:\www\wwwroot\ycwl\ycwl-ms\data-management\data\Download
mkdir E:\www\wwwroot\ycwl\ycwl-ms\data-management\data\uploadFile
mkdir E:\www\wwwroot\ycwl\ycwl-ms\data-management\data\nullFrom
mkdir E:\www\wwwroot\ycwl\resource\file
mkdir E:\www\wwwroot\ycwl\db

# Linux/macOS 环境
mkdir -p /www/wwwroot/ycwl/ycwl-ms/data-management/data/{Download,uploadFile,nullFrom}
mkdir -p /www/wwwroot/ycwl/resource/file
mkdir -p /www/wwwroot/ycwl/db
```

#### 创建数据源配置文件
```bash
# 创建 E:\www\wwwroot\ycwl\masterDatasource.txt
echo "master" > E:\www\wwwroot\ycwl\masterDatasource.txt
```

### 3. 修改配置文件

根据本地环境修改 Nacos 配置中的以下内容：

#### 数据库地址
```yaml
# 将配置文件中的数据库地址修改为本地地址
url: ********************************?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8
```

#### Nacos 地址
```yaml
# 确保 Nacos 地址正确
spring:
  cloud:
    nacos:
      server-addr: localhost:8848
```

#### 文件路径
```yaml
# 根据操作系统修改文件路径
# Windows
file:
  DOWNLOAD_PATH: E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/Download/
  UPLOAD_PATH: E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/uploadFile/

# Linux/macOS
file:
  DOWNLOAD_PATH: /www/wwwroot/ycwl/ycwl-ms/data-management/data/Download/
  UPLOAD_PATH: /www/wwwroot/ycwl/ycwl-ms/data-management/data/uploadFile/
```

---

## 🖥️ 前端环境搭建

### 前端架构设计逻辑

本项目采用**Vue 3 + TypeScript**现代前端架构，设计思路如下：

1. **技术栈选择原因**：
   - **Vue 3**：组合式API，更好的TypeScript支持，性能提升
   - **TypeScript**：类型安全，提高代码质量和开发效率
   - **Vite**：快速构建工具，支持热更新和ES模块
   - **Element Plus**：企业级UI组件库，符合管理系统设计规范

2. **前端架构分层**：
   ```
   展示层 (Pages)          # 页面组件，业务逻辑
   ├── 组件层 (Components)  # 通用组件，可复用
   ├── 状态管理 (Store)     # Pinia状态管理
   ├── 服务层 (Service)     # API调用，数据处理
   ├── 路由层 (Router)      # 路由配置，权限控制
   └── 工具层 (Utils)       # 工具函数，通用方法
   ```

3. **状态管理策略**：
   - **Pinia**：Vue 3官方推荐状态管理库
   - **模块化设计**：按业务功能拆分Store
   - **响应式数据**：自动追踪数据变化
   - **持久化存储**：关键数据本地存储

4. **组件设计原则**：
   - **单一职责**：每个组件只负责一个功能
   - **可复用性**：通用组件抽象，提高复用率
   - **组合式API**：使用Composition API组织逻辑
   - **TypeScript类型**：完整的类型定义

5. **API调用架构**：
   - **Axios封装**：统一请求拦截、响应处理
   - **模块化API**：按服务拆分API调用
   - **错误处理**：统一错误处理机制
   - **Loading状态**：请求状态管理

6. **权限控制机制**：
   - **路由守卫**：页面级权限控制
   - **按钮权限**：操作级权限控制
   - **菜单权限**：动态菜单生成
   - **Token管理**：JWT令牌自动刷新

### 1. Node.js 安装

#### 安装 Node.js
```bash
# 官网下载: https://nodejs.org/
# 或使用 nvm 管理版本
nvm install 16.0.0
nvm use 16.0.0
```

#### 验证安装
```bash
node --version  # 应显示 v16.0.0+
npm --version   # 应显示 8.0.0+
```

### 2. 项目依赖安装

```bash
# 进入前端项目目录
cd test

# 安装依赖
npm install

# 或使用 yarn
yarn install

# 如果网络慢，可以使用淘宝镜像
npm config set registry https://registry.npm.taobao.org
npm install
```

### 3. 环境配置

#### 创建环境配置文件
```javascript
// test/.env.development
VITE_APP_BASE_API = 'http://localhost:8080'
VITE_APP_MAP_KEY = 'your-amap-key'
```

#### 修改 API 地址
```typescript
// test/src/service/config/index.ts
export const BASE_URL = 'http://localhost:8080'
```

---

## 🚀 启动流程

### 启动流程设计逻辑

本项目采用**分层启动策略**，确保系统稳定运行：

1. **启动顺序的重要性**：
   - **依赖关系**：后启动的服务依赖先启动的服务
   - **服务发现**：微服务需要注册到Nacos才能被发现
   - **数据库连接**：所有服务都需要数据库支持
   - **网关路由**：网关需要知道后端服务地址

2. **三层启动架构**：
   ```
   基础设施层 (Infrastructure)
   ├── MySQL数据库          # 数据存储
   ├── Nacos配置中心        # 服务注册发现
   └── Redis缓存           # 缓存支持
   
   核心服务层 (Core Services)
   ├── Gateway网关         # 统一入口
   ├── User-Service        # 用户认证
   └── Data-Management     # 数据管理
   
   业务服务层 (Business Services)
   ├── Guestbook          # 客户反馈
   ├── Cluster-Calculate   # 聚集区计算
   ├── Path-Calculate      # 路径计算
   └── Pickup             # 定点取货
   ```

3. **启动检查机制**：
   - **健康检查**：每个服务启动后进行健康检查
   - **依赖验证**：确认依赖服务可用
   - **配置验证**：验证配置文件正确性
   - **数据库连接**：测试数据库连接

4. **服务启动顺序原理**：
   - **Gateway优先**：作为流量入口，需要最先启动
   - **User-Service次之**：提供认证服务，其他服务可能需要
   - **Data-Management重要**：核心数据服务
   - **算法服务最后**：计算服务通常是独立的

### 1. 基础服务启动顺序

#### 第一步：启动基础服务
```bash
# 1. 启动 MySQL 数据库
# 2. 启动 Nacos 配置中心
cd nacos/bin
  startup.cmd -m standalone

# 3. 启动 Redis (可选)
redis-server
```

#### 第二步：验证基础服务
```bash
# 验证 Nacos 启动成功
curl http://localhost:8848/nacos

# 验证 MySQL 连接
mysql -u root -p123 -e "SHOW DATABASES;"
```

### 2. 后端服务启动顺序

#### 推荐启动顺序
1. **Gateway** (8080) - 网关服务
2. **User-Service** (8081) - 用户服务
3. **Data-Management** (8085) - 数据管理服务
4. **Guestbook** (8082) - 留言板服务
5. **Cluster-Calculate** (8083) - 聚集区计算服务
6. **Path-Calculate** (8084) - 路径计算服务
7. **Pickup** (8086) - 定点取货服务

#### 启动方式一：IDE 启动
```java
// 在 IntelliJ IDEA 中
// 1. 找到各服务的 Application 类
// 2. 右键 -> Run 'XxxApplication'

// 主要启动类：
GatewayApplication.java           // 网关服务
UserServiceApplication.java      // 用户服务
DataManagementApplication.java   // 数据管理服务
GuestbookApplication.java        // 留言板服务
ClusterCalculateApplication.java // 聚集区计算服务
PathCalculateApplication.java    // 路径计算服务
PickupServiceApplication.java    // 定点取货服务
```

#### 启动方式二：命令行启动
```bash
# 进入各服务目录，执行：
mvn spring-boot:run

# 或者打包后启动
mvn clean package -DskipTests
java -jar target/service-name.jar
```

### 3. 前端服务启动

```bash
# 进入前端项目目录
cd test

# 启动开发服务器
npx vite --mode prd

# 或
npm run homeDev

# 访问地址
http://localhost:3000
```

### 4. 启动验证

#### 验证后端服务
```bash
# 检查 Nacos 服务列表
curl http://localhost:8848/nacos/v1/ns/catalog/services

# 检查网关服务
curl http://localhost:8080/actuator/health

# 检查具体服务
curl http://localhost:8080/userservice/actuator/health
```

#### 验证前端服务
- 打开浏览器访问 `http://localhost:3000`
- 进入登录页面，验证界面显示正常

---

## 📝 配置文件详解

### 微服务功能逻辑详解

在详细解析配置文件之前，先了解各个微服务的业务逻辑：

#### 1. Gateway (网关服务) - 系统入口
**业务逻辑**：
- **统一入口**：所有客户端请求的唯一入口点
- **路由转发**：根据请求路径将请求转发到对应微服务
- **负载均衡**：在多个服务实例之间分发请求
- **跨域处理**：解决前端跨域问题
- **请求过滤**：可以添加认证、限流等过滤器

**技术实现**：
- 使用Spring Cloud Gateway的响应式编程模型
- 通过Nacos服务发现自动更新路由表
- 支持动态路由配置

#### 2. User-Service (用户服务) - 认证中心
**业务逻辑**：
- **用户认证**：登录验证，生成JWT令牌
- **权限管理**：角色和权限的分配与验证
- **用户管理**：用户信息的CRUD操作
- **组织管理**：部门、班组的管理

**技术实现**：
- JWT令牌认证机制
- 基于RBAC的权限控制
- 密码加密存储
- 验证码生成与验证

#### 3. Data-Management (数据管理服务) - 业务核心
**业务逻辑**：
- **商铺管理**：商铺信息的维护、查询、统计
- **车辆管理**：车辆信息和工作实情管理
- **区域管理**：配送区域、中转站管理
- **数据导入导出**：Excel文件的批量处理

**技术实现**：
- 使用MyBatis-Plus简化数据库操作
- 支持文件上传下载
- 数据校验和异常处理
- 定时任务处理

#### 4. Guestbook (留言板服务) - 客户反馈
**业务逻辑**：
- **反馈收集**：物流异常、营销问题反馈
- **问题处理**：反馈的分类、处理、回复
- **文件管理**：反馈附件的上传和管理
- **统计分析**：反馈数据的统计和分析

**技术实现**：
- 多媒体文件上传
- 反馈状态流转
- 消息通知机制

#### 5. Cluster-Calculate (聚集区计算服务) - 算法引擎
**业务逻辑**：
- **K-means聚类**：将商铺按地理位置聚类
- **参数优化**：根据业务需求调整聚类参数
- **结果验证**：验证聚类结果的合理性
- **数据分析**：聚类结果的可视化分析

**技术实现**：
- Apache Commons Math数学库
- 地理坐标计算
- 异步计算处理
- 结果缓存机制

#### 6. Path-Calculate (路径计算服务) - 优化算法
**业务逻辑**：
- **路径规划**：计算最优配送路径
- **遗传算法**：使用遗传算法优化路径
- **多约束优化**：考虑时间、距离、载重等约束
- **实时计算**：支持实时路径调整

**技术实现**：
- 遗传算法实现
- 腾讯地图API集成
- 多线程并行计算
- 动态数据源支持

#### 7. Pickup (定点取货服务) - 业务扩展
**业务逻辑**：
- **取货点管理**：取货点的位置和容量管理
- **选址分析**：新取货点的选址建议
- **用户分配**：取货用户的分配和管理
- **效率优化**：取货效率的分析和优化

**技术实现**：
- 地理信息系统集成
- 数据挖掘算法
- 用户行为分析

### 1. 网关服务配置 (gateway.yaml)

**配置逻辑说明**：

```yaml
server:
  port: 8080                    # 网关服务端口
  # 逻辑：作为统一入口，使用标准HTTP端口，便于前端调用

spring:
  application:
    name: gateway               # 服务名称
    # 逻辑：在Nacos中注册的服务名，其他服务通过此名称发现网关
  cloud:
    nacos:
      server-addr: localhost:8848  # Nacos 地址
      # 逻辑：指向配置中心，网关启动时自动注册并获取其他服务信息
    gateway:
      discovery:
        locator:
          enabled: true         # 启用服务发现
          # 逻辑：自动根据Nacos中注册的服务创建路由
          lower-case-service-id: true  # 服务名小写
          # 逻辑：统一路由规则，避免大小写混乱
      routes:                   # 路由配置
        - id: guestbook         # 路由ID
          # 逻辑：每个路由的唯一标识
          uri: lb://guestbook   # 负载均衡地址
          # 逻辑：lb://表示负载均衡，guestbook是服务名
          predicates:
            - Path=/guestbook/**  # 路径匹配
            # 逻辑：以/guestbook/开头的请求都转发到guestbook服务
      globalcors:               # 全局跨域配置
        corsConfigurations:
          '[/**]':              # 匹配所有路径
            allowedOrigins: ["*"]  # 允许的源
            # 逻辑：允许所有域名访问，开发环境可以设置为*
            allowedMethods: ["GET", "POST", "PUT", "DELETE"]
            # 逻辑：允许的HTTP方法，覆盖RESTful API需求
            allowedHeaders: "*"
            # 逻辑：允许所有请求头，包括自定义认证头
            allowCredentials: true
            # 逻辑：允许携带认证信息，支持JWT令牌传递
            maxAge: 360000
            # 逻辑：预检请求缓存时间，减少OPTIONS请求频率
```

**路由转发逻辑**：
1. **请求接收**：前端发送请求到网关8080端口
2. **路径匹配**：网关根据路径前缀匹配对应服务
3. **服务发现**：从Nacos获取目标服务的实例列表
4. **负载均衡**：在多个实例中选择一个进行转发
5. **请求转发**：将请求转发给选中的服务实例

### 2. 用户服务配置 (userservice.yaml)

**配置逻辑说明**：

```yaml
server:
  port: 8081                    # 用户服务端口
  # 逻辑：避免与网关端口冲突，使用8081作为用户服务端口

spring:
  application:
    name: userservice           # 服务名称
    # 逻辑：与网关路由配置中的服务名保持一致
  datasource:                   # 数据源配置
    url: ********************************
    # 逻辑：连接主数据库，用户服务主要进行写操作
    username: root
    password: 123
    driver-class-name: com.mysql.jdbc.Driver
    # 逻辑：使用MySQL 5.7兼容的驱动
  cloud:
    nacos:
      server-addr: localhost:8848  # Nacos 地址
      # 逻辑：用于服务注册和配置获取

mybatis:                        # MyBatis 配置
  type-aliases-package: com.ict.ycwl.user.pojo
  # 逻辑：指定实体类包路径，简化XML中的类型引用
  configuration:
    map-underscore-to-camel-case: true  # 下划线转驼峰
    # 逻辑：数据库字段命名是下划线，Java是驼峰，自动转换

mybatis-plus:                   # MyBatis-Plus 配置
  global-config:
    db-config:
      id-type: auto             # 主键自增
      # 逻辑：使用数据库自增主键，避免分布式ID冲突
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL 日志
    # 逻辑：开发环境输出SQL日志，便于调试

jjking:                         # 自定义配置
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
  # 逻辑：指定数据源配置文件路径，用于动态数据源切换
```

**用户服务业务逻辑**：
1. **认证流程**：用户登录 → 验证密码 → 生成JWT → 返回令牌
2. **权限控制**：请求验证 → 解析JWT → 检查权限 → 允许/拒绝访问
3. **用户管理**：CRUD操作 → 数据验证 → 密码加密 → 数据库存储
4. **组织架构**：部门管理 → 角色分配 → 权限继承 → 层级控制

### 3. 数据管理服务配置 (datamanagement.yaml)

```yaml
server:
  port: 8085                    # 数据管理服务端口

spring:
  application:
    name: datamanagement
  cloud:
    nacos:
      server-addr: localhost:8848

mybatis-plus:                   # MyBatis-Plus 配置
  mapper-locations: classpath:mapper/*.xml  # Mapper XML 位置
  type-aliases-package: com.ict.datamanagement.domain.entity
  global-config:
    db-config:
      id-type: auto

route:
  version-limit: 3              # 路线版本限制

file:                           # 文件存储配置
  DOWNLOAD_PATH: E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/Download/
  UPLOAD_PATH: E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/uploadFile/
  DOWNLOAD_NULL_FROM_PATH: E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/nullFrom/
```

### 4. 路径计算服务配置 (pathcalculate.yaml)

**配置逻辑说明**：

```yaml
server:
  port: 8084                    # 路径计算服务端口
  # 逻辑：独立端口，避免与其他服务冲突

spring:
  datasource:
    dynamic:                    # 动态数据源配置
      # 逻辑：路径计算需要大量读取数据，使用读写分离提高性能
      primary: master           # 主数据源
      # 逻辑：写操作使用主库，保证数据一致性
      strict: false             # 非严格模式
      # 逻辑：允许未配置数据源的情况下使用默认数据源
      druid:                    # Druid 连接池配置
        default-config:
          initial-size: 5       # 初始连接数
          # 逻辑：服务启动时预创建连接，减少首次查询延迟
          max-active: 20        # 最大连接数
          # 逻辑：限制最大连接数，避免数据库连接过多
          min-idle: 5           # 最小空闲连接数
          # 逻辑：保持最小空闲连接，应对突发请求
          max-wait: 60000       # 获取连接等待超时时间
          # 逻辑：60秒超时，避免长时间等待
      datasource:
        master:                 # 主库配置
          url: ********************************
          # 逻辑：主库用于写操作，存储计算结果
          username: root
          password: 123
        slave1:                 # 从库配置
          url: ***************************************
          # 逻辑：从库用于读操作，查询商铺、路线数据
          username: root
          password: 123

route:
  version-limit: 100            # 路线版本限制
  # 逻辑：限制路线版本数量，避免历史数据过多影响性能

ycwl:                          # 自定义配置
  saveDbName: ycdb             # 数据库名称
  # 逻辑：指定计算结果保存的数据库
  saveDbPath: E:\www\wwwroot\ycwl\db  # 数据库保存路径
  # 逻辑：本地备份路径，用于数据恢复

jjking:                        # 自定义数据源配置
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
  # 逻辑：数据源配置文件路径，支持动态切换
  MybatisFile: file:E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
  # 逻辑：MyBatis配置文件路径，用于动态SQL配置
  URL: ********************************
  USER: root
  PASSWORD: 123
  # 逻辑：默认数据源连接信息，作为备选方案
```

**路径计算业务逻辑**：
1. **数据获取**：从数据库读取商铺、中转站、车辆信息
2. **聚类分析**：调用cluster-calculate服务获取商铺聚类结果
3. **路径规划**：使用遗传算法计算最优路径
4. **约束优化**：考虑时间窗口、车辆载重、距离限制
5. **结果验证**：验证路径的可行性和优化效果
6. **数据存储**：将计算结果保存到数据库

**动态数据源切换逻辑**：
- **@DS("master")**：写操作使用主库
- **@DS("slave1")**：读操作使用从库
- **自动切换**：根据操作类型自动选择数据源

### 5. 共享数据库配置 (shared-jdbc.yaml)

```yaml
jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource  # 数据源类型
    druid:
      master:                   # 主库配置
        url: ********************************?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8&allowMultiQueries=true
        username: root
        password: 123
        driver-class-name: com.mysql.jdbc.Driver
      slave1:                   # 从库配置
        url: ***************************************?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8&allowMultiQueries=true
        username: root
        password: 123
        driver-class-name: com.mysql.jdbc.Driver
      # 连接池配置
      initial-size: 15          # 初始连接数
      min-idle: 15              # 最小空闲连接数
      max-active: 200           # 最大连接数
      max-wait: 60000           # 获取连接等待超时时间
      time-between-eviction-runs-millis: 60000  # 空闲连接回收时间间隔
      min-evictable-idle-time-millis: 300000    # 连接最小空闲时间
      test-while-idle: true     # 空闲时检测连接是否有效
      test-on-borrow: false     # 取得连接时检测有效性
      test-on-return: false     # 归还连接时检测有效性
```

---

## 🔧 常见问题解决

### 1. 数据库连接问题

#### 问题：连接被拒绝
```
java.sql.SQLException: Access denied for user 'root'@'localhost'
```

**解决方案：**
```sql
-- 重置 MySQL 用户权限
ALTER USER 'root'@'localhost' IDENTIFIED BY '123';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

#### 问题：时区问题
```
java.sql.SQLException: The server time zone value 'CST' is unrecognized
```

**解决方案：**
```yaml
# 在数据库 URL 中添加时区参数
url: ********************************?useSSL=false&serverTimezone=Asia/Shanghai
```

### 2. Nacos 连接问题

#### 问题：服务注册失败
```
com.alibaba.nacos.api.exception.NacosException: failed to req API
```

**解决方案：**
1. 检查 Nacos 服务是否启动
2. 确认端口 8848 是否被占用
3. 检查防火墙设置

#### 问题：配置获取失败
```
com.alibaba.nacos.api.exception.NacosException: config not found
```

**解决方案：**
1. 确认配置文件已上传到 Nacos
2. 检查 Data ID 和 Group 是否正确
3. 确认配置文件格式是否正确

### 3. 文件路径问题

#### 问题：文件上传失败
```
java.io.FileNotFoundException: 系统找不到指定的路径
```

**解决方案：**
```bash
# 创建必要的目录
mkdir -p E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/uploadFile
mkdir -p E:/www/wwwroot/ycwl/ycwl-ms/data-management/data/Download
mkdir -p E:/www/wwwroot/ycwl/resource/file
```

### 4. 端口冲突问题

#### 问题：端口被占用
```
java.net.BindException: Address already in use: bind
```

**解决方案：**
```bash
# Windows 查看端口占用
netstat -ano | findstr :8080
taskkill /f /pid <PID>

# Linux/macOS 查看端口占用
lsof -i :8080
kill -9 <PID>
```

### 5. 前端启动问题

#### 问题：依赖安装失败
```
npm ERR! peer dep missing
```

**解决方案：**
```bash
# 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

#### 问题：跨域问题
```
Access to XMLHttpRequest at 'http://localhost:8080' from origin 'http://localhost:3000' has been blocked by CORS policy
```

**解决方案：**
检查网关配置中的 CORS 设置是否正确配置。

---

## 🔄 开发调试技巧

### 1. 日志配置

#### 后端日志配置
```yaml
logging:
  level:
    com.ict: debug              # 项目包日志级别
    org.springframework: info   # Spring 日志级别
    com.alibaba.druid: debug    # Druid 日志级别
```

#### 前端调试
```javascript
// 开启 Vue DevTools
// 在浏览器中安装 Vue DevTools 扩展
```

### 2. 热部署配置

#### 后端热部署
```xml
<!-- 在 pom.xml 中添加 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <optional>true</optional>
</dependency>
```

#### 前端热更新
```javascript
// Vite 自动支持热更新
// 修改代码后自动刷新页面
```

### 3. API 测试

#### 使用 Swagger
```
# 各服务的 Swagger 文档地址
http://localhost:8081/doc.html  # 用户服务
http://localhost:8082/doc.html  # 留言板服务
http://localhost:8085/doc.html  # 数据管理服务
```

---

## 🏗️ 系统整体架构交互逻辑

### 完整的业务流程示例

以**路径计算**为例，展示整个系统的交互逻辑：

```
用户操作 → 前端页面 → 网关路由 → 微服务处理 → 数据库操作 → 结果返回

详细流程：
1. 用户在前端点击"路径计算"按钮
2. 前端发送请求到 http://localhost:8080/pathcalculate/path/calculateAll
3. Gateway接收请求，根据路径/pathcalculate/**匹配到path-calculate服务
4. Gateway从Nacos获取path-calculate服务实例列表
5. Gateway使用负载均衡选择一个实例转发请求
6. Path-Calculate服务接收请求，开始路径计算
7. Path-Calculate调用Cluster-Calculate服务获取聚类结果
8. Path-Calculate调用Data-Management服务获取商铺数据
9. Path-Calculate使用遗传算法计算最优路径
10. 计算结果保存到数据库
11. 返回结果给前端，前端展示路径规划图
```

### 微服务间协作逻辑

```
Frontend (Vue3)
    ↓ HTTP请求
Gateway (8080)
    ↓ 路由转发
┌─────────────────────────────────────────────────────────────┐
│                    微服务集群                                │
│                                                           │
│  User-Service (8081)     ←→    Data-Management (8085)    │
│       ↓                             ↓                     │
│  认证授权                        数据管理                  │
│       ↓                             ↓                     │
│  Guestbook (8082)        ←→    Cluster-Calculate (8083)   │
│       ↓                             ↓                     │
│  客户反馈                        聚类计算                  │
│       ↓                             ↓                     │
│  Path-Calculate (8084)   ←→    Pickup (8086)              │
│       ↓                             ↓                     │
│  路径计算                        定点取货                  │
│                                                           │
└─────────────────────────────────────────────────────────────┘
    ↓ 数据操作
┌─────────────────────────────────────────────────────────────┐
│                    数据层                                  │
│                                                           │
│  MySQL Master (ycdb)     ←→    MySQL Slave (ycwl_slave1)  │
│       ↓                             ↓                     │
│  写操作                          读操作                    │
│                                                           │
│  Nacos (8848)            ←→    Redis (6379)               │
│       ↓                             ↓                     │
│  配置中心                        缓存                      │
│                                                           │
└─────────────────────────────────────────────────────────────┘
```

### 数据流向逻辑

1. **用户数据流**：
   - 用户登录 → User-Service验证 → 生成JWT → 前端存储
   - 后续请求携带JWT → Gateway验证 → 转发到业务服务

2. **业务数据流**：
   - 商铺数据 → Data-Management → 存储到MySQL
   - 计算请求 → Path-Calculate → 读取数据 → 算法处理 → 结果存储

3. **配置数据流**：
   - 配置变更 → Nacos控制台 → 推送到微服务 → 动态更新配置

### 系统扩展逻辑

1. **水平扩展**：
   - 单个服务多实例部署
   - Gateway自动负载均衡
   - 数据库读写分离

2. **垂直扩展**：
   - 新增业务服务
   - 在Gateway中添加路由配置
   - 在Nacos中添加服务配置

3. **容错机制**：
   - 服务健康检查
   - 自动故障转移
   - 熔断降级

## 📚 总结

通过以上步骤，您应该能够成功搭建起粤北卷烟物流管理平台的开发环境。关键点包括：

### 🎯 核心理解要点

1. **架构设计逻辑**：
   - 微服务按业务功能拆分，职责单一
   - 通过配置中心统一管理，支持动态更新
   - 使用网关作为统一入口，简化客户端调用
   - 数据库读写分离，提高系统性能

2. **技术选型原理**：
   - Spring Cloud提供完整的微服务解决方案
   - Nacos同时支持服务发现和配置管理
   - Vue 3 + TypeScript提供现代化前端开发体验
   - MySQL主从架构保证数据一致性和高可用

3. **业务流程逻辑**：
   - 用户认证是系统安全的基础
   - 数据管理是业务运营的核心
   - 算法计算是系统优化的关键
   - 客户反馈是持续改进的动力

### 🚀 最佳实践建议

1. **开发环境**：
   - 严格按照启动顺序启动服务
   - 定期检查服务健康状态
   - 及时查看日志排查问题

2. **配置管理**：
   - 统一使用Nacos管理配置
   - 区分开发、测试、生产环境
   - 敏感信息加密存储

3. **问题排查**：
   - 从网关开始逐层排查
   - 检查服务注册状态
   - 验证数据库连接
   - 确认配置文件正确性

如果在搭建过程中遇到问题，建议：
- 检查日志输出，定位具体错误
- 确认配置文件格式和内容正确
- 验证基础服务（MySQL、Nacos）正常运行
- 参考常见问题解决方案
- 理解每个配置项的业务逻辑，而不是简单复制配置 