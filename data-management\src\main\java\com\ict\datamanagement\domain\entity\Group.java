package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName group
 */
@TableName(value ="group")
@Data
public class Group implements Serializable {
    /**
     * 班组id
     */
    @TableId
    private Long groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 班组颜色
     */
    private String colour;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}