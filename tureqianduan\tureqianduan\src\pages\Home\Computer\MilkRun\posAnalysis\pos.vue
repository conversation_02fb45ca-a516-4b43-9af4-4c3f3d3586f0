<template>
  <div class="pos">
    <div class="section">
      <div class="box">
        <div class="searchContent">
          <div class="circle" ref="circle"></div>
          <div class="range">
            <el-select
              v-model="searchFromData.status"
              placeholder="全部"
              style="width: 140px"
              @change="getCircleColor"
            >
              <el-option label="禁用" :value="1">
                <div class="shopType">
                  <div class="ci" style="background-color: #794049"></div>
                  <span>禁用</span>
                </div>
              </el-option>
              <el-option label="启用(已分配)" :value="3">
                <div class="shopType">
                  <div class="ci" style="background-color: #4e8a38"></div>
                  <span>启用(已分配)</span>
                </div>
              </el-option>
              <el-option label="启用(未分配)" :value="2">
                <div class="shopType">
                  <div class="ci" style="background-color: #5378bc"></div>
                  <span>启用(未分配)</span>
                </div>
              </el-option>
              <el-option label="全部" :value="0">
                <div class="shopType">
                  <div class="ci" style="background-color: transparent"></div>
                  <span>全部</span>
                </div>
              </el-option>
            </el-select>
          </div>
          <div class="search">
            <el-input
              placeholder="请点击搜索"
              v-model:model-value="displayValue"
              @click="toggleSearchView"
            />
            <div class="searchView" v-if="searchView">
              <div class="content">
                <div class="group">
                  <div class="closeBold" @click="toggleSearchView">x</div>
                  <el-form
                    label-width="auto"
                    :model="searchFromData"
                    ref="searchModal"
                    class="searchForm"
                  >
                    <el-form-item label="名称" prop="pickupName">
                      <el-input
                        v-model="searchFromData.pickupName"
                        placeholder="请输入"
                      />
                    </el-form-item>
                    <el-form-item label="取货地类型" prop="type">
                      <el-select
                        placeholder="请选择"
                        v-model="searchFromData.type"
                      >
                        <el-option label="邮局" value="邮局" />
                        <el-option label="烟站" value="烟站" />
                        <el-option label="村委会" value="村委会" />
                        <el-option label="其他" value="其他" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="详细地址" prop="pickupAddress">
                      <el-input
                        v-model="searchFromData.pickupAddress"
                        placeholder="请输入"
                      />
                    </el-form-item>
                  </el-form>

                  <div class="btns">
                    <el-button @click="resetSearch" type="primary"
                      >清空</el-button
                    >
                    <el-button
                      style="margin-left: 10px"
                      @click="confirmSearch"
                      type="primary"
                      >搜索</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="butContent">
          <el-button
            :icon="UploadFilled"
            type="primary"
            @click="addSiteSelection"
            v-op="'pickup:location:add'"
            >添加选址</el-button
          >
          <el-button
            :icon="Memo"
            type="primary"
            style="margin-left: 2vw"
            @click="modifyInformation"
            v-op="'pickup:location:update'"
            >修改信息</el-button
          >
          <el-button
            :icon="SetUp"
            type="primary"
            style="margin-left: 2vw"
            @click="openTable"
            v-op="'pickup:location:importForm'"
            >导入表格</el-button
          >
          <el-button
            :icon="EditPen"
            type="primary"
            style="margin-left: 2vw"
            @click="openNote"
            v-op="'pickup:location:importLogs'"
            >导入日志</el-button
          >
          <el-button
            :icon="Refresh"
            type="primary"
            style="margin-left: 2vw"
            @click="exportTable"
            v-op="'pickup:location:exportForm'"
            >导出表格</el-button
          >
        </div>
        <div class="table">
          <el-table
            :data="tableData"
            ref="tableRef"
            :cell-style="{ textAlign: 'center' }"
            @row-click="handleRowClick"
            :header-cell-style="{
              height: '4vh',
              'text-align': 'center',
            }"
            size="small"
            :row-style="{ height: '3.9vh' }"
            :row-class-name="tableRowClassName"
            style="font-size: 0.8vw; width: 100%"
            @selection-change="handleSelect"
          >
            <el-table-column type="selection"></el-table-column>
            <el-table-column
              label="序号"
              type="index"
              :index="Bindex"
            ></el-table-column>
            <el-table-column
              prop="pickupName"
              label="名称"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column
              prop="pickupAddress"
              label="详细地址"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column label="经纬度" :show-overflow-tooltip="true">
              <template #default="scope">
                {{
                  "经度:" +
                  scope.row.longitude +
                  "," +
                  "纬度:" +
                  scope.row.latitude
                }}
              </template>
            </el-table-column>
            <el-table-column prop="type" label="取货地类型"></el-table-column>
            <el-table-column label="关联商户" :show-overflow-tooltip="true">
              <template #default="scope">
                {{
                  scope.row.stores.length > 0
                    ? scope.row.stores
                        .map((item: any) => {
                          return item.storeName;
                        })
                        .join(",")
                    : "无"
                }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <span v-if="scope.row.status === 1">禁用</span>
                <span v-else-if="scope.row.status === 2">启用(未分配)</span>
                <span v-else-if="scope.row.status === 3">启用(已分配)</span>
                <span v-else>全部</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="divide">
            <div class="pageDivide">
              <el-pagination
                v-if="nowData"
                layout="prev, pager, next"
                :current-page="searchData.pageNum"
                :page-size="searchData.pageSize"
                :total="Number(nowData.total)"
                @current-change="handlePageChange"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="mapSection">
        <div class="map">
          <pickMap
            ref="mapRef"
            :list="points"
            :mapType="0"
            :loading="isLoading"
          ></pickMap>
        </div>
        <div class="mapButton">
          <el-button
            type="primary"
            @click="edit"
            v-op="'pickup:location:recalculate'"
            >重新计算</el-button
          >
        </div>
      </div>
    </div>
    <div class="waitfor">
      <!-- 商户展示组件 -->
      <div class="merchant-container">
        <div class="carousel-header">
          <input type="checkbox" v-model="selectAll" @click.stop />
          <!-- <el-icon size="20px" style="cursor: pointer" @click="edit">
            <Edit />
          </el-icon> -->
          <span> 待分配商户（{{ totalMerchants }}）： </span>
        </div>

        <div class="carousel-wrapper">
          <el-icon class="arrow left" @click="prevGroup">
            <ArrowLeft />
          </el-icon>
          <div class="merchant-group">
            <div
              v-for="(merchant, index) in visibleMerchants"
              :key="index"
              class="merchant-card"
            >
              <div class="cardOk">
                <input
                  type="checkbox"
                  :checked="merchant.selected"
                  @click.stop="toggleSelection(merchant.id)"
                />
              </div>
              <div class="cardInfo">
                <p>客户编码：{{ merchant.customerCode }}</p>
                <p>客户名称：{{ merchant.contactName }}</p>
                <p>客户地址：{{ merchant.storeAddress }}</p>
                <p>
                  最近选址：{{ merchant.recentlySited }} 距离
                  <span class="distance">{{ merchant.dist }}</span> km
                </p>
              </div>
            </div>
          </div>

          <el-icon class="arrow right" @click="nextGroup">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>
    <addPost ref="add" @add="addConfirm"></addPost>
    <changePost
      ref="change"
      @change="changeConfirm"
      :row="current"
    ></changePost>
    <uploadNote ref="uploadNoteRef"></uploadNote>
    <uploadTable ref="uploadTableRef"></uploadTable>
  </div>
</template>

<script setup lang="ts">
  import {
    EditPen,
    Memo,
    Refresh,
    SetUp,
    UploadFilled,
    Edit,
  } from "@element-plus/icons-vue";
  import addPost from "./modal/addPost.vue";
  import changePost from "./modal/changePost.vue";
  import uploadTable from "./modal/uploadTable.vue";
  import uploadNote from "./modal/uploadNote.vue";
  import { ref, computed } from "vue";
  import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
  import { useLocationStore } from "@/store/pos";
  import { usePickStore } from "@/store/pick";
  import pickMap from "../pickMap.vue";
  const mapRef = ref<any>();
  const store2 = usePickStore();
  const points = ref<any>([]);
  const store = useLocationStore();
  const uploadNoteRef = ref<any>();
  const uploadTableRef = ref<any>();
  const add = ref<any>();
  const nowData = ref<any>();
  const change = ref<any>();
  const current = ref<any>({});
  const tableData = ref<any>();
  const tableRef = ref<any>();
  const circle = ref<any>();
  const searchData = reactive({
    pageNum: 1,
    pageSize: 6,
  });
  const displayValue = ref("");
  const inSearch = ref<boolean>(false);
  const searchView = ref<boolean>(false);
  const searchModal = ref<any>();
  const searchFromData = reactive<any>({
    pageNum: 1,
    pageSize: 6,
    pickupAddress: "",
    pickupName: "",
    status: "",
    type: "",
  });

  // 属性名称映射表（英文属性名 -> 中文名称）
  const propertyNameMap: any = {
    type: "取货柜类型",
    pickupName: "取货柜地址",
    pickupAddress: "地址",
    status: "颜色",
  };
  const isLoading = ref(true);

  interface Merchant {
    id: number;
    customerCode: string;
    contactName: string;
    storeAddress: string;
    recentlySited: string;
    dist: number;
    selected: boolean; //
  }

  // 模拟数据
  const merchants = ref<Merchant[]>([]);

  const currentGroup = ref(0);
  const itemsPerGroup = 4;

  // 计算属性
  const totalMerchants = computed(() => merchants.value.length);
  const groupedMerchants = computed(() => {
    const groups = [];
    for (let i = 0; i < merchants.value.length; i += itemsPerGroup) {
      groups.push(merchants.value.slice(i, i + itemsPerGroup));
    }
    return groups;
  });

  // 在代码中添加调试语句

  // 全选状态
  const selectAll = computed({
    get: () => merchants.value.every((m) => m.selected),
    set: (value: boolean) => {
      merchants.value.forEach((m) => (m.selected = value));
    },
  });

  // 单选切换
  const toggleSelection = (id: number) => {
    const merchant = merchants.value.find((m) => m.id === id);
    if (merchant) {
      merchant.selected = !merchant.selected;
    }
  };

  function addConfirm(e: any) {
    if (inSearch.value) {
      ElMessage.error("搜索状态中");
      return;
    }
    store.addLocationData(e).then((res: any) => {
      if (res.msg) {
        ElMessage.success(res.msg);
        getData();
      } else {
        ElMessage.error("失败");
      }
    });
  }

  const handleRowClick = (row: any) => {
    if (!mapRef.value.getMap()) {
      return;
    }
    if (
      row.status == 0 ||
      row.status == 2 ||
      row.color == 1 ||
      row.color == 5
    ) {
      return;
    }
    // 执行地图定位
    mapRef.value.getMap().setZoomAndCenter(16, [row.longitude, row.latitude]);
  };

  function changeConfirm(e: any) {
    store.updateLocationData(e).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success(res.msg);
        if (inSearch.value) {
          getData(4);
          return;
        }
        getData();
      } else if (!res.code) {
        ElMessage.error("失败");
        return;
      }
    });
  }

  const tableRowClassName = ({ row }: any) => {
    if (row.status === 1) {
      return "red";
    } else if (row.status === 2) {
      return "gray";
    } else if (row.status === 3) {
      return "gree";
    }
  };

  const visibleMerchants = computed(() => {
    return groupedMerchants.value[currentGroup.value] || [];
  });

  // 切换逻辑
  const nextGroup = () => {
    if (currentGroup.value < groupedMerchants.value.length - 1) {
      currentGroup.value++;
    }
  };

  const prevGroup = () => {
    if (currentGroup.value > 0) {
      currentGroup.value--;
    }
  };
  //处理分页
  function handleSelect(row: any) {
    current.value = row[0];
  }
  function handlePageChange(num: number = 1) {
    if (inSearch.value) {
      searchFromData.pageNum = num;
      getData(4);
      return;
    }
    searchData.pageNum = num;
    getData();
  }

  function getCircleColor(e: any) {
    searchFromData.pageNum = 1;
    searchData.pageNum = 1;
    inSearch.value = true;
    getData(4);
    if (e === 1) {
      circle.value.style = "background-color: rgb(121, 64, 73)";
      return;
    } else if (e === 2) {
      circle.value.style = "background-color: #5378bc";
      return;
    } else if (e === 3) {
      circle.value.style = "background-color: rgb(78, 138, 56)";
      return;
    }
  }

  function openNote() {
    nextTick(() => {
      uploadNoteRef.value.onOpenDialog();
      uploadNoteRef.value.noteVisible = true;
    });
  }

  function edit() {
    const ids = merchants.value
      .filter((item: any) => item.selected)
      .map((item: any) => item.id);
    store.editLocationData({ ids }).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success(res.msg);
        getData(2);
        getData();
      }
      console.log(res);
      if (!res.code || res.code === "ERR_BAD_RESPONSE") {
        ElMessage.error("计算失败");
      }
    });
  }

  async function getData(type = 0) {
    //获得未分配列表
    if (type == 2) {
      store.getUnassignedListData().then((res: any) => {
        if (!res.data) {
          return;
        }
        merchants.value = res.data.map(
          (item: any) => ((item.selected = false), item)
        );
      });
    } else if (type == 4) {
      getMapData();
      store.getLocationListData({ ...searchFromData }).then((res: any) => {
        inSearch.value = true;
        nowData.value = res;
        searchData.pageNum = res.current;
        tableData.value = res.records;
      });
    } else {
      getMapData();
      store.getLocationListData({ ...searchData }).then((res: any) => {
        nowData.value = res;
        searchData.pageNum = res.current;
        tableData.value = res.records;
      });
    }
  }

  async function getMapData() {
    try {
      // 异步获取数据
      isLoading.value = true;
      store2.getMap().then((res: any) => {
        points.value = res.pickupUsers.map((item: any) => {
          const point: any = {
            lnglat: [item.longitude, item.latitude],
            info: {
              name: item.storeName,
              address: item.pickupContainers ? item.pickupContainers : "无",
              distance: item.deliveryDistance,
            },
          };
          if (item.color === 3) {
            point.type = "B";
          } else if (item.color === 4) {
            point.type = "C";
          } else if (item.color === 2) {
            point.type = "D";
          }
          if (item.pickupContainers) {
            point.info.dian = res.pickupLocationVos
              .filter((vos: any) => vos.pickupName === item.pickupContainers)
              .map((vos: any) => [vos.longitude, vos.latitude]);
          }
          return point;
        });
        points.value = [
          ...points.value,
          ...res.pickupLocationVos.map((item: any) => {
            if (item.status == 1) {
              return {
                lnglat: [item.longitude, item.latitude],
                type: "E",
                info: {
                  name: item.pickupName,
                },
              };
            } else if (item.status == 3) {
              return {
                lnglat: [item.longitude, item.latitude],
                type: "F",
                info: {
                  name: item.pickupName,
                  stores:
                    item.stores.length > 0
                      ? item.stores.map((item: any) => item.storeName).join(",")
                      : "无",
                  posList: res.pickupUsers
                    .filter(
                      (user: any) => user.pickupContainers == item.pickupName
                    )
                    .map((user: any) => [user.longitude, user.latitude]),
                },
              };
            }
            return {
              lnglat: [item.longitude, item.latitude],
              type: "A",
              info: {
                name: item.pickupName,
                stores:
                  item.stores.length > 0
                    ? item.stores.map((item: any) => item.storeName).join(",")
                    : "无",
                posList: res.pickupUsers
                  .filter(
                    (user: any) => user.pickupContainers == item.pickupName
                  )
                  .map((user: any) => [user.longitude, user.latitude]),
              },
            };
          }),
        ];
        isLoading.value = false;
      });
    } catch (error) {
      console.error("获取数据失败", error);
    }
  }

  function toggleSearchView() {
    // 获取所有非空属性，并以分号分隔
    const nonEmptyProps = Object.entries(searchFromData)
      .filter(([key, value]) => {
        // 过滤掉空值和pageNum、pageSize属性
        return (
          value !== "" &&
          value !== null &&
          value !== undefined &&
          key !== "pageNum" &&
          key !== "pageSize"
        );
      })
      .map(([key, value]) => {
        // 使用中文属性名
        const displayName: any = propertyNameMap[key] || key;
        return `${displayName}:${value}`;
      })
      .join("; ");

    // 更新显示值
    displayValue.value = nonEmptyProps;
    searchView.value = !searchView.value;
  }
  onMounted(() => {
    getData(2);
    getData();
  });
  function resetSearch() {
    inSearch.value = false;
    // 重置所有属性值为空字符串，但保留pageNum和pageSize
    Object.keys(searchFromData).forEach((key) => {
      if (key !== "pageNum" && key !== "pageSize") {
        searchFromData[key] = "";
      }
    });
    circle.value.style = "";
    searchFromData.status = "";
    searchFromData.pageNum = 1;
    searchData.pageNum = 1;
    getData();
  }
  function confirmSearch() {
    if (!searchFromData.status) {
      searchFromData.status = 0;
    }
    store.getLocationListData({ ...searchFromData }).then((res: any) => {
      nowData.value = res;
      if (nowData.value.records.length == 0) {
        ElMessage.error("没有查询到数据");
      }
      inSearch.value = true;
      searchData.pageNum = res.current;
      tableData.value = res.records;
    });
  }
  //添加选址弹窗
  function addSiteSelection() {
    add.value.addPostOpen = true;
  }
  function exportTable() {
    store.exportLocationData().then((res: any) => {
      const url = window.URL.createObjectURL(res);
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "定点商户信息表.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
  //修改信息选址弹窗
  function modifyInformation() {
    if (tableRef.value?.getSelectionRows().length > 1) {
      ElMessage.error("只能选择一条");
      return;
    } else if (tableRef.value?.getSelectionRows().length == 0) {
      ElMessage.error("没有选择");
      return;
    }
    change.value.changePostOpen = true;
  }
  function openTable() {
    uploadTableRef.value.uploadVisible = true;
  }
  function Bindex(index: number) {
    const page = searchData.pageNum; // 当前页码
    const pagesize = searchData.pageSize; // 每页条数
    return index + 1 + (page - 1) * pagesize;
  }
</script>

<style lang="less" scoped>
  .pos {
    .el-button {
      background-color: #97c7e7;
      color: #003766;
      border: none !important;
      font-size: 14px;
    }

    width: 100%;
    height: 80vh;

    .section {
      width: 100%;
      display: flex;

      .box {
        width: 54.1%;

        .searchContent {
          width: 100%;
          height: 5vh;
          display: flex;
          align-items: center;

          .circle {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            border: white 1.6px solid;
          }

          .range {
            margin-left: 1vw;

            :deep(.el-input__inner) {
              font-size: 12px;
            }
            :deep(.el-select) {
              --el-input-bg-color: transparent !important;
              background-color: transparent !important;
            }
            :deep(.el-input) {
              --el-input-bg-color: transparent !important;
              background-color: transparent !important;
            }
          }

          .search {
            flex: 1;
            margin-left: 1vw;
            position: relative;

            .searchView {
              :deep(.el-form-item__label) {
                font-size: 16px;
              }

              :deep(.el-input) {
                width: 100px;
                height: 28px;
              }

              :deep(.el-form-item) {
                margin-bottom: 4px;
              }

              position: absolute;
              background-color: #000032;
              border: 1px solid #fff;
              z-index: 999;
              top: 5vh;

              .btns {
                display: flex;
                justify-content: center;
                margin-bottom: 12px;
              }

              .content {
                width: 100%;
                display: flex;
                justify-content: center;
              }

              .group {
                .searchForm {
                  padding-left: 4%;
                  width: 80%;
                  padding-top: 6vh;
                  display: flex;
                  flex-wrap: wrap;
                  :deep(.el-form-item) {
                    margin-right: 20px; // 调整表单项间距
                    &:last-child {
                      flex-basis: 100%; // 移动端单列
                      margin: 0;
                    }
                  }
                  :deep(.el-form-item__content),
                  :deep(.el-form-item__label) {
                    width: 6vw; // 统一标签和内容宽度
                    :deep(.el-select) {
                      --el-input-bg-color: transparent !important;
                      background-color: transparent !important;
                    }
                    :deep(.el-input) {
                      --el-input-bg-color: transparent !important;
                      background-color: transparent !important;
                    }
                  }
                  padding-bottom: 1vh;
                }

                position: relative;

                .closeBold {
                  position: absolute;
                  cursor: pointer;
                  text-align: center;
                  width: 40px;
                  height: 36px;
                  right: 0px;
                  top: 0px;
                  background-color: #97c7e7;
                  font-size: 30px;
                  color: black;
                }
              }
            }
          }
        }

        .butContent {
          margin-top: 2vh;
          width: 100%;
          height: 5vh;
          display: flex;
          margin-bottom: 1.2vh;

          .el-button {
            font-size: 12px;
            width: 6.6vw;
            height: 4vh;
          }
        }

        .table {
          height: 47.5vh;
          :deep(.el-table__row) {
            &.red {
              background-color: rgba(101, 55, 61, 0.8);
            }
            &.gray {
              background-color: rgba(85, 121, 189, 0.8);
            }
            &.gree {
              background-color: rgba(78, 138, 56, 0.8);
            }
          }
          position: relative;
          .divide {
            width: 100%;
            position: absolute;
            bottom: 0;
          }
          .pageDivide {
            display: flex;
            justify-content: center;

            :deep(.el-pagination) {
              margin-bottom: 0;
            }
          }
        }
      }

      .mapSection {
        margin-left: 2%;
        flex: 1;
        margin-right: 2%;

        .map {
          position: static;
          border: #6e91c4 4px solid;
          width: 100%;
          height: 56vh;
        }

        .mapButton {
          margin-top: 1vh;
          display: flex;
          justify-content: center;
        }
      }
    }

    .waitfor {
      color: #cbe7eb;
      margin-top: 1vh;
      width: 100%;
      height: 24vh;
      overflow: hidden;

      .merchant-container {
        padding: 0 20px;
      }

      .carousel-header {
        display: flex;
        font-size: 18px;
      }

      .carousel-wrapper {
        margin-top: 1vh;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
      }

      .merchant-group {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
      }

      .merchant-card {
        font-size: 12px;
        overflow: hidden;
        border: 2px solid #84aeeb;
        border-radius: 8px;
        padding-right: 5px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        display: flex;
      }

      .cardOk {
        height: 100%;
        width: 1.4vw;
        background-color: #84aeeb;
        display: flex;
        justify-content: center;
        align-self: center;
      }

      .cardInfo {
        padding-left: 4px;
      }

      .merchant-card p {
        margin: 8px 0;
      }

      .distance {
        color: #409eff;
        font-weight: bold;
      }

      .arrow {
        font-size: 30px;
        color: #cbe7eb;
        cursor: pointer;
        padding: 10px;
        border-radius: 50%;
        transition: all 0.3s;
      }

      .arrow:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }
    }
  }

  .shopType {
    display: flex;
    align-items: center;
    color: white;
    font-size: 16px;

    .ci {
      width: 17px;
      height: 17px;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
</style>
