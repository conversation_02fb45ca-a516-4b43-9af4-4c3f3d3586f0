<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.FeedbackMapper">

    <sql id="feedbackListColumn">
        feedback_id,fb.route_name,delivery_name,fb.customer_manager_name,feedback_information,fb.customer_code,store_name,contact_name,store_address,fb.area_name,order_date,feedback_status,fb.create_time,fb.update_time
    </sql>

    <resultMap id="feedbackList" type="com.ict.ycwl.guestbook.api.vo.FeedbackListVo">
        <id property="feedbackId" column="feedback_id"></id>
        <result property="routeName" column="route_name"></result>
        <result property="deliveryName" column="delivery_name"></result>
        <result property="customerManagerName" column="customer_manager_name"></result>
        <result property="feedbackInformation" column="feedback_information"></result>
        <result property="orderDate" column="order_date"></result>
        <result property="feedbackStatus" column="feedback_status"></result>
        <result property="customerCode" column="customer_code"></result>
        <result property="storeName" column="store_name"></result>
        <result property="contactName" column="contact_name"></result>
        <result property="storeAddress" column="store_address"></result>
        <collection property="feedbackFileList" fetchType="eager"
                    select="com.ict.ycwl.guestbook.mapper.FeedbackFileMapper.selectFilePath"
                    column="feedback_id"></collection>
    </resultMap>

    <select id="selectFeedbackList" resultMap="feedbackList"
            parameterType="com.ict.ycwl.guestbook.api.form.FeedbackListForm">
        select <include refid="feedbackListColumn"></include>
        FROM feedback fb LEFT JOIN store st ON fb.customer_code = st.customer_code
        <where>
            feedback_type = #{feedbackType}
            <if test="orderStartDate != null and orderEndDate != null">
                and order_date BETWEEN #{orderStartDate} AND #{orderEndDate}
            </if>
            <if test="customerCode != '' and customerCode != null">
                and fb.customer_code like concat('%',#{customerCode},'%')
            </if>
            <if test="contactName != '' and contactName != null">
                and contact_name like concat('%',#{contactName},'%')
            </if>
            <if test="routeId != null">
                and fb.route_id = #{routeId}
            </if>
            <if test="feedbackStatus != null">
                and feedback_status = #{feedbackStatus}
            </if>
            <if test="areaName != '' and areaName != null">
                <choose>
                    <when test="areaName == '乳源瑶族自治县'">
                        AND (fb.area_name = '大桥镇' or fb.area_name = '必背镇')
                    </when>
                    <otherwise>
                        AND fb.area_name = #{areaName}
                    </otherwise>
                </choose>
            </if>
            <if test="routeName != '' and routeName != null">
                and fb.route_name = #{routeName}
            </if>
            <if test="deliveryWorkNumber != '' and deliveryWorkNumber != null">
                and fb.delivery_work_number = #{deliveryWorkNumber}
            </if>
            <if test="customerManagerName != '' and customerManagerName != null">
                and fb.customer_manager_name = #{customerManagerName}
            </if>
        </where>
        ORDER BY feedback_status ASC,create_time DESC
    </select>

    <insert id="insertFeedback" parameterType="feedback" useGeneratedKeys="true" keyProperty="feedbackId">
        INSERT INTO feedback
        VALUES(NULL,#{customerCode},#{routeId},#{routeName},#{orderDate},#{deliveryWorkNumber},#{deliveryName},#{customerManagerName},#{ManagerWorkNumber},#{createBy},#{feedbackInformation}
              , #{feedbackType}, #{feedbackStatus}, #{createTime}, #{completeTime}, #{areaName})
    </insert>

    <update id="updateFeedbackStatus">
        UPDATE feedback SET feedback_status = #{feedbackStatus}
        <if test="completeTime != null">
            , complete_time = #{completeTime}
        </if>
        WHERE feedback_id =#{feedbackId}
    </update>

    <select id="selectTypeById" parameterType="Long" resultType="String">
        SELECT feedback_type FROM feedback WHERE feedback_id = #{feedbackId}
    </select>

    <delete id="deleteFeedbackByIds">
        DELETE FROM feedback WHERE feedback_id IN
        <foreach collection="feedbackIdList" separator="," item="feedbackId" open="(" close=")">
            #{feedbackId}
        </foreach>
    </delete>

    <update id="updateUpdateData">
        UPDATE feedback SET update_time = #{updateTime},update_by = #{updateBy} WHERE feedback_id = #{feedbackId}
    </update>

    <select id="selectCountByType" resultType="integer" parameterType="string">
        SELECT COUNT(*) FROM `feedback`
        WHERE feedback_type = #{feedbackType} AND feedback_status = 0
    </select>

</mapper>