<template>
  <div class="changeMilk">
    <el-dialog
      v-model="MilkOpen"
      title="修改定点取货数据"
      width="60%"
      height="70%"
      :close-on-click-modal="false"
    >
      <div class="flex-center">
        <el-form
          label-width="auto"
          width="100%"
          class="areaForm"
          :model="mi"
          ref="formRef"
        >
          <el-form-item label="名称">
            <p>{{ data.contactName }}</p>
          </el-form-item>
          <el-form-item label="档位">
            <p>{{ data.gear }}</p>
          </el-form-item>
          <el-form-item label="编码">
            <p>{{ data.customerCode }}</p>
          </el-form-item>
          <el-form-item label="配送距离" prop="deliveryDistance">
            <el-input
              placeholder="请输入"
              v-model="mi.deliveryDistance"
            ></el-input>
          </el-form-item>
          <el-form-item label="线路">
            <p>{{ data.customerManagerName }}</p>
          </el-form-item>
          <el-form-item label="取货柜类型" prop="type">
            <el-select placeholder="请选择" v-model="mi.type">
              <el-option label="01" value="01" />
              <el-option label="02" value="02" />
              <el-option label="03" value="03" />
              <el-option label="04" value="04" />
              <el-option label="无" value="" />
            </el-select>
          </el-form-item>
          <el-form-item label="地址">
            <div class="ellipsis-2-lines">{{ data.storeAddress }}</div>
          </el-form-item>
          <el-form-item label="取货柜地址" prop="pickupContainers">
            <el-input
              v-if="data.locks == 0"
              placeholder="请输入"
              v-model="mi.pickupContainers"
            ></el-input>
            <p v-else>
              {{ data.pickupContainers ? data.pickupContainers : "" }}
            </p>
          </el-form-item>
          <el-form-item prop="locks">
            <template #label>
              <div class="flex-center">
                <el-tooltip placement="top" effect="dark">
                  <img
                    src="@/assets/images/空心问号.png"
                    alt=""
                    width="16px"
                    height="16px"
                    style="padding-right: 4px"
                  />
                  <template #content>
                    加锁的商户不受系统计算的影响，选址配置不会更变。如需更改配置则需要更改为解锁状态。
                  </template>
                </el-tooltip>
                <span>是否加锁</span>
              </div>
            </template>
            <el-select placeholder="请选择" v-model="mi.locks">
              <el-option label="解锁" :value="0" />
              <el-option label="加锁" :value="1" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="btns">
          <el-button type="primary" @click="closeChange">取消</el-button>
          <el-button
            type="primary"
            style="margin-left: 100px"
            @click="confirmChange"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  const MilkOpen = ref<boolean>(false);
  defineExpose({ MilkOpen });
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
  });
  const mi = ref<any>({
    locks: "",
    deliveryDistance: "",
    pickupContainers: "",
    type: "",
  });

  watch(
    () => props.data,
    (newVal) => {
      mi.value = {
        locks: newVal.locks,
        deliveryDistance: Number(newVal.deliveryDistance),
        pickupContainers: newVal.pickupContainers,
        type: newVal.type,
      };
    },
    { deep: true, immediate: true }
  ); // 立即执行初始化

  const formRef = ref<any>();

  const changeEmit = defineEmits(["confirmChange"]);

  function confirmChange() {
    MilkOpen.value = false;
    console.log(mi.value);
    formRef.value.validate((valid: boolean) => {
      if (valid) {
        changeEmit("confirmChange", mi.value);
        MilkOpen.value = false;
      }
    });
  }

  function closeChange() {
    formRef.value.resetFields();
    MilkOpen.value = false;
  }
</script>

<style lang="less" scoped>
  .ellipsis-2-lines {
    display: -webkit-box; /* 弹性伸缩盒子模型 */
    -webkit-box-orient: vertical; /* 垂直方向排列子元素 */
    -webkit-line-clamp: 2; /* 限制显示行数 */
    overflow: hidden; /* 溢出隐藏 */
    text-overflow: ellipsis; /* 溢出文本显示省略号 */
    line-height: 1.5; /* 行高建议显式定义 */
    max-width: 100%; /* 防止容器宽度异常 */
  }
  .flex-center {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .flex {
    display: flex;
    justify-content: center;
  }
  .areaForm {
    .areaItem {
      font-size: 20px;
    }

    p {
      font-size: 20px;
      width: 10vw;
      height: 6vh;
    }
    :deep(.el-select) {
      width: 10vw;
      height: 3.6vh;
    }
    :deep(.el-input) {
      width: 10vw;
      height: 3.6vh;
    }
    :deep(.el-form-item) {
      margin-right: 20px; // 调整表单项间距
      &:last-child {
        flex-basis: 100%;
      }
    }
    :deep(.el-form-item__content),
    :deep(.el-form-item__label) {
      width: 12vw; // 统一标签和内容宽度
    }
    display: flex;
    flex-wrap: wrap;
    justify-content: center; // 水平居中
    max-width: 80%; // 控制表单最大宽度
  }
  .btns {
    display: flex;
    justify-content: center;
    color: black;
  }
</style>
