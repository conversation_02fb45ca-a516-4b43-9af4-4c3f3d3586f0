-- =============================================
-- 数据库名称转换：中转站 → 对接点
-- 执行日期：2025-08-15
-- 影响范围：route, route_copy_522, transit_depot, transit_depot_back, work_parameter
-- =============================================

-- 开始事务，确保数据一致性
START TRANSACTION;

-- =============================================
-- 1. 备份当前数据（安全措施）
-- =============================================

-- 创建备份表（如果不存在）
CREATE TABLE IF NOT EXISTS route_name_backup_20250815 AS 
SELECT route_id, route_name, update_time FROM route WHERE route_name LIKE '%中转站%';

CREATE TABLE IF NOT EXISTS transit_depot_name_backup_20250815 AS 
SELECT transit_depot_id, transit_depot_name FROM transit_depot WHERE transit_depot_name LIKE '%中转站%';

CREATE TABLE IF NOT EXISTS work_parameter_name_backup_20250815 AS 
SELECT id, name FROM work_parameter WHERE name LIKE '%中转站%';

-- =============================================
-- 2. 执行名称更新
-- =============================================

-- 2.1 更新 route 表中的路线名称
UPDATE route 
SET route_name = REPLACE(route_name, '新丰县中转站', '新丰县对接点')
WHERE route_name LIKE '%新丰县中转站%';

UPDATE route 
SET route_name = REPLACE(route_name, '坪石镇中转站', '老坪石对接点')
WHERE route_name LIKE '%坪石镇中转站%';

UPDATE route 
SET route_name = REPLACE(route_name, '翁源县中转站', '翁源县对接点')
WHERE route_name LIKE '%翁源县中转站%';

-- 2.2 更新 route_copy_522 表中的路线名称
UPDATE route_copy_522 
SET route_name = REPLACE(route_name, '新丰县中转站', '新丰县对接点')
WHERE route_name LIKE '%新丰县中转站%';

UPDATE route_copy_522 
SET route_name = REPLACE(route_name, '坪石镇中转站', '老坪石对接点')
WHERE route_name LIKE '%坪石镇中转站%';

UPDATE route_copy_522 
SET route_name = REPLACE(route_name, '翁源县中转站', '翁源县对接点')
WHERE route_name LIKE '%翁源县中转站%';

-- 2.3 更新 transit_depot 表中的中转站名称
UPDATE transit_depot 
SET transit_depot_name = '新丰县对接点'
WHERE transit_depot_name = '新丰县中转站';

UPDATE transit_depot 
SET transit_depot_name = '老坪石对接点'
WHERE transit_depot_name = '坪石镇中转站';

UPDATE transit_depot 
SET transit_depot_name = '翁源县对接点'
WHERE transit_depot_name = '翁源县中转站';

-- 2.4 更新 transit_depot_back 表中的备份数据
UPDATE transit_depot_back 
SET transit_depot_name = '新丰县对接点'
WHERE transit_depot_name = '新丰县中转站';

UPDATE transit_depot_back 
SET transit_depot_name = '老坪石对接点'
WHERE transit_depot_name = '坪石镇中转站';

UPDATE transit_depot_back 
SET transit_depot_name = '翁源县对接点'
WHERE transit_depot_name = '翁源县中转站';

-- 2.5 更新 work_parameter 表中的工作参数名称
UPDATE work_parameter 
SET name = '新丰县对接点'
WHERE name = '新丰县中转站';

UPDATE work_parameter 
SET name = '老坪石对接点'
WHERE name = '坪石镇中转站';

UPDATE work_parameter 
SET name = '翁源县对接点'
WHERE name = '翁源县中转站';

-- =============================================
-- 3. 验证更新结果
-- =============================================

-- 检查更新后的数据
SELECT '=== route 表更新结果 ===' as info;
SELECT COUNT(*) as updated_routes FROM route WHERE route_name LIKE '%对接点%';
SELECT COUNT(*) as remaining_old FROM route WHERE route_name LIKE '%中转站%';

SELECT '=== transit_depot 表更新结果 ===' as info;
SELECT transit_depot_name FROM transit_depot WHERE transit_depot_name LIKE '%对接点%';

SELECT '=== work_parameter 表更新结果 ===' as info;
SELECT name FROM work_parameter WHERE name LIKE '%对接点%';

-- =============================================
-- 4. 提交事务（如果一切正常）
-- =============================================
-- COMMIT;

-- 如果需要回滚，使用：
-- ROLLBACK;

-- =============================================
-- 5. 回滚脚本（紧急情况使用）
-- =============================================
/*
-- 紧急回滚脚本
UPDATE route SET route_name = REPLACE(route_name, '新丰县对接点', '新丰县中转站') WHERE route_name LIKE '%新丰县对接点%';
UPDATE route SET route_name = REPLACE(route_name, '老坪石对接点', '坪石镇中转站') WHERE route_name LIKE '%老坪石对接点%';
UPDATE route SET route_name = REPLACE(route_name, '翁源县对接点', '翁源县中转站') WHERE route_name LIKE '%翁源县对接点%';

UPDATE transit_depot SET transit_depot_name = '新丰县中转站' WHERE transit_depot_name = '新丰县对接点';
UPDATE transit_depot SET transit_depot_name = '坪石镇中转站' WHERE transit_depot_name = '老坪石对接点';
UPDATE transit_depot SET transit_depot_name = '翁源县中转站' WHERE transit_depot_name = '翁源县对接点';

UPDATE work_parameter SET name = '新丰县中转站' WHERE name = '新丰县对接点';
UPDATE work_parameter SET name = '坪石镇中转站' WHERE name = '老坪石对接点';
UPDATE work_parameter SET name = '翁源县中转站' WHERE name = '翁源县对接点';
*/
