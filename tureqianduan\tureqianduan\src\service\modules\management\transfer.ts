import request from "../../index";

import type {
    ITransitDepotAddData,
    ITransitDepotDeleteData,
    ITransitDepotGetData,
    ITransitDepotUpdateData
} from '@/types/transfer'
import { IRequest } from "../../request/type";

//添加中转站
export function addTransitDepot(params: ITransitDepotAddData) {
    return request.post<IRequest<ITransitDepotAddData>>({
        url: '/datamanagement/addTransitDepot',
        params,
    })
}

//删除中转站
export function deleteTransitDepot(params: ITransitDepotDeleteData) {
    return request.delete<IRequest<ITransitDepotDeleteData>>({
        url: '/datamanagement/deleteTransitDepot',
        params,
    })
}

//更新中转站
export function updateTransitDepot(params: ITransitDepotUpdateData) {
    return request.post<IRequest<ITransitDepotUpdateData>>({
        url: '/datamanagement/updateTransitDepot',
        params,
    })
}

//获取班组信息
export function getTransitDepotInfo() {
    return request.post<IRequest<any>>({
        url: '/datamanagement/getTeamInfo'
    })
}

//获取中转站
export function getTransitDepot(params: ITransitDepotGetData) {
    return request.get<IRequest<ITransitDepotGetData>>({
        url: '/datamanagement/TransitDepotList',
        params,
    })
}