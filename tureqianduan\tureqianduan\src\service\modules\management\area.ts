import request from "../../index";

import type {
    IAreaGetData,
    IAreaAddData,
    IAreaDeleteData,
    IAreaUpdateData
} from '@/types/managerment'
import { IRequest } from "../../request/type";

// 查询班组变更数据
export function queryArea(data: IAreaAddData) {
    return request.post<IRequest<IAreaGetData>>({
        url: '/datamanagement/queryChanges',
        data,
    })
}

// 获取班组信息数据
export function getAreaList(params: IAreaGetData) {
    return request.get<IRequest<IAreaGetData>>({
        url: '/datamanagement/teamList',
        params,
    })
}

// 获取添加班组固定信息
export function getAddAreaInfo() {
    return request.get<IRequest<any>>({
        url: '/datamanagement/getAddTeamInfo',
    })
}


// 添加班组信息
export function addAreaList(data: IAreaAddData) {
    return request.post<IRequest<IAreaAddData>>({
        url: '/datamanagement/saveTeam',
        data
    })
}

// 删除班组信息
export function deleteAreaList(params: IAreaDeleteData) {
    return request.delete<IRequest<IAreaDeleteData>>({
        url: '/datamanagement/deleteTeam',
        params,
    })
}

// 更新班组信息
export function updateAreaList(data: IAreaUpdateData) {
    return request.post<IRequest<IAreaUpdateData>>({
        url: '/datamanagement/updateTeam',
        data
    })
}

// 搜索班组信息
export function getSearchAreaList() {
    return request.post<IRequest<any>>({
        url: '/datamanagement/searchFormInfo',
    })
}

