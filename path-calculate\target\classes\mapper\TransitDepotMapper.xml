<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.TransitDepotMapper">
    <select id="selectByName" resultType="com.ict.ycwl.pathcalculate.pojo.TransitDepot">
        SELECT *
        FROM transit_depot
        WHERE transit_depot_name LIKE CONCAT('%', #{name}, '%');
    </select>

    <select id="selectGroupToTransit" resultType="java.lang.String">
        SELECT GROUP_CONCAT(transit_depot_id) AS transit_depot_ids
        FROM transit_depot
        WHERE is_delete = 0
        GROUP BY group_id;
    </select>

    <select id="selectTransitIds" resultType="java.lang.Long">
        select transit_depot_id from transit_depot where is_delete=0 and status=1
    </select>
</mapper>