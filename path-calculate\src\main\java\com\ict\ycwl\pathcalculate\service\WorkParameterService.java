package com.ict.ycwl.pathcalculate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.ycwl.pathcalculate.pojo.WorkParameter;

import java.util.List;

/**
 * 工作参数配置服务接口
 * 
 * <AUTHOR>
 * @since 2024-08-14
 */
public interface WorkParameterService extends IService<WorkParameter> {

    /**
     * 获取所有工作参数配置
     * 
     * @return 工作参数配置列表
     */
    List<WorkParameter> getAllParameters();

    /**
     * 根据站点名称获取工作参数
     * 
     * @param name 站点名称
     * @return 工作参数配置，如果不存在则返回全局参数
     */
    WorkParameter getParameterByName(String name);

    /**
     * 更新工作参数
     * 
     * @param workParameter 工作参数配置
     * @return 是否更新成功
     */
    boolean updateParameter(WorkParameter workParameter);

    /**
     * 批量更新工作参数
     * 
     * @param workParameters 工作参数配置列表
     * @return 是否更新成功
     */
    boolean batchUpdateParameters(List<WorkParameter> workParameters);

    /**
     * 获取全局参数（韶关市）
     * 
     * @return 全局工作参数配置
     */
    WorkParameter getGlobalParameter();
}
