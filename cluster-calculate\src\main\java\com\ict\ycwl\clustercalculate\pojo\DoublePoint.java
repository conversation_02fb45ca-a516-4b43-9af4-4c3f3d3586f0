package com.ict.ycwl.clustercalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.math3.ml.clustering.Clusterable;

/**
 * <AUTHOR>
 */
@Data
public class DoublePoint implements Clusterable {
    private double[] point;

    public DoublePoint(double[] point) {
        this.point = point;
    }

    @Override
    public double[] getPoint() {
        return point;
    }
}
