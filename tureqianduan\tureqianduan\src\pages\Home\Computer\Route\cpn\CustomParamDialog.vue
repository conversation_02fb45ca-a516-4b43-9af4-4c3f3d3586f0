<template>
  <el-dialog
    v-model="visible"
    width="48%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose"
  >
    <!-- 自定义标题，带 Tooltip -->
    <template #header>
      <div class="dialog-header">
        <span>自定义参数</span>
        <el-tooltip placement="top" effect="dark">
          <img
            src="../../../../../assets/images/空心问号.png"
            alt=""
            width="16px"
            height="16px"
            style="padding-right: 4px"
          />
          <template #content
            >局部参数优先级高于全局:两者不同时<br />局部按自身参数规划，均衡仅限局部;相<br />同时，启动全局均衡规划，90%以上路线<br />时长差异在
            30 分钟内。算法核心是通过<br />参数控制单条路线时长及作用域内路线时<br />长差异，路线数不确定，可反向调整参数<br />以达满意均衡路线数。</template
          >
        </el-tooltip>
      </div>
    </template>

    <!-- 内容区域：可滚动的分站点表单（折叠面板） -->
    <div class="content" v-loading="loading">
      <!-- 滚动列表：每个 item 是 collapse，内含一个表单 -->
      <el-scrollbar class="list-scroll" height="60vh">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="site in sites"
            :key="site.id"
            :name="site.id"
          >
            <template #title>
              <div class="item-title">{{ site.name }}</div>
            </template>
            <el-form
              :model="site.form"
              :rules="formRules"
              label-width="140px"
              label-position="left"
              class="item-form"
              ref="formRef"
            >
              <div class="form-row">
                <el-form-item
                  label="最大工作时间(小时)"
                  prop="maxWorkHours"
                  class="param-item"
                >
                  <el-input
                    v-model="site.form.maxWorkHours"
                    placeholder="1-10, 可保留一位小数"
                    class="param-input"
                  />
                </el-form-item>
                <el-form-item
                  label="可动区间(小时)"
                  prop="flexibleRange"
                  class="param-item"
                >
                  <el-input
                    v-model="site.form.flexibleRange"
                    placeholder="0-1, 可保留一位小数"
                    class="param-input"
                  />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item
                  label="最优工作时间(小时)"
                  prop="optimalWorkHours"
                  class="param-item"
                >
                  <el-input
                    v-model="site.form.optimalWorkHours"
                    placeholder="1-10, 可保留一位小数"
                    class="param-input"
                  />
                </el-form-item>
                <el-form-item
                  label="立即停止阈值"
                  prop="stopThreshold"
                  class="param-item"
                >
                  <el-input
                    v-model="site.form.stopThreshold"
                    placeholder="0-1, 可保留三位小数"
                    class="param-input"
                  />
                </el-form-item>
              </div>
              <div class="apply-section" v-if="site.id === 1">
                <el-divider class="section-divider" />
                <div class="apply-action">
                  <el-button
                    type="primary"
                    plain
                    @click="applyShaoguanToAll"
                    class="apply-btn"
                  >
                    <el-icon><CopyDocument /></el-icon>
                    全部应用
                  </el-button>
                </div>
              </div>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
    </div>

    <template #footer>
      <div class="footer-actions">
        <el-button type="primary" @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">保存设置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { ref, defineExpose, watch } from "vue";
  import { ElMessage } from "element-plus";
  import { CopyDocument } from "@element-plus/icons-vue";
  import { getAllWorkParameters, updateWorkParametersBatch } from "@/service/modules/cluster";

  const visible = ref(false);

  type Site = {
    id: string | number;
    name: string;
    form: {
      maxWorkHours: number;
      flexibleRange: number;
      optimalWorkHours: number;
      stopThreshold: number;
    };
  };

  // 站点数据
  const sites = ref<Site[]>([]);
  const loading = ref(false);

  // 加载站点数据
  const loadSites = async () => {
    loading.value = true;
    try {
      const res: any = await getAllWorkParameters();
      if (res.code === 200 && res.data) {
        sites.value = res.data.map((item: any) => ({
          id: item.id,
          name: item.name,
          form: {
            maxWorkHours: item.maxWorkHours || 8.0,
            flexibleRange: item.flexibleRange || 0.5,
            optimalWorkHours: item.optimalWorkHours || 6.5,
            stopThreshold: item.stopThreshold || 0.85,
          },
        }));
      } else {
        ElMessage.error(res.message || "获取站点数据失败");
        // 使用默认数据
        sites.value = getDefaultSites();
      }
    } catch (error) {
      console.error("加载站点数据失败:", error);
      ElMessage.error("加载站点数据失败");
      // 使用默认数据
      sites.value = getDefaultSites();
    } finally {
      loading.value = false;
    }
  };

  // 默认站点数据
  const getDefaultSites = (): Site[] => [];

  // 默认只有第一个展开
  const activeNames = ref<(string | number)[]>([1]);

  // 表单引用
  const formRef = ref([]);
  
  // 表单验证规则
  const formRules = {
    maxWorkHours: [
      { required: true, message: "请输入最大工作时间", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 1 || num > 10) {
            callback(new Error("请输入1-10之间的数字"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
    flexibleRange: [
      { required: true, message: "请输入可动区间", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 0 || num > 1) {
            callback(new Error("请输入0-1之间的数字"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
    optimalWorkHours: [
      { required: true, message: "请输入最优工作时间", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 1 || num > 10) {
            callback(new Error("请输入1-10之间的数字"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
    stopThreshold: [
      { required: true, message: "请输入立即停止阈值", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 0 || num > 1) {
            callback(new Error("请输入0-1之间的数字"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
  };

  // 监听 visible 变化，弹窗打开时加载数据
  watch(visible, (newVal) => {
    if (newVal) {
      loadSites();
    }
  });

  function openDialog() {
    visible.value = true;
  }
  function handleClose() {
    visible.value = false;
  }
  function handleConfirm() {
    // 验证所有表单
    const validatePromises = formRef.value.map(form => {
      return new Promise((resolve) => {
        if (form) {
          form.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    });

    Promise.all(validatePromises).then(results => {
      const allValid = results.every(valid => valid);
      
      if (!allValid) {
        ElMessage.error('请检查所有站点的参数填写是否正确');
        return;
      }

      // 准备批量更新的数据
      const updateData = sites.value.map(site => ({
        id: site.id,
        name: site.name,
        maxWorkHours: parseFloat(site.form.maxWorkHours),
        flexibleRange: parseFloat(site.form.flexibleRange),
        optimalWorkHours: parseFloat(site.form.optimalWorkHours),
        stopThreshold: parseFloat(site.form.stopThreshold)
      }));

      // 调用批量更新接口
      updateWorkParametersBatch(updateData)
        .then(res => {
          if (res.code === 200) {
            ElMessage.success('参数保存成功');
            visible.value = false;
          } else {
            ElMessage.error(res.message || '参数保存失败');
          }
        })
        .catch(error => {
          console.error('保存参数失败:', error);
          ElMessage.error('保存参数失败，请稍后重试');
        });
    });
  }

  function applyShaoguanToAll() {
    const shaoguanForm = sites.value[0].form;

    // 验证韶关市中转站的数据
    const validateField = (value, validator, fieldName) => {
      let isValid = true;
      validator({}, value, (valid) => {
        if (!valid) isValid = false;
      });
      return isValid;
    };

    const errors = [];

    if (
      !validateField(
        shaoguanForm.maxWorkHours,
        formRules.maxWorkHours[1].validator,
        "最大工作时间"
      )
    ) {
      errors.push("最大工作时间");
    }
    if (
      !validateField(
        shaoguanForm.flexibleRange,
        formRules.flexibleRange[1].validator,
        "可动区间"
      )
    ) {
      errors.push("可动区间");
    }
    if (
      !validateField(
        shaoguanForm.optimalWorkHours,
        formRules.optimalWorkHours[1].validator,
        "最优工作时间"
      )
    ) {
      errors.push("最优工作时间");
    }
    if (
      !validateField(
        shaoguanForm.stopThreshold,
        formRules.stopThreshold[1].validator,
        "立即停止阈值"
      )
    ) {
      errors.push("立即停止阈值");
    }

    if (errors.length > 0) {
      ElMessage.error(
        `韶关市中转站的 ${errors.join("、")} 填写有误，请检查后再应用`
      );
      return;
    }

    ElMessageBox.confirm(
      "该操作将覆盖所有局部设置的参数，确定要应用吗？",
      "确认提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    ).then(() => {
      sites.value.slice(1).forEach((site) => {
        site.form.maxWorkHours = shaoguanForm.maxWorkHours;
        site.form.flexibleRange = shaoguanForm.flexibleRange;
        site.form.optimalWorkHours = shaoguanForm.optimalWorkHours;
        site.form.stopThreshold = shaoguanForm.stopThreshold;
      });
      ElMessage.success("已将韶关市参数应用到所有站点");
    }).catch(() => {
      // 用户取消操作，不做任何处理
    });
  }

  defineExpose({ openDialog });
</script>

<style scoped lang="less">
  .dialog-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 16px;
  }
  .tip-icon {
    cursor: pointer;
  }
  .content {
    display: flex;
    flex-direction: column;
  }
  .global-section {
    padding: 6px 10px 0 10px;
  }
  .global-form .range-box {
    display: inline-flex;
    align-items: center;
    gap: 6px;
  }
  .range-split {
    padding: 0 4px;
  }
  .list-scroll {
    margin-top: 6px;
  }
  .item-title {
    font-weight: 600;
  }
  .item-form {
    padding: 6px 10px 0 10px;
  }
  .footer-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
  .form-row {
    display: flex;
    gap: 32px;
    margin-bottom: 24px;
  }

  .form-row .el-form-item {
    flex: 1;
    margin-bottom: 0;
  }

  .param-item {
    margin-bottom: 0;
  }

  .el-collapse {
    :deep(.el-collapse-item) {
      padding: 0;
    }
    :deep(.el-collapse-item:before) {
      display: none;
    }
    :deep(.el-collapse-item__header) {
      padding: 10px;
    }
  }

  :deep(.param-item .el-form-item__label) {
    white-space: nowrap;
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    padding-right: 8px;
  }

  :deep(.param-input .el-input__wrapper) {
    height: 32px;
    padding: 0 8px;
  }

  :deep(.param-input .el-input__inner) {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }

  :deep(.el-collapse-item__header) {
    background-color: #85adeb;
    color: #fff;
    border-radius: 4px;
    margin-bottom: 2px;
  }

  :deep(.el-collapse-item__header.is-active) {
    background-color: #85adeb;
  }

  :deep(.el-collapse-item__content) {
    padding: 16px;
    border-radius: 0 0 4px 4px;
  }

  .apply-section {
    margin-top: 20px;
  }

  :deep(.section-divider) {
    margin: 0 0 16px 0;
  }

  .apply-action {
    display: flex;
    justify-content: center;
  }

  :deep(.apply-btn) {
    padding: 8px 20px;
    font-size: 13px;
    background-color: #95c6e5;
    border-color: #95c6e5;
    color: #fff;
  }

  :deep(.apply-btn:hover) {
    background-color: #7fb5d9;
    border-color: #7fb5d9;
  }
  .ml8 {
    margin-left: 8px;
  }
</style>
