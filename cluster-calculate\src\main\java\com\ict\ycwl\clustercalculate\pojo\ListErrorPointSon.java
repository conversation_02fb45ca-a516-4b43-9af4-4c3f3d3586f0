package com.ict.ycwl.clustercalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListErrorPointSon {
    private double longitude;
    private double latitude;
    private String name;
    private String customerCode;

    private String specialType;
    private String remark;

    public ListErrorPointSon(Double longitude, Double latitude, String storeAddress) {
        this.latitude=latitude;
        this.longitude=longitude;
        this.name=storeAddress;
    }
}
