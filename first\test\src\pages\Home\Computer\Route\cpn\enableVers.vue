<template>
  <el-dialog
    v-model="dialogVisible"
    :title="versionName"
    width="500px"
    :close-on-click-modal="false"
    :show-close="true"
    class="version-diff-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="version-info">
        <div
          class="version-dot"
          :style="{ backgroundColor: status ? 'green' : 'red' }"
        ></div>
        <div class="version-name">{{ versionName }}</div>
        <div class="update-time">更新时间: {{ updateTime }}</div>
      </div>

      <div class="diff-message">
        选中版本的基础数据与当前版本存在差异，是否继续启用？
        <div class="note">* 如需调整可先导出路径详情查看差异。</div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleContinue" class="continue-btn"
          >不调整，直接启用</el-button
        >
        <el-button @click="closeDialog" class="cancel-btn">取消</el-button>
      </div>
    </template>
  </el-dialog>
  <ConfirmDialog
    ref="confirmDialogRef"
    :versionId="versionId"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>

<script setup lang="ts">
  import { ref, watch } from "vue";
  import { ElDialog, ElButton } from "element-plus";
  import ConfirmDialog from "./confirmEnableVers.vue";
  import { useVersStore } from "@/store/ver.ts";
  const confirmDialogRef = ref();
  const verStore = useVersStore();

  const showConfirmDialog = () => {
    // 通过ref直接调用组件暴露的方法
    confirmDialogRef.value.openDialog();
  };

  const handleConfirm = (id: string) => {
    console.log("确认启用版本:", id);
    emit("continue", props.versionId);
    // 处理确认逻辑
  };

  const handleCancel = () => {
    console.log("取消操作");
    // 处理取消逻辑
  };

  interface Props {
    versionName: string;
    versionId: string;
    status: string;
    updateTime: string;
  }

  interface Emits {
    (e: "continue", id: string): void;
    (e: "cancel"): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    versionName: "",
    versionId: "",
    status: "",
    updateTime: "",
  });

  const emit = defineEmits<Emits>();

  // 对话框可见性
  const dialogVisible = ref(false);

  // 添加对props的监听
  watch(
    () => props.versionName,
    (newVal) => {
      // 当props更新时，可以在这里执行一些操作
    }
  );

  watch(
    () => props.status,
    (newVal) => {}
  );

  watch(
    () => props.updateTime,
    (newVal) => {}
  );

  watch(
    () => props.versionId,
    (newVal) => {}
  );

  // 打开对话框
  const openDialog = () => {
    if (props.status) {
      // 如果状态为1，直接打开确认对话框
      showConfirmDialog();
    } else {
      // 否则显示当前对话框
      dialogVisible.value = true;
    }
  };
  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false;
    emit("cancel");
  };

  // 处理关闭事件
  const handleClose = () => {
    closeDialog();
  };

  // 继续按钮点击事件
  const handleContinue = () => {
    console.log(props.updateTime);
    emit("continue", props.versionId);
    closeDialog();
    // showConfirmDialog();
  };

  // 暴露方法给父组件
  defineExpose({
    openDialog,
    closeDialog,
    dialogVisible,
  });
</script>

<style lang="less" scoped>
  .version-diff-dialog {
    :deep(.el-dialog) {
      background-color: #0a1929;
      border: 1px solid #1e3a5f;
      border-radius: 4px;
      max-width: 90%;
      margin: 0 auto;

      .el-dialog__header {
        margin: 0;
        padding: 15px 20px;
        color: white;
        font-size: 16px;
        border-bottom: 1px solid #1e3a5f;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .el-dialog__title {
          color: white;
        }

        .el-dialog__close {
          color: #a0a0a0;
          font-size: 20px;

          &:hover {
            color: white;
          }
        }
      }

      .el-dialog__body {
        padding: 20px;
        color: white;
      }

      .el-dialog__footer {
        padding: 10px 20px 20px;
        border-top: none;
      }
    }

    .dialog-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .version-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .version-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: red;
          margin-right: 10px;
        }

        .version-name {
          font-weight: bold;
          margin-right: 15px;
        }

        .update-time {
          font-size: 14px;
          color: #a9ced9;
        }
      }

      .diff-message {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 20px;

        .note {
          font-size: 14px;
          color: #a9ced9;
          margin-top: 10px;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 20px;

      .continue-btn,
      .cancel-btn {
        min-width: 140px;
        height: 40px;
        border-radius: 4px;
      }

      .continue-btn {
        background-color: #a4cddf;
        border-color: #a4cddf;
        color: #0a1929;

        &:hover {
          background-color: #8bbfd5;
          border-color: #8bbfd5;
        }
      }

      .cancel-btn {
        background-color: #a4cddf;
        border-color: #a4cddf;
        color: #0a1929;

        &:hover {
          background-color: #8bbfd5;
          border-color: #8bbfd5;
        }
      }
    }
  }
</style>
