import { defineStore } from "pinia";

import {
  getSystemInfo,
  updateSystemInfo,
  deleteSystemInfo,
} from "@/service/modules/management/system";
import { MAP_API_KEY, SECURITY_CODE } from "@/utils/getMapKey";
export interface ISystemDelete {
  id: number;
}

export const systemStore = defineStore("system", () => {
  const systemInfo = ref<any>({});

  async function getSystemData() {
    const res = await getSystemInfo();
    systemInfo.value = res.data;
  }

  async function updateSystemData(data: any) {
    const apikey = MAP_API_KEY;
    console.log("apikey", apikey);
    const res = await updateSystemInfo({ apiKey: apikey }, data);
    return res;
  }

  async function deleteSystemData(id: ISystemDelete) {
    const res: any = await deleteSystemInfo(id);
    return res;
  }

  return {
    systemInfo,
    getSystemData,
    updateSystemData,
    deleteSystemData,
  };
});
