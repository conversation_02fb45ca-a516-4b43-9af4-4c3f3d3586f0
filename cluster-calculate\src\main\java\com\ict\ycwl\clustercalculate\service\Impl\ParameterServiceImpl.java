package com.ict.ycwl.clustercalculate.service.Impl;

import com.ict.ycwl.clustercalculate.pojo.ParameterListManager;
import com.ict.ycwl.clustercalculate.service.ParameterService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 */
@Service
public class ParameterServiceImpl implements ParameterService {
    private static List<ParameterListManager> listManagers = new CopyOnWriteArrayList<>();

    // 静态初始化块
    static {
        ParameterListManager p1 = new ParameterListManager("中转站信息改变",0,new ArrayList<>());
        ParameterListManager p2 = new ParameterListManager("商铺信息改变",0,new ArrayList<>());
        ParameterListManager p3 = new ParameterListManager("系统参数改变",0,new ArrayList<>());
        ParameterListManager p4 = new ParameterListManager("地理信息改变",0,new ArrayList<>());
        ParameterListManager p5 = new ParameterListManager("异常情况发生",0,new ArrayList<>());

        listManagers.add(p1);
        listManagers.add(p2);
        listManagers.add(p3);
        listManagers.add(p4);
        listManagers.add(p5);
    }

    @Override
    public void updateList(String managerName, String data) {
        for (ParameterListManager p : listManagers) {
            if (p.getName().equals(managerName)) {
                int number = p.getNumber();
                p.getData().add(data);
                p.setNumber(number + 1);
            }
        }
    }

    @Override
    public List<ParameterListManager> getInformationList() {
        return listManagers;
    }

    @Override
    public void clearInformation() {
        for (ParameterListManager p : listManagers) {
            p.setNumber(0);
            p.setData(new ArrayList<>());
        }
    }

}
