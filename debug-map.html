<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        #mapContainer {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin-top: 10px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 地图功能调试工具</h1>
        
        <div class="section">
            <h2>1. 环境变量检查</h2>
            <button class="button" onclick="checkEnvironment()">检查环境变量</button>
            <div id="env-result"></div>
        </div>
        
        <div class="section">
            <h2>2. 高德地图API测试</h2>
            <button class="button" onclick="testMapAPI()">测试地图API</button>
            <div id="map-api-result"></div>
            <div id="mapContainer"></div>
        </div>
        
        <div class="section">
            <h2>3. 后端API测试</h2>
            <button class="button" onclick="testBackendAPIs()">测试后端API</button>
            <div id="backend-result"></div>
        </div>
        
        <div class="section">
            <h2>4. 地图数据加载测试</h2>
            <button class="button" onclick="testMapDataLoading()">测试地图数据加载</button>
            <div id="map-data-result"></div>
        </div>

        <div class="section">
            <h2>5. 地图交互测试</h2>
            <button class="button" onclick="testMapInteraction()">测试地图交互功能</button>
            <button class="button" onclick="clearMapCache()">清除地图缓存</button>
            <div id="map-interaction-result"></div>
        </div>

        <div class="section">
            <h2>6. 问题诊断</h2>
            <button class="button" onclick="runFullDiagnosis()">运行完整诊断</button>
            <div id="diagnosis-result"></div>
        </div>
    </div>

    <script>
        // 模拟环境变量（实际项目中这些来自.env文件）
        const ENV_VARS = {
            VITE_MAP_KEY: '004bb8268ef0cff3264e9b2a8816e29c',
            VITE_MAP_API_KEY: '3729e38b382749ba3a10bae7539e0d9a',
            VITE_SECURITY_CODE: '92707992ffdad18bfbf2f31e9bd158bd',
            VITE_BASE_URL: 'http://localhost:8080'
        };

        function checkEnvironment() {
            const result = document.getElementById('env-result');
            let html = '<h3>环境变量检查结果:</h3>';
            
            for (const [key, value] of Object.entries(ENV_VARS)) {
                const status = value && value !== 'undefined' ? '✅' : '❌';
                const displayValue = key.includes('KEY') || key.includes('CODE') ? 
                    value.substring(0, 8) + '...' : value;
                html += `<p>${status} ${key}: ${displayValue}</p>`;
            }
            
            result.innerHTML = html;
        }

        async function testMapAPI() {
            const result = document.getElementById('map-api-result');
            result.innerHTML = '<p>🔄 正在测试高德地图API...</p>';
            
            try {
                // 动态加载高德地图API
                const script = document.createElement('script');
                script.src = `https://webapi.amap.com/maps?v=2.0&key=${ENV_VARS.VITE_MAP_KEY}&callback=initMap`;
                
                // 设置安全密钥
                window._AMapSecurityConfig = {
                    securityJsCode: ENV_VARS.VITE_SECURITY_CODE,
                };
                
                // 定义地图初始化回调
                window.initMap = function() {
                    try {
                        const map = new AMap.Map('mapContainer', {
                            zoom: 10,
                            center: [113.767587, 24.718014], // 韶关市中心
                            viewMode: '3D'
                        });

                        // 保存地图实例供后续测试使用
                        window.mapInstance = map;

                        result.innerHTML = '<p class="success">✅ 高德地图API加载成功！地图已初始化。</p>';

                        // 添加一个标记点测试
                        const marker = new AMap.Marker({
                            position: [113.767587, 24.718014],
                            title: '韶关市'
                        });
                        map.add(marker);

                    } catch (error) {
                        result.innerHTML = `<p class="error">❌ 地图初始化失败: ${error.message}</p>`;
                    }
                };
                
                script.onerror = function() {
                    result.innerHTML = '<p class="error">❌ 高德地图API加载失败，请检查网络连接和API密钥</p>';
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        }

        async function testBackendAPIs() {
            const result = document.getElementById('backend-result');
            result.innerHTML = '<p>🔄 正在测试后端API...</p>';
            
            const apis = [
                { name: 'getConvex', url: '/pathcalculate/path/getConvex?apiKey=' + ENV_VARS.VITE_MAP_API_KEY },
                { name: 'getAllColourConvex', url: '/pathcalculate/path/getAllColourConvex' },
                { name: 'getConvexPoint', url: '/pathcalculate/path/getConvexPoint' },
                { name: 'getTransitDepotName', url: '/pathcalculate/path/getTransitDepotName' }
            ];
            
            let html = '<h3>后端API测试结果:</h3>';
            
            for (const api of apis) {
                try {
                    const response = await fetch(ENV_VARS.VITE_BASE_URL + api.url);
                    const status = response.status;
                    const statusText = response.ok ? '✅ 成功' : '❌ 失败';
                    
                    html += `<p>${statusText} ${api.name}: HTTP ${status}</p>`;
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.code === 200) {
                            html += `<p style="margin-left: 20px;">📊 数据: ${JSON.stringify(data).substring(0, 100)}...</p>`;
                        } else {
                            html += `<p style="margin-left: 20px;" class="warning">⚠️ 业务错误: ${data.msg || '未知错误'}</p>`;
                        }
                    }
                } catch (error) {
                    html += `<p class="error">❌ ${api.name}: 网络错误 - ${error.message}</p>`;
                }
            }
            
            result.innerHTML = html;
        }

        async function testMapDataLoading() {
            const result = document.getElementById('map-data-result');
            result.innerHTML = '<p>🔄 正在测试地图数据加载...</p>';

            try {
                // 测试获取凸包数据
                const convexResponse = await fetch(ENV_VARS.VITE_BASE_URL + '/pathcalculate/path/getConvex?apiKey=' + ENV_VARS.VITE_MAP_API_KEY);
                const convexData = await convexResponse.json();

                // 测试获取颜色数据
                const colorResponse = await fetch(ENV_VARS.VITE_BASE_URL + '/pathcalculate/path/getAllColourConvex');
                const colorData = await colorResponse.json();

                // 测试获取点数据
                const pointResponse = await fetch(ENV_VARS.VITE_BASE_URL + '/pathcalculate/path/getConvexPoint');
                const pointData = await pointResponse.json();

                let html = '<h3>地图数据加载测试结果:</h3>';

                if (convexData.code === 200) {
                    html += `<p class="success">✅ 凸包数据: ${convexData.data ? convexData.data.length : 0} 条记录</p>`;
                    if (convexData.data && convexData.data.length > 0) {
                        html += `<p style="margin-left: 20px;">示例数据: ${JSON.stringify(convexData.data[0]).substring(0, 200)}...</p>`;
                    }
                } else {
                    html += `<p class="error">❌ 凸包数据加载失败: ${convexData.msg}</p>`;
                }

                if (colorData.code === 200) {
                    html += `<p class="success">✅ 颜色数据: ${colorData.data ? colorData.data.length : 0} 个班组</p>`;
                    if (colorData.data && colorData.data.length > 0) {
                        html += `<p style="margin-left: 20px;">班组1色块数: ${colorData.data[0] ? colorData.data[0].length : 0}</p>`;
                    }
                } else {
                    html += `<p class="error">❌ 颜色数据加载失败: ${colorData.msg}</p>`;
                }

                if (pointData.code === 200) {
                    html += `<p class="success">✅ 点数据: ${pointData.data ? pointData.data.length : 0} 个点</p>`;
                    if (pointData.data && pointData.data.length > 0) {
                        html += `<p style="margin-left: 20px;">示例点: ${JSON.stringify(pointData.data[0]).substring(0, 150)}...</p>`;
                    }
                } else {
                    html += `<p class="error">❌ 点数据加载失败: ${pointData.msg}</p>`;
                }

                // 检查localStorage缓存
                const cachedConvex = localStorage.getItem('convex');
                const cachedColor = localStorage.getItem('convexColor');

                html += '<h4>缓存状态:</h4>';
                html += `<p>凸包缓存: ${cachedConvex ? '✅ 存在' : '❌ 不存在'}</p>`;
                html += `<p>颜色缓存: ${cachedColor ? '✅ 存在' : '❌ 不存在'}</p>`;

                // 模拟地图渲染过程
                html += '<h4>地图渲染模拟:</h4>';
                if (convexData.code === 200 && colorData.code === 200 && pointData.code === 200) {
                    html += '<p class="success">✅ 所有数据就绪，可以进行地图渲染</p>';

                    // 检查数据结构
                    if (convexData.data && convexData.data.length > 0) {
                        const firstRoute = convexData.data[0];
                        if (firstRoute.convex && Array.isArray(firstRoute.convex)) {
                            html += '<p style="margin-left: 20px;">✅ 凸包坐标数据结构正确</p>';
                        } else {
                            html += '<p style="margin-left: 20px;" class="warning">⚠️ 凸包坐标数据结构异常</p>';
                        }
                    }

                    if (colorData.data && colorData.data.length > 0) {
                        html += '<p style="margin-left: 20px;">✅ 颜色数据结构正确</p>';
                    }

                    if (pointData.data && pointData.data.length > 0) {
                        const firstPoint = pointData.data[0];
                        if (firstPoint.longitude && firstPoint.latitude) {
                            html += '<p style="margin-left: 20px;">✅ 点坐标数据结构正确</p>';
                        } else {
                            html += '<p style="margin-left: 20px;" class="warning">⚠️ 点坐标数据结构异常</p>';
                        }
                    }
                } else {
                    html += '<p class="error">❌ 数据不完整，无法进行地图渲染</p>';
                }

                result.innerHTML = html;

            } catch (error) {
                result.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        }

        async function testMapInteraction() {
            const result = document.getElementById('map-interaction-result');
            result.innerHTML = '<p>🔄 正在测试地图交互功能...</p>';

            let html = '<h3>地图交互测试结果:</h3>';

            try {
                // 检查地图是否已初始化
                if (typeof AMap !== 'undefined') {
                    html += '<p class="success">✅ 高德地图API已加载</p>';

                    // 测试地图容器
                    const container = document.getElementById('mapContainer');
                    if (container) {
                        html += '<p class="success">✅ 地图容器存在</p>';

                        // 检查地图实例
                        if (window.mapInstance) {
                            html += '<p class="success">✅ 地图实例已创建</p>';

                            // 测试地图事件
                            try {
                                window.mapInstance.on('click', function(e) {
                                    console.log('地图点击事件:', e.lnglat);
                                });
                                html += '<p class="success">✅ 地图事件监听正常</p>';
                            } catch (e) {
                                html += '<p class="error">❌ 地图事件监听失败: ' + e.message + '</p>';
                            }

                            // 测试添加标记
                            try {
                                const marker = new AMap.Marker({
                                    position: [113.767587, 24.718014],
                                    title: '测试标记'
                                });
                                window.mapInstance.add(marker);
                                html += '<p class="success">✅ 标记添加功能正常</p>';
                            } catch (e) {
                                html += '<p class="error">❌ 标记添加失败: ' + e.message + '</p>';
                            }

                        } else {
                            html += '<p class="warning">⚠️ 地图实例未创建，请先测试地图API</p>';
                        }
                    } else {
                        html += '<p class="error">❌ 地图容器不存在</p>';
                    }
                } else {
                    html += '<p class="error">❌ 高德地图API未加载</p>';
                }

                result.innerHTML = html;

            } catch (error) {
                result.innerHTML = `<p class="error">❌ 测试失败: ${error.message}</p>`;
            }
        }

        function clearMapCache() {
            try {
                localStorage.removeItem('convex');
                localStorage.removeItem('convexColor');
                document.getElementById('map-interaction-result').innerHTML = '<p class="success">✅ 地图缓存已清除</p>';
            } catch (error) {
                document.getElementById('map-interaction-result').innerHTML = `<p class="error">❌ 清除缓存失败: ${error.message}</p>`;
            }
        }

        async function runFullDiagnosis() {
            const result = document.getElementById('diagnosis-result');
            result.innerHTML = '<p>🔄 正在运行完整诊断...</p>';

            let html = '<h3>完整诊断报告:</h3>';
            let issues = [];
            let suggestions = [];

            try {
                // 1. 检查环境变量
                html += '<h4>1. 环境变量检查</h4>';
                for (const [key, value] of Object.entries(ENV_VARS)) {
                    if (!value || value === 'undefined') {
                        issues.push(`环境变量 ${key} 未设置或为空`);
                        html += `<p class="error">❌ ${key}: 未设置</p>`;
                    } else {
                        html += `<p class="success">✅ ${key}: 已设置</p>`;
                    }
                }

                // 2. 检查网络连接
                html += '<h4>2. 网络连接检查</h4>';
                try {
                    const response = await fetch(ENV_VARS.VITE_BASE_URL + '/pathcalculate/path/getTransitDepotName');
                    if (response.ok) {
                        html += '<p class="success">✅ 后端服务连接正常</p>';
                    } else {
                        issues.push(`后端服务响应异常: HTTP ${response.status}`);
                        html += `<p class="error">❌ 后端服务响应异常: HTTP ${response.status}</p>`;
                    }
                } catch (e) {
                    issues.push('无法连接到后端服务');
                    html += '<p class="error">❌ 无法连接到后端服务</p>';
                }

                // 3. 检查高德地图API
                html += '<h4>3. 高德地图API检查</h4>';
                if (typeof AMap !== 'undefined') {
                    html += '<p class="success">✅ 高德地图API已加载</p>';
                } else {
                    issues.push('高德地图API未加载');
                    html += '<p class="error">❌ 高德地图API未加载</p>';
                    suggestions.push('检查网络连接和API密钥是否正确');
                }

                // 4. 生成建议
                html += '<h4>4. 问题总结和建议</h4>';
                if (issues.length === 0) {
                    html += '<p class="success">🎉 未发现明显问题！系统应该可以正常工作。</p>';
                } else {
                    html += '<p class="error">发现以下问题:</p>';
                    html += '<ul>';
                    issues.forEach(issue => {
                        html += `<li class="error">${issue}</li>`;
                    });
                    html += '</ul>';

                    html += '<p class="warning">建议解决方案:</p>';
                    html += '<ul>';

                    if (issues.some(i => i.includes('环境变量'))) {
                        suggestions.push('检查.env.prd文件是否存在且包含正确的API密钥');
                    }
                    if (issues.some(i => i.includes('后端服务'))) {
                        suggestions.push('确保后端服务已启动并运行在正确的端口');
                    }
                    if (issues.some(i => i.includes('高德地图'))) {
                        suggestions.push('检查高德地图API密钥是否有效，是否有足够的调用配额');
                    }

                    suggestions.forEach(suggestion => {
                        html += `<li class="warning">${suggestion}</li>`;
                    });
                    html += '</ul>';
                }

                result.innerHTML = html;

            } catch (error) {
                result.innerHTML = `<p class="error">❌ 诊断失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
