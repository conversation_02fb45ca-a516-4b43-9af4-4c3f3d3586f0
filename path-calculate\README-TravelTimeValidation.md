# Travel Time数据验证测试

## 概述
这个测试类用于验证OSRM生成的280万条travel_time数据的准确性，通过高德地图API进行小样本抽样对比。

## 快速开始

### 1. 配置高德API Key
在 `TravelTimeValidationTest.java` 中修改：
```java
private static final String AMAP_API_KEY = "your_actual_amap_api_key_here";
```

### 2. 运行测试
```bash
# 方法1：Maven命令
mvn test -Dtest=TravelTimeValidationTest#testTravelTimeAccuracy

# 方法2：在IDEA中直接运行测试方法
```

### 3. 查看结果
测试完成后会在 `target/test-results/` 目录生成详细报告。

## 测试配置

### 默认配置
- **抽样数量**：50条（避免API配额超限）
- **请求间隔**：200ms（避免频率限制）
- **允许误差**：20%（考虑算法差异）

### 自定义配置
```java
private static final int SAMPLE_SIZE = 30;           // 减少样本数量
private static final int REQUEST_DELAY_MS = 300;     // 增加请求间隔
private static final double TOLERANCE_PERCENTAGE = 15.0; // 降低误差容忍度
```

## 高德API申请

### 1. 注册账号
访问 [高德开放平台](https://lbs.amap.com/) 注册开发者账号

### 2. 创建应用
- 应用类型：Web服务
- 服务平台：Web端
- 获取API Key

### 3. 配额说明
- 个人开发者：每日5000次免费
- 企业开发者：根据套餐不同
- 建议控制测试在50次以内

## 结果解读

### 通过率标准
- **≥80%**：数据质量良好 ✅
- **60-80%**：数据质量一般 ⚠️
- **<60%**：数据质量较差 ❌

### 常见误差原因
1. **路径选择差异**：不同算法的路径规划策略
2. **实时路况**：高德考虑实时路况，OSRM使用静态数据
3. **地图版本**：底层地图数据的版本差异
4. **计算模型**：时间估算的数学模型不同

### 报告示例
```
Travel Time数据验证报告
==================================================
抽样数量: 50
验证通过: 42
验证失败: 6
请求错误: 2
通过率: 84.0%

统计摘要:
平均误差: 12.3%
最大误差: 35.2%
最小误差: 1.1%

结论:
✅ OSRM数据质量良好，通过率84.0%
```

## 注意事项

### 1. 网络要求
- 确保网络连接稳定
- 能够访问高德API服务

### 2. 数据库要求
- travel_time表存在且有数据
- 数据库连接配置正确

### 3. API限制
- 控制请求频率避免被限流
- 监控API配额使用情况

### 4. 结果分析
- 关注系统性偏差而非个别异常
- 结合业务场景评估数据可用性

## 故障排除

### 编译错误
```bash
# 确保Maven依赖完整
mvn clean compile test-compile
```

### 运行时错误
- 检查数据库连接
- 验证API Key有效性
- 确认网络连接正常

### 结果异常
- 检查坐标数据格式
- 确认时间单位一致
- 验证API返回格式

## 扩展功能

### 分区域验证
可以按中转站或地理区域分别验证数据质量。

### 批量验证
对于大规模验证，可以考虑使用批量API或异步处理。

### 多API对比
可以集成百度、腾讯等其他地图API进行交叉验证。

---

**重要提醒**：
1. 请先申请高德API Key并配置到测试类中
2. 建议先用小样本（10-20条）测试API连通性
3. 关注API配额使用情况，避免超限
4. 测试结果仅供参考，需结合实际业务场景分析
