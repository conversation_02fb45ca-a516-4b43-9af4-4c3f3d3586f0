package com.ict.ycwl.pathcalculate.controller;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.pojo.WorkParameter;
import com.ict.ycwl.pathcalculate.service.WorkParameterService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作参数配置控制器
 * 
 * <AUTHOR>
 * @since 2024-08-14
 */
@Api(tags = "工作参数配置API")
@Slf4j
@Validated
@RestController
@RequestMapping("/workParameter")
public class WorkParameterController {

    @Autowired
    private WorkParameterService workParameterService;

    @ApiOperation(value = "获取所有工作参数配置")
    @GetMapping("/list")
    public AjaxResult getAllParameters() {
        try {
            log.info("获取所有工作参数配置");
            List<WorkParameter> parameters = workParameterService.getAllParameters();
            return AjaxResult.success("获取成功", parameters);
        } catch (Exception e) {
            log.error("获取工作参数配置失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取工作参数配置失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "根据站点名称获取工作参数")
    @GetMapping("/getByName")
    public AjaxResult getParameterByName(
            @ApiParam(value = "站点名称", required = true, example = "韶关市")
            @RequestParam("name") String name) {
        try {
            log.info("根据站点名称获取工作参数: {}", name);
            WorkParameter parameter = workParameterService.getParameterByName(name);
            return AjaxResult.success("获取成功", parameter);
        } catch (Exception e) {
            log.error("获取工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取工作参数失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新工作参数")
    @PostMapping("/update")
    public AjaxResult updateParameter(
            @ApiParam(value = "工作参数配置", required = true)
            @Validated @RequestBody WorkParameter workParameter) {
        try {
            log.info("更新工作参数: {}", workParameter.getName());
            boolean success = workParameterService.updateParameter(workParameter);
            if (success) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新工作参数失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "批量更新工作参数")
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateParameters(
            @ApiParam(value = "工作参数配置列表", required = true)
            @Validated @RequestBody List<WorkParameter> workParameters) {
        try {
            log.info("批量更新工作参数，数量: {}", workParameters.size());
            boolean success = workParameterService.batchUpdateParameters(workParameters);
            if (success) {
                return AjaxResult.success("批量更新成功");
            } else {
                return AjaxResult.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量更新工作参数失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取全局参数")
    @GetMapping("/global")
    public AjaxResult getGlobalParameter() {
        try {
            log.info("获取全局工作参数");
            WorkParameter parameter = workParameterService.getGlobalParameter();
            return AjaxResult.success("获取成功", parameter);
        } catch (Exception e) {
            log.error("获取全局工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取全局工作参数失败: " + e.getMessage());
        }
    }
}
