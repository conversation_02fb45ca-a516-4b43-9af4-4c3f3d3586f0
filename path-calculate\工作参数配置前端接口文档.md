# 接口文档

## 接口概述

**基础信息**

- 服务地址：`http://localhost:8080`
- 接口前缀：`/path/workParameter`

---

## 1. 获取所有工作参数配置

### 接口信息
- **URL**：`GET /path/workParameter/list`
- **功能**：获取系统中所有站点的工作参数配置
- **权限**：需要登录

### 请求参数
无

### 响应格式
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "韶关市",
      "maxWorkHours": 8.00,
      "flexibleRange": 0.50,
      "optimalWorkHours": 6.50,
      "stopThreshold": 0.85,
      "createTime": "2025-08-15T00:20:41",
      "updateTime": "2025-08-15T00:20:41",
      "isDeleted": 0
    },
    {
      "id": 2,
      "name": "新丰县中转站",
      "maxWorkHours": 8.00,
      "flexibleRange": 0.50,
      "optimalWorkHours": 6.50,
      "stopThreshold": 0.85,
      "createTime": "2025-08-15T00:20:41",
      "updateTime": "2025-08-15T00:20:41",
      "isDeleted": 0
    }
  ]
}
```

### 字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 参数配置ID |
| name | String | 站点名称 |
| maxWorkHours | Number | 最大工作时间（小时） |
| flexibleRange | Number | 灵活范围（小时） |
| optimalWorkHours | Number | 最优工作时间（小时） |
| stopThreshold | Number | 停止阈值 |
| createTime | String | 创建时间 |
| updateTime | String | 更新时间 |
| isDeleted | Integer | 是否删除（0：未删除，1：已删除） |

---

## 2. 根据站点名称获取工作参数

### 接口信息
- **URL**：`GET /path/workParameter/getByName`
- **功能**：根据站点名称获取对应的工作参数，如果不存在则返回全局参数
- **权限**：需要登录

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| name | String | 是 | 站点名称 | 韶关市 |

### 请求示例
```
GET /path/workParameter/getByName?name=韶关市
```

### 响应格式
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "id": 1,
    "name": "韶关市",
    "maxWorkHours": 8.00,
    "flexibleRange": 0.50,
    "optimalWorkHours": 6.50,
    "stopThreshold": 0.85,
    "createTime": "2025-08-15T00:20:41",
    "updateTime": "2025-08-15T00:20:41",
    "isDeleted": 0
  }
}
```

---

## 3. 更新工作参数

### 接口信息
- **URL**：`POST /path/workParameter/update`
- **功能**：更新单个工作参数配置
- **权限**：需要管理员权限

### 请求参数
```json
{
  "id": 1,
  "name": "韶关市",
  "maxWorkHours": 8.50,
  "flexibleRange": 0.60,
  "optimalWorkHours": 7.00,
  "stopThreshold": 0.90
}
```

### 字段说明
| 字段名 | 类型 | 必填 | 说明 | 约束 |
|--------|------|------|------|------|
| id | Long | 是 | 参数配置ID | 必须存在 |
| name | String | 是 | 站点名称 | 不能为空 |
| maxWorkHours | Number | 是 | 最大工作时间 | 0.1-24.0 |
| flexibleRange | Number | 是 | 灵活范围 | 0.1-12.0 |
| optimalWorkHours | Number | 是 | 最优工作时间 | 0.1-24.0 |
| stopThreshold | Number | 是 | 停止阈值 | 0.1-1.0 |

### 响应格式
```json
{
  "code": 200,
  "msg": "更新成功"
}
```

---

## 4. 批量更新工作参数

### 接口信息
- **URL**：`POST /path/workParameter/batchUpdate`
- **功能**：批量更新多个工作参数配置
- **权限**：需要管理员权限

### 请求参数
```json
[
  {
    "id": 1,
    "name": "韶关市",
    "maxWorkHours": 8.50,
    "flexibleRange": 0.60,
    "optimalWorkHours": 7.00,
    "stopThreshold": 0.90
  },
  {
    "id": 2,
    "name": "新丰县中转站",
    "maxWorkHours": 8.00,
    "flexibleRange": 0.50,
    "optimalWorkHours": 6.50,
    "stopThreshold": 0.85
  }
]
```

### 响应格式
```json
{
  "code": 200,
  "msg": "批量更新成功"
}
```

---

## 5. 获取全局工作参数

### 接口信息
- **URL**：`GET /path/workParameter/global`
- **功能**：获取全局工作参数（韶关市的配置）
- **权限**：需要登录

### 请求参数
无

### 响应格式
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "id": 1,
    "name": "韶关市",
    "maxWorkHours": 8.00,
    "flexibleRange": 0.50,
    "optimalWorkHours": 6.50,
    "stopThreshold": 0.85,
    "createTime": "2025-08-15T00:20:41",
    "updateTime": "2025-08-15T00:20:41",
    "isDeleted": 0
  }
}
```



---

## 站点名称枚举

系统预置的站点名称：
- 韶关市（全局参数）
- 新丰县中转站
- 坪石镇中转站
- 翁源县中转站
- 马市烟叶工作站
- 班组一物流配送中心
- 班组二物流配送中心

---

## 注意事项

1. **全局参数机制**：当查询特定站点参数不存在时，系统会自动返回"韶关市"的全局参数
2. **参数约束**：前端需要对输入参数进行验证，确保数值在合理范围内
