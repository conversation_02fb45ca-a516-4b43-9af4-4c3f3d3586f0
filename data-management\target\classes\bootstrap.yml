#      master:
#        url: jdbc:mysql://**********:3306/ycwl?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8
#        username: root
#        password: 123456
#        driver-class-name: com.mysql.jdbc.Driver
#      slave1:
#        url: jdbc:mysql://**********:3306/ycwl_slave1?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8
#        username: root
#        password: 123456
#        driver-class-name: com.mysql.jdbc.Driver
#      slave2:
#        url: jdbc:mysql://**********:3306/ycwl_slave2?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8
#        username: root
#        password: 123456
#        driver-class-name: com.mysql.jdbc.Driver
#      slave3:
#        url: jdbc:mysql://**********:3306/ycwl_slave3?useSSL=false&allowPublicKeyRetrieval=true&characterEncoding=utf-8
#        username: root
#        password: 123456
#        driver-class-name: com.mysql.jdbc.Driver

spring:
  servlet:
    multipart:
      max-file-size: 20MB   #单文件最大限制
      max-request-size: 200MB #总文件最大限制
  application:
    # 应用名称
    name: datamanagement
  cloud:
        nacos:
          server-addr: localhost:8848
          discovery:
            server-addr: localhost:8848
          config:
            server-addr: localhost:8848
            file-extension: yaml
            shared-configs:
              - data-id: shared-jdbc.yaml
                refresh: true
                group: DEFAULT_GROUP

##    540
#        nacos:
#          server-addr: **********:8848
#          discovery:
#            server-addr: **********:8848
#          config:
#            server-addr: **********:8848
#            file-extension: yaml
#            shared-configs:
#              - data-id: shared-jdbc.yaml
#                refresh: true
#                group: DEFAULT_GROUP

# 烟草//数据库：************
#    nacos:
#      server-addr: ************:8848
#      discovery:
#        server-addr: ************:8848
#      config:
#        server-addr: ************:8848
#        file-extension: yaml
feign:
  client:
    config:
      # 提供方的服务名
      pathcalculate:
        #请求日志级别
        loggerLevel: BASIC
        # 连接超时时间，默认2s，设置单位为毫秒
        connectTimeout: 5000
        # 请求处理超时时间，默认5s，设置单位为毫秒。
        readTimeout: 900000
  httpclient:
    enabled: true # 支持httpclient的开关
    max-connections: 200  # 最大连接数
    max-connections-per-route: 50 # 单个路径的最大连接数

