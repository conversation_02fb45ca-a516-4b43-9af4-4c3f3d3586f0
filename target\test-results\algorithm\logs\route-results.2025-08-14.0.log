2025-08-14 22:46:27.209 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_224627
2025-08-14 22:46:27.209 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_224627
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 22:46:30.310 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.310 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 22:46:30.347 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.347 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 22:46:30.375 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.375 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 22:46:30.473 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.473 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 22:46:30.525 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.525 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 22:46:30.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 22:46:30.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_224627
2025-08-14 22:46:30.574 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 22:46:30.574 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 22:46:33.302 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 22:46:36.007 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 22:46:38.564 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 22:46:41.190 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 22:46:43.747 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 22:46:46.414 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 22:46:49.132 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 22:46:51.727 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 22:46:54.582 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 22:46:54.872 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 22:46:56.014 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 22:46:56.267 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 22:46:57.060 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 22:46:57.062 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 22:46:57.080 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 22:46:57.080 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 22:46:57.080 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 22:46:57.080 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 22:46:57.082 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 22:46:57.098 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 22:46:57.100 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 22:48:10.967 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 22:48:10.967 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 22:48:10.972 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 103759ms, 生成路线: 125, 总工作时间: 91937.5分钟
2025-08-14 23:07:26.854 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_230726
2025-08-14 23:07:26.855 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 23:07:29.803 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 23:07:29.803 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_230726
2025-08-14 23:07:29.803 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 23:07:29.803 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 23:07:29.849 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:07:29.849 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 23:07:29.880 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:07:29.880 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 23:07:29.899 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:07:29.899 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 23:07:29.935 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:07:29.936 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 23:07:29.987 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:07:29.987 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 23:07:30.019 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:07:30.019 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 23:07:30.019 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_230726
2025-08-14 23:07:30.056 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 23:07:30.056 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 23:07:32.761 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 23:07:35.342 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 23:07:37.923 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 23:07:40.505 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 23:07:42.910 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 23:07:45.690 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 23:07:48.424 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 23:07:51.023 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 23:07:53.617 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 23:07:53.889 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 23:07:55.014 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 23:07:55.290 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 23:07:56.093 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 23:07:56.097 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 23:07:56.116 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 23:07:56.117 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 23:07:56.117 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 23:07:56.117 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 23:07:56.119 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 23:07:56.135 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 23:07:56.138 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 23:09:11.621 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 23:09:11.621 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 23:09:11.626 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 104768ms, 生成路线: 125, 总工作时间: 91937.5分钟
2025-08-14 23:18:34.418 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_231834
2025-08-14 23:18:34.420 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 23:18:37.492 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 23:18:37.492 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_231834
2025-08-14 23:18:37.492 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 23:18:37.492 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 23:18:37.507 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:18:37.507 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 23:18:37.531 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:18:37.531 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 23:18:37.546 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:18:37.546 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 23:18:37.577 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:18:37.577 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 23:18:37.625 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:18:37.625 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 23:18:37.652 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:18:37.652 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 23:18:37.653 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_231834
2025-08-14 23:18:37.680 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 23:18:37.680 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 23:18:37.681 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 23:18:37.682 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 23:18:37.683 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 23:18:37.683 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 23:18:37.689 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 23:18:37.690 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 23:18:37.691 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 23:18:37.691 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 23:18:37.692 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 23:18:37.715 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 23:18:37.722 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 23:18:37.730 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 23:18:37.733 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 23:18:37.737 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 23:18:37.750 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 23:18:37.750 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 23:18:37.750 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 23:18:37.750 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 23:18:37.753 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 23:18:37.756 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 23:18:37.758 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 23:23:33.982 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_232333
2025-08-14 23:23:33.983 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 23:23:37.635 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 23:23:37.635 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_232333
2025-08-14 23:23:37.635 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 23:23:37.635 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 23:23:37.702 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:23:37.702 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 23:23:37.744 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:23:37.744 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 23:23:37.770 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:23:37.770 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 23:23:37.822 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:23:37.822 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 23:23:37.888 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:23:37.888 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 23:23:37.926 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:23:37.927 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 23:23:37.927 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_232333
2025-08-14 23:23:37.969 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 23:23:37.969 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 23:23:41.039 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 23:23:43.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 23:23:46.772 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 23:23:49.604 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 23:23:52.227 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 23:23:55.296 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 23:23:58.525 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 23:24:01.680 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 23:24:04.851 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 23:24:05.169 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 23:24:06.531 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 23:24:06.889 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 23:24:07.896 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 23:24:07.907 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 23:24:07.947 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 23:24:07.947 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 23:24:07.947 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 23:24:07.947 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 23:24:07.951 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 23:24:08.019 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 23:24:08.026 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 23:25:29.509 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 23:25:29.509 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 23:25:29.515 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 115533ms, 生成路线: 125, 总工作时间: 91937.5分钟
2025-08-14 23:36:05.798 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_233605
2025-08-14 23:36:05.798 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 23:36:08.832 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 23:36:08.832 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_233605
2025-08-14 23:36:08.832 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 23:36:08.832 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 23:36:08.882 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:36:08.882 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 23:36:08.914 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:36:08.914 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 23:36:08.934 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:36:08.934 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 23:36:08.970 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:36:08.970 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 23:36:09.022 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:36:09.022 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 23:36:09.054 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:36:09.055 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 23:36:09.055 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_233605
2025-08-14 23:36:09.083 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 23:36:09.083 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 23:36:11.806 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 23:36:14.642 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 23:36:17.243 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 23:36:20.092 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 23:36:22.478 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 23:36:25.191 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 23:36:27.950 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 23:36:30.625 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 23:36:33.422 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 23:36:33.747 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 23:36:34.824 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 23:36:35.095 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 23:36:35.857 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 23:36:35.859 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 23:36:35.877 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 23:36:35.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 23:36:35.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 23:36:35.877 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 23:36:35.880 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 23:36:35.893 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 23:36:35.897 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 23:37:58.784 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 23:37:58.790 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 23:37:58.835 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 112993ms, 生成路线: 125, 总工作时间: 91937.5分钟
2025-08-14 23:38:50.995 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_233850
2025-08-14 23:38:50.996 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 23:38:53.705 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 23:38:53.706 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_233850
2025-08-14 23:38:53.706 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 23:38:53.706 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 23:38:53.720 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:38:53.720 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 23:38:53.745 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:38:53.745 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 23:38:53.762 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:38:53.762 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 23:38:53.795 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:38:53.795 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 23:38:53.848 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:38:53.848 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 23:38:53.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:38:53.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 23:38:53.877 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_233850
2025-08-14 23:38:53.888 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 23:38:53.888 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 23:38:53.889 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 23:38:53.890 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 23:38:53.890 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 23:38:53.891 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 23:38:53.893 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 23:38:53.894 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 23:38:53.894 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 23:38:53.895 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 23:38:53.896 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 23:38:53.903 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 23:38:53.907 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 23:38:53.914 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 23:38:53.917 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 23:38:53.918 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 23:38:53.928 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 23:38:53.928 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 23:38:53.928 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 23:38:53.928 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 23:38:53.930 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 23:38:53.933 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 23:38:53.935 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 23:40:17.085 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 23:40:17.085 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 23:40:17.086 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 86091ms, 生成路线: 125, 总工作时间: 91937.5分钟
2025-08-14 23:41:09.510 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_234109
2025-08-14 23:41:09.510 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 23:41:12.597 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 23:41:12.597 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_234109
2025-08-14 23:41:12.597 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 23:41:12.597 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 23:41:12.616 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:41:12.616 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 23:41:12.641 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:41:12.641 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 23:41:12.658 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:41:12.659 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 23:41:12.691 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:41:12.691 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 23:41:12.738 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:41:12.738 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 23:41:12.766 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 23:41:12.766 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 23:41:12.766 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_234109
2025-08-14 23:41:12.777 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 23:41:12.777 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 23:41:12.778 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 23:41:12.778 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 23:41:12.780 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 23:41:12.780 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 23:41:12.787 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 23:41:12.788 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 23:41:12.789 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 23:41:12.789 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 23:41:12.791 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 23:41:12.798 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 23:41:12.802 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 23:41:12.818 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 23:41:12.823 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 23:41:12.824 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 23:41:12.842 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 23:41:12.842 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 23:41:12.842 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 23:41:12.842 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 23:41:12.844 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 23:41:12.846 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 23:41:12.849 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 23:42:25.951 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 23:42:25.951 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 23:42:25.952 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 76445ms, 生成路线: 125, 总工作时间: 91937.5分钟
