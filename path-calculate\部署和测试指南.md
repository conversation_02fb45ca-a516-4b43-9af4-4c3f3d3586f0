# 粤北卷烟物流管理系统 - 新算法部署和测试指南

## 部署步骤

### 1. 确认环境准备

确保以下环境已准备就绪：
- Java 8 或更高版本
- MySQL 数据库 (localhost:3307/ycdb)
- Maven 3.6+
- Spring Boot 2.x

### 2. 数据库配置

确认数据库连接配置正确：
```properties
spring.datasource.url=********************************
spring.datasource.username=root
spring.datasource.password=aA13717028793#
```

### 3. 编译和启动

```bash
# 进入项目目录
cd path-calculate

# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 4. 验证启动

访问健康检查接口：
```
GET http://localhost:8080/api/new-algorithm/health
```

## 测试步骤

### 1. 基础功能测试

#### 1.1 数据加载测试
```bash
curl -X GET "http://localhost:8080/api/new-algorithm/test-data-loading"
```

预期结果：
```json
{
  "success": true,
  "message": "数据加载成功",
  "data": {
    "accumulationCount": 150,
    "transitDepotCount": 5,
    "teamCount": 3,
    "timeMatrixSize": 2250
  }
}
```

#### 1.2 算法配置测试
```bash
curl -X GET "http://localhost:8080/api/new-algorithm/config"
```

#### 1.3 完整算法流程测试
```bash
curl -X POST "http://localhost:8080/api/new-algorithm/test-full-execution?apiKey=test-api-key"
```

### 2. 集成接口测试

#### 2.1 使用新算法
```bash
curl -X GET "http://localhost:8080/path/calculateAll?apiKey=a123fae9da370c45984c58720bf3ac7c&useNewAlgorithm=true"
```

#### 2.2 使用旧算法（对比测试）
```bash
curl -X GET "http://localhost:8080/path/calculateAll?apiKey=a123fae9da370c45984c58720bf3ac7c&useNewAlgorithm=false"
```

### 3. 前端集成测试

在前端应用中调用：
```javascript
// 使用新算法
fetch('/path/calculateAll?apiKey=a123fae9da370c45984c58720bf3ac7c&useNewAlgorithm=true')
  .then(response => response.json())
  .then(data => {
    console.log('新算法结果:', data);
  });

// 使用旧算法
fetch('/path/calculateAll?apiKey=a123fae9da370c45984c58720bf3ac7c&useNewAlgorithm=false')
  .then(response => response.json())
  .then(data => {
    console.log('旧算法结果:', data);
  });
```

## 配置说明

### 算法配置文件

在 `application.yml` 中添加：

```yaml
ycwl:
  algorithm:
    enable-new-algorithm: true      # 启用新算法
    timeout-ms: 300000             # 超时时间5分钟
    enable-fallback: true          # 启用降级
    max-retry-count: 3             # 最大重试次数
    params:
      max-route-count: 50          # 最大路线数
      min-accumulation-count: 2    # 最小聚集区数
      average-speed: 30.0          # 平均速度km/h
```

### 日志配置

```yaml
logging:
  level:
    com.ict.ycwl.pathcalculate.algorithm: DEBUG
    com.ict.ycwl.pathcalculate.service.adapter: DEBUG
    com.ict.ycwl.pathcalculate.controller.NewAlgorithmController: INFO
```

## 性能监控

### 1. 执行时间监控

新算法会记录详细的执行时间：
- 数据加载时间
- 算法计算时间
- 结果转换时间
- 总执行时间

### 2. 内存使用监控

建议监控以下指标：
- JVM堆内存使用
- 算法执行期间的内存峰值
- 垃圾回收频率

### 3. 成功率监控

监控算法执行的成功率：
- 新算法成功率
- 降级到旧算法的频率
- 总体成功率

## 故障排除

### 常见问题

#### 1. 数据加载失败
**症状**: 接口返回数据加载失败
**解决方案**:
- 检查数据库连接
- 确认相关表有数据
- 查看详细错误日志

#### 2. 算法执行超时
**症状**: 算法执行时间过长
**解决方案**:
- 增加超时时间配置
- 检查数据量是否过大
- 优化算法参数

#### 3. 结果转换失败
**症状**: 算法执行成功但结果转换失败
**解决方案**:
- 检查算法结果格式
- 确认API密钥有效
- 查看转换逻辑日志

### 日志分析

关键日志位置：
```
com.ict.ycwl.pathcalculate.service.adapter.DatabaseToAlgorithmAdapter
com.ict.ycwl.pathcalculate.service.NewAlgorithmService
com.ict.ycwl.pathcalculate.controller.PathController
```

### 性能调优

#### 1. 数据库优化
- 为相关查询添加索引
- 优化查询语句
- 考虑数据缓存

#### 2. 算法参数调优
- 根据实际数据调整参数
- 平衡计算精度和执行时间
- 监控资源使用情况

#### 3. 系统资源调优
- 调整JVM堆内存大小
- 优化垃圾回收策略
- 考虑使用异步执行

## 生产环境部署

### 1. 配置检查清单

- [ ] 数据库连接配置正确
- [ ] 算法参数配置合理
- [ ] 日志级别设置为INFO或WARN
- [ ] 监控和告警配置完成
- [ ] 备份和恢复策略制定

### 2. 部署策略

建议采用灰度发布：
1. 先在测试环境充分测试
2. 生产环境先启用新算法但保持降级开关
3. 监控一段时间后完全切换到新算法
4. 保留旧算法作为应急备份

### 3. 回滚计划

如果新算法出现问题：
1. 立即设置 `useNewAlgorithm=false`
2. 重启应用确保配置生效
3. 分析问题原因
4. 修复后重新部署

## 联系支持

如遇到问题，请提供以下信息：
- 错误日志
- 系统配置
- 数据样本
- 复现步骤

技术支持联系方式：[待补充]
