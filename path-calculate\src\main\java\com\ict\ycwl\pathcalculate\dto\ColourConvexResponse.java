package com.ict.ycwl.pathcalculate.dto;

import lombok.Data;
import java.util.List;

/**
 * 色块数据响应DTO
 * 包含班组颜色信息的色块数据
 */
@Data
public class ColourConvexResponse {
    
    /**
     * 班组ID
     */
    private Long groupId;
    
    /**
     * 班组名称
     */
    private String groupName;
    
    /**
     * 班组颜色
     */
    private String colour;
    
    /**
     * 该班组的色块坐标数据列表
     * 每个字符串表示一个多边形的坐标点串，格式：经度,纬度;经度,纬度;...
     */
    private List<String> polygons;
    
    public ColourConvexResponse() {}
    
    public ColourConvexResponse(Long groupId, String groupName, String colour, List<String> polygons) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.colour = colour;
        this.polygons = polygons;
    }
}
