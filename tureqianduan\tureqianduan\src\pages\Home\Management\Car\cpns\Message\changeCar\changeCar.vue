<template>
  <div class="changeCarDialog">
    <el-dialog v-model="changeCarOpen" title="修改车辆信息" width="60%" height="70%" :close-on-click-modal="false" @close="closeChange">
      <div class="flex-center">
        <el-form label-width="auto" class="areaForm" :model="totalFields" ref="formRef" :rules="rules">
        <div class="flex">
          <el-form-item label="车牌号">
            <p style="width: 250px;font-size: 20px;">
              {{ carName }}
            </p>
        </el-form-item>
        <el-form-item label="配送域" prop="deliveryAreaName">
          <el-select
            style="width: 250px;"
            placeholder="请选择"
            v-model="totalFields.deliveryAreaName"
          >
          <el-option :label="item" :value="item" v-for="(item) in store.carAddInfo.deliveryList" :key="item +'9'"/>
          </el-select>
        </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="驾驶人" prop="carDriverName">
          <el-select
            style="width: 250px;"
            placeholder="请选择"
            v-model="totalFields.carDriverName"
            @change="completePhone"
          >
          <el-option :label="item.userName" :value="item.userName" v-for="(item) in store.carAddInfo.carDriver" :key="item +'9'"/>
          </el-select>
        </el-form-item>
        <el-form-item label="电话" prop="carDriverPhone">
          <el-select
            style="width: 250px;"
            placeholder="请选择"
            v-model="totalFields.carDriverPhone"
            @change="completeDriver"
          >
          <el-option :label="item.phone" :value="item.phone" v-for="(item) in store.carAddInfo.carDriver" :key="item +'9'"/>
          </el-select>
        </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="最大载重" prop="maxLoad" :style="{display: 'flex'}">
            <el-input
              style="width: 200px;"
              placeholder="请输入"
              v-model="totalFields.maxLoad"
            >
            </el-input>
            <div class="tons">吨</div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            style="width: 250px;"
            placeholder="请选择"
            v-model="totalFields.status"
          >
          <el-option :label="item" :value="item" v-for="(item) in store.carAddInfo.status" :key="item +'9'"/>
          </el-select>
        </el-form-item>
        </div>
      </el-form>
      </div>
      <div class="btns">
        <el-button type="primary" @click="closeChange">取消</el-button>
        <el-button type="primary" style="margin-left: 100px" @click="confirmChange"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { carStore } from "@/store/managerment/car";
const store = carStore()
const changeCarOpen = ref(false);
defineExpose({
  changeCarOpen
});
const props = defineProps(['carId', 'carName', 'info'])
const formRef = ref<any>()
const changeEmit = defineEmits(["confirmChange"]);
const totalFields = ref<any>({
  carDriverName: '',
  carDriverPhone: '',
  deliveryAreaName: '',
  maxLoad: '',
  status: '',
})

onUpdated(() => {
  totalFields.value.carDriverName = props.info.userName
  totalFields.value.carDriverPhone = props.info.phone
  totalFields.value.maxLoad = props.info.maxLoad
  totalFields.value.status = props.info.status
  totalFields.value.deliveryAreaName = props.info.deliveryAreaName
})

const validateMaxLoad = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入吨数'));
  } else if (isNaN(value) || value.trim() === '') {
    callback(new Error('请输入数字'));
  } else if (Number(value) > 2) {
    callback(new Error('不能超过2吨'));
  } else {
    callback(); // 验证通过
  }
}

const rules = reactive({
  maxLoad: [
    {
      validator: validateMaxLoad, trigger: 'blur'
    },
  ],
})

//确认修改
async function confirmChange() {
  const totalData : any = {
    licensePlateNumber: props.carName,
    carId: Number(props.carId),
    ...totalFields.value
  }
  if (!formRef.value) return
    formRef.value.validate((valid : any) => {
    if (valid) {
      store.updateCarData(totalData).then((res) => {
        formRef.value.resetFields()
        changeCarOpen.value = false
        if(res.code === 50001 || res.message === '系统异常') {
            ElMessage.error('系统异常!')
            return
          }
        changeEmit("confirmChange");
      }) 
    } else {
      ElMessage({
        message: '修改失败!',
        type: 'warning',
      })
      
    }
  })
  
}

//取消修改
const closeChange = () => {
  formRef.value.resetFields()
  changeCarOpen.value = false
};

function completeDriver() {
    totalFields.value.carDriverName = store.carAddInfo.carDriver.find((item: any) => 
      item.phone === totalFields.value.carDriverPhone
    ).userName
  }

  function completePhone() {
    totalFields.value.carDriverPhone = store.carAddInfo.carDriver.find((item: any) => 
      item.userName === totalFields.value.carDriverName
    ).phone
  }

</script>

<style lang="less" scoped>
.flex-center {
width: 100%;
display: flex;
justify-content: center;
}
.flex {
display: flex;
}
.areaForm {
  .areaItem {
    font-size: 20px;
  }
}
.btns {
  display: flex;
  justify-content: center;
  color: black;
}

.transform {
  .content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    font-size: 20px;
  }
}

.tons {
  font-size: 20px;
  margin-left: 20px;
}
</style>
