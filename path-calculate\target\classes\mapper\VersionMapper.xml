<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.VersionMapper">

    <select id="selectById" resultType="java.lang.String">
        select version_db from version where version_id=#{version} and is_show=1
    </select>

    <select id="selectList" resultType="com.ict.ycwl.pathcalculate.pojo.Version">
        select * from version where is_show=1
    </select>

    <update id="updateById">
        update version set version_name=#{versionName},version_info=#{versionInfo} where version_id=#{versionId}
    </update>

    <delete id="deleteIsShowById">
        update version set is_show =0 where version_id=#{versionId}
    </delete>



    <select id="MySelectOne" resultType="com.ict.ycwl.pathcalculate.pojo.Version">
        select * from version where is_show=0 and version_id!=1 limit 1
    </select>

    <update id="MyUpdateById" >
        update version set version_name=#{versionName},version_info=#{versionInfo},is_show=1 where version_id=#{versionId}
    </update>

    <select id="selectAllList" resultType="com.ict.ycwl.pathcalculate.pojo.Version">
        select * from version
    </select>
</mapper>