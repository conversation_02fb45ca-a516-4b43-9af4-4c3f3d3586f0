import request from "../../index";

import type {
    ICarAddData,
    ICarDeleteData,
    ICarGetData,
    ICarUpdateData,
    IAcutualAddCar,
    IAcutualDeleteCar,
    IAcutualGetCar,
    IAcutualUpdateCar,
    ILogDelete,
    ILogDownload,
    IFromDownload,
    ILogImport
} from '@/types/car'
import { IRequest } from "../../request/type";

//添加车辆
export function addCar(params: ICarAddData) {
    return request.post<IRequest<ICarAddData>>({
        url: '/datamanagement/addCar',
        params,
    })
}

//删除车辆
export function deleteCar(params: ICarDeleteData) {
    return request.delete<IRequest<ICarDeleteData>>({
        url: '/datamanagement/deleteCar',
        params,
    })
}

//更新车辆
export function updateCar(params: ICarUpdateData) {
    return request.post<IRequest<ICarUpdateData>>({
        url: '/datamanagement/updateCar',
        params,
    })
}

//获取车辆
export function getCar(params: ICarGetData) {
    return request.get<IRequest<ICarGetData>>({
        url: '/datamanagement/carList',
        params,
    })
}

//搜索车辆
export function searchCarInfo() {
    return request.post<IRequest<any>>({
        url: '/datamanagement/selectDownBox',
    })
}

//添加车辆的固定信息
export function addCarInfo() {
    return request.post<IRequest<any>>({
        url: '/datamanagement/getAddCarDownBox',
    })
}

//添加实情
export function addActualCar(params: IAcutualAddCar) {
    return request.post<IRequest<IAcutualAddCar>>({
        url: '/datamanagement/addCarActualList',
        params
    })
}

//删除实情
export function deleteActualCar(params: IAcutualDeleteCar) {
    return request.delete<IRequest<IAcutualDeleteCar>>({
        url: '/datamanagement/deleteCarActual',
        params
    })
}

//获得实情
export function getActualCar(params: IAcutualGetCar) {
    return request.get<IRequest<IAcutualGetCar>>({
        url: '/datamanagement/carActualList',
        params
    })
}

//修改实情
export function updateActualCar(params: IAcutualUpdateCar) {
    return request.post<IRequest<IAcutualUpdateCar>>({
        url: '/datamanagement/updateCarActual',
        params
    })
}

//获得实情下拉框
export function getActualCarDownBox() {
    return request.post<IRequest<any>>({
        url: '/datamanagement/carActualDownBox',
    })
}

//导入实情
export function importActual(formData: FormData, config: any) {
    return request.post<IRequest<any>>({
        url: '/datamanagement/carImport',
        data: formData,
        headers: {
            'accept': '*/*',
            'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: config.onUploadProgress,
        signal: config.signal
    })
}

//导入日志 
export function importLog(params: ILogImport) {
    return request.get<IRequest<ILogImport>>({
        url: '/datamanagement/getImportLogs',
        params
    })
}

//删除日志
export function deleteLog(params: ILogDelete) {
    return request.delete<IRequest<ILogDelete>>({
        url: '/datamanagement/deleteImportLogs',
        params
    })
}


//下载日志
export function downloadLog(params: ILogDownload) {
    return request.get<IRequest<ILogDownload>>({
        url: '/datamanagement/downloadLogs',
        headers: { 'Content-Type': 'application/x-download' },
        responseType: 'blob', 
        params
    })
}

//下载空白表格
export function downloadNullFrom(params: IFromDownload) {
    return request.post<IRequest<IFromDownload>>({
        url: '/datamanagement/downloadNullFrom',
        headers: { 'Content-Type': 'application/x-download' },
        responseType: 'blob', 
        params
    })
}