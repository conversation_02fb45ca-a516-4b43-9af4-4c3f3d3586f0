<template>
  <el-dialog v-model="visible" title="应用参数并计算" width="480px" @close="handleClose">
    <el-form label-width="160px">
      <el-form-item label="最大工作时长(小时)">
        <el-input-number v-model="form.maxWorkHours" :min="4" :max="12" :step="0.5" :precision="1" />
      </el-form-item>
      <el-form-item label="弹性区间(小时)">
        <el-input-number v-model="form.flexibleRange" :min="0" :max="2" :step="0.5" :precision="1" />
      </el-form-item>
      <el-form-item label="最优工作时长(小时)">
        <el-input-number v-model="form.optimalWorkHours" :min="4" :max="10" :step="0.5" :precision="1" />
      </el-form-item>
      <el-form-item label="停止阈值">
        <el-input-number v-model="form.stopThreshold" :min="0.5" :max="1" :step="0.05" :precision="2" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, defineExpose } from 'vue';

const visible = ref(false);

const form = reactive({
  maxWorkHours: 8.0,
  flexibleRange: 0.5,
  optimalWorkHours: 6.5,
  stopThreshold: 0.85,
});

function openDialog() {
  visible.value = true;
}
function handleClose() {
  visible.value = false;
}
function handleConfirm() {
  visible.value = false;
  // 可在此处添加应用参数的确认逻辑
}
defineExpose({ openDialog });
</script>
