# Travel Time数据验证测试指南

## 概述
`TravelTimeValidationTest` 是用于验证OSRM生成的280万条travel_time数据准确性的测试类。通过高德地图API进行小样本抽样对比，评估数据质量。

## 使用前准备

### 1. 获取高德API Key
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册账号并创建应用
3. 获取Web服务API Key
4. 在测试类中替换 `AMAP_API_KEY` 常量

### 2. 配置测试参数
```java
// 测试配置
private static final int SAMPLE_SIZE = 50;           // 抽样数量，建议50条以内
private static final int REQUEST_DELAY_MS = 200;     // 请求间隔200ms，避免超限
private static final double TOLERANCE_PERCENTAGE = 20.0; // 允许误差20%
```

### 3. 高德API配额说明
- 个人开发者：每日5000次免费调用
- 企业开发者：根据套餐不同
- 建议控制测试样本在50条以内，避免超限

## 测试流程

### 1. 数据抽样
- 从travel_time表随机抽取指定数量的样本
- 自动过滤异常坐标（0,0坐标或超出中国范围）
- 确保样本数据有效性

### 2. API对比验证
- 调用高德路径规划API获取实际行驶时间
- 与OSRM数据进行对比
- 计算误差百分比

### 3. 结果评估
- **通过**：误差在允许范围内（默认20%）
- **失败**：误差超过允许范围
- **错误**：API调用失败或数据异常

## 运行测试

### 方法1：IDE运行
1. 在IDEA中打开 `TravelTimeValidationTest.java`
2. 配置高德API Key
3. 右键运行 `testTravelTimeAccuracy()` 方法

### 方法2：Maven命令
```bash
mvn test -Dtest=TravelTimeValidationTest#testTravelTimeAccuracy
```

## 测试报告

测试完成后会生成详细报告：
- 文件位置：`target/test-results/travel_time_validation_yyyyMMdd_HHmmss.txt`
- 包含内容：
  - 验证统计摘要
  - 每条样本的详细对比结果
  - 误差分析和质量评估

### 报告示例
```
Travel Time数据验证报告
==================================================
生成时间: 2025-08-17T15:30:00
抽样数量: 50
验证通过: 42
验证失败: 6
请求错误: 2
通过率: 84.0%
允许误差: 20.0%

详细验证结果:
序号	OSRM时间	高德时间	误差%	状态	起点坐标	终点坐标
1	15.2	14.8	2.7	通过	113.596766,24.810403	113.598123,24.812456
2	45.6	52.3	14.7	通过	114.124922,23.996005	114.120966,23.999628
...

统计摘要:
平均误差: 12.3%
最大误差: 35.2%
最小误差: 1.1%

结论:
✅ OSRM数据质量良好，通过率84.0%
```

## 质量评估标准

### 通过率评估
- **≥80%**：数据质量良好 ✅
- **60-80%**：数据质量一般，建议检查 ⚠️
- **<60%**：数据质量较差，建议重新生成 ❌

### 常见问题分析

#### 1. 误差较大的原因
- **路径选择差异**：OSRM与高德的路径规划算法不同
- **实时路况**：高德考虑实时路况，OSRM使用静态数据
- **道路更新**：地图数据版本差异
- **计算方式**：不同的时间计算模型

#### 2. API调用失败
- **配额超限**：检查API调用次数
- **网络问题**：确保网络连接正常
- **坐标异常**：过滤无效坐标数据
- **API Key**：确认Key有效且权限正确

## 注意事项

### 1. 请求频率控制
- 默认200ms间隔，避免触发高德API限流
- 可根据实际配额调整 `REQUEST_DELAY_MS`

### 2. 样本选择
- 建议样本数量控制在50条以内
- 优先选择有代表性的路径（不同距离、不同区域）

### 3. 误差容忍度
- 20%误差是合理范围，考虑到算法差异
- 可根据业务需求调整 `TOLERANCE_PERCENTAGE`

### 4. 数据解读
- 重点关注系统性偏差，而非个别异常值
- 结合实际业务场景评估数据可用性

## 扩展功能

### 1. 分区域验证
可以修改抽样逻辑，按中转站或地理区域分别验证：
```java
// 按中转站分组验证
List<TravelTime> samples = getSamplesByDepot(depotId);
```

### 2. 批量验证
对于大规模验证，可以考虑：
- 使用高德批量路径规划API
- 实现异步并发调用
- 添加断点续传功能

### 3. 多API对比
可以集成其他地图服务API进行交叉验证：
- 百度地图API
- 腾讯地图API
- Google Maps API（需要特殊网络环境）

## 故障排除

### 1. 编译错误
- 确保Java版本≥11（HttpClient需要）
- 检查Maven依赖是否完整

### 2. 运行时错误
- 检查数据库连接配置
- 确认travel_time表存在且有数据
- 验证高德API Key有效性

### 3. 结果异常
- 检查坐标数据格式
- 确认时间单位一致（分钟）
- 验证API返回数据格式

## 联系支持
如有问题，请联系算法团队或查看相关文档。
