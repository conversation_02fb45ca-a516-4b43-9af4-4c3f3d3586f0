<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.UserMapper">

    <select id="selectUserByGroupId" resultType="com.ict.ycwl.guestbook.api.vo.UserVo" parameterType="Long">
        SELECT user_id,user_name,avatar_path FROM `user` WHERE user_id IN(
            SELECT user_id FROM user_group WHERE group_id = #{groupId})
    </select>

    <select id="selectUserForCondition" parameterType="String" resultType="com.ict.ycwl.guestbook.api.vo.ConditionUserVo">
        SELECT user_name,work_number FROM `user` WHERE `status` = '1' AND position = #{position}
    </select>

</mapper>