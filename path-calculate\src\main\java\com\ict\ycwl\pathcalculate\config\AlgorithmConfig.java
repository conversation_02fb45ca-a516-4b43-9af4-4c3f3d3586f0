package com.ict.ycwl.pathcalculate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 算法配置类
 * 用于管理路径规划算法的配置参数
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-11
 */
@Data
@Component
@ConfigurationProperties(prefix = "ycwl.algorithm")
public class AlgorithmConfig {

    /**
     * 是否启用新算法
     * true: 使用PathPlanningUtils新算法
     * false: 使用原有算法
     */
    private boolean enableNewAlgorithm = true;

    /**
     * 算法超时时间（毫秒）
     */
    private long timeoutMs = 300000; // 5分钟

    /**
     * 是否启用算法降级
     * 当新算法失败时，是否自动降级到旧算法
     */
    private boolean enableFallback = true;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 算法执行模式
     * SYNC: 同步执行
     * ASYNC: 异步执行
     */
    private ExecutionMode executionMode = ExecutionMode.SYNC;

    /**
     * 数据验证开关
     * 是否在算法执行前验证输入数据
     */
    private boolean enableDataValidation = true;

    /**
     * 结果缓存开关
     * 是否缓存算法计算结果
     */
    private boolean enableResultCache = false;

    /**
     * 缓存过期时间（秒）
     */
    private long cacheExpireSeconds = 3600; // 1小时

    /**
     * 日志级别
     * DEBUG: 详细日志
     * INFO: 基本日志
     * WARN: 警告日志
     * ERROR: 错误日志
     */
    private LogLevel logLevel = LogLevel.INFO;

    /**
     * 算法参数配置
     */
    private AlgorithmParams params = new AlgorithmParams();

    /**
     * 执行模式枚举
     */
    public enum ExecutionMode {
        SYNC,   // 同步执行
        ASYNC   // 异步执行
    }

    /**
     * 日志级别枚举
     */
    public enum LogLevel {
        DEBUG,
        INFO,
        WARN,
        ERROR
    }

    /**
     * 算法参数配置
     */
    @Data
    public static class AlgorithmParams {
        
        /**
         * 最大路线数量
         */
        private int maxRouteCount = 50;

        /**
         * 最小聚集区数量（少于此数量不执行算法）
         */
        private int minAccumulationCount = 2;

        /**
         * 最大工作时间（小时）
         */
        private double maxWorkTimeHours = 8.0;

        /**
         * 平均行驶速度（km/h）
         */
        private double averageSpeed = 30.0;

        /**
         * 配送时间系数
         * 用于计算每个聚集区的配送时间
         */
        private double deliveryTimeFactor = 1.0;

        /**
         * 是否考虑交通拥堵
         */
        private boolean considerTraffic = true;

        /**
         * 是否优化路线顺序
         */
        private boolean optimizeRouteOrder = true;

        /**
         * 路线平衡系数
         * 用于平衡各路线的工作量
         */
        private double routeBalanceFactor = 0.8;
    }

    /**
     * 获取算法描述信息
     */
    public String getAlgorithmDescription() {
        return String.format("算法配置 - 新算法: %s, 超时: %dms, 降级: %s, 执行模式: %s", 
                enableNewAlgorithm, timeoutMs, enableFallback, executionMode);
    }

    /**
     * 验证配置参数的有效性
     */
    public boolean isValid() {
        if (timeoutMs <= 0) {
            return false;
        }
        if (maxRetryCount < 0) {
            return false;
        }
        if (params.maxRouteCount <= 0) {
            return false;
        }
        if (params.minAccumulationCount < 0) {
            return false;
        }
        if (params.maxWorkTimeHours <= 0) {
            return false;
        }
        if (params.averageSpeed <= 0) {
            return false;
        }
        return true;
    }
}
