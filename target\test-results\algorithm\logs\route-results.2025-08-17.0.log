2025-08-17 22:44:36.003 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 7, 调试会话: debug_20250817_224435
2025-08-17 22:44:36.145 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-17 22:44:42.496 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 7 个中转站分组
2025-08-17 22:44:42.496 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250817_224435
2025-08-17 22:44:42.498 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-17 22:44:42.498 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县对接点 分配 118 个聚集区到 10 条路线
2025-08-17 22:44:42.640 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.640 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇对接点 分配 45 个聚集区到 9 条路线
2025-08-17 22:44:42.654 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.654 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县对接点 分配 168 个聚集区到 10 条路线
2025-08-17 22:44:42.683 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.683 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-17 22:44:42.736 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.736 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-17 22:44:42.793 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.793 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-17 22:44:42.821 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.822 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 乐昌对接点 分配 192 个聚集区到 10 条路线
2025-08-17 22:44:42.843 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 22:44:42.844 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-17 22:44:42.844 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 7, 会话ID: debug_20250817_224435
2025-08-17 22:44:42.897 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-17 22:44:42.897 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-17 22:44:45.090 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-17 22:44:47.157 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-17 22:44:49.217 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-17 22:44:51.163 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-17 22:44:53.228 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-17 22:44:55.311 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-17 22:44:57.392 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-17 22:44:59.075 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-17 22:44:59.504 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-17 22:45:00.136 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-17 22:45:00.790 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-17 22:45:02.478 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-17 22:45:03.527 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-17 22:45:03.530 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-17 22:45:03.546 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-17 22:45:03.546 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 49, 超30分钟差距中转站: 7
2025-08-17 22:45:03.546 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-17 22:45:03.546 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-17 22:45:03.548 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-17 22:45:03.563 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-17 22:45:03.565 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-17 22:46:02.281 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 186, 中转站调整: 1
2025-08-17 22:46:02.281 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-17 22:46:02.289 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 86292ms, 生成路线: 125, 总工作时间: 91449.9分钟
2025-08-17 23:28:30.134 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 7, 调试会话: debug_20250817_232830
2025-08-17 23:28:30.134 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-17 23:28:36.103 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 7 个中转站分组
2025-08-17 23:28:36.103 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250817_232830
2025-08-17 23:28:36.103 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-17 23:28:36.103 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县对接点 分配 118 个聚集区到 10 条路线
2025-08-17 23:28:36.151 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.151 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇对接点 分配 45 个聚集区到 9 条路线
2025-08-17 23:28:36.159 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.159 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县对接点 分配 168 个聚集区到 10 条路线
2025-08-17 23:28:36.180 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.180 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-17 23:28:36.227 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.227 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-17 23:28:36.280 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.280 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-17 23:28:36.312 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.312 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 乐昌对接点 分配 192 个聚集区到 10 条路线
2025-08-17 23:28:36.331 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-17 23:28:36.331 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-17 23:28:36.331 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 7, 会话ID: debug_20250817_232830
2025-08-17 23:28:36.362 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-17 23:28:36.362 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-17 23:28:38.638 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-17 23:28:40.888 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-17 23:28:43.205 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-17 23:28:45.470 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-17 23:28:47.692 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-17 23:28:49.946 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-17 23:28:52.155 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-17 23:28:54.403 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-17 23:28:57.054 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-17 23:28:59.307 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-17 23:29:01.506 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-17 23:29:03.726 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-17 23:29:04.883 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-17 23:29:04.886 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-17 23:29:04.917 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-17 23:29:04.917 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 27, 超30分钟差距中转站: 7
2025-08-17 23:29:04.917 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-17 23:29:04.917 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-17 23:29:04.919 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-17 23:29:04.932 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-17 23:29:04.935 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-17 23:30:16.812 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 150, 中转站调整: 1
2025-08-17 23:30:16.812 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-17 23:30:16.820 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 106680ms, 生成路线: 125, 总工作时间: 53067.4分钟
