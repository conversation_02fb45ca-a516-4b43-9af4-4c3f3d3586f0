<template>
  <div class="MilkRun">
    <div class="content">
      <RouterView></RouterView>
    </div>
    <div class="btn-content" v-if="!route.meta.isShow">
      <el-badge class="item">
        <el-button
          :class="{ vertical: true, active: currentRoute === 'pickup' }"
          @click="routerChange('pickup')"
          >取货户分析</el-button
        >
      </el-badge>
      <el-badge class="item">
        <el-button
          :class="{ vertical: true, active: currentRoute === 'pos' }"
          @click="routerChange('pos')"
          >选址分析</el-button
        >
      </el-badge>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter, useRoute } from "vue-router";
  const router = useRouter();
  const route = useRoute();
  const currentRoute = ref(route.name);
  function routerChange(route: any) {
    router.push(`/home/<USER>/MilkRun/${route}`);
    currentRoute.value = route;
  }
</script>

<style lang="less" scoped>
  .MilkRun {
    display: flex;
    .btn-content {
      width: 20px;
      display: flex;
      flex-direction: column;

      .el-button {
        --el-button-bg-color: #15335f;
        border: 0;
        color: #84aeeb;
        --el-button-text-color: #73e1ff;
        height: 16vh !important;
        width: 2.2vw;
        font-size: 1.2vw !important;
        border-radius: 0;
      }

      .el-button:hover {
        color: #041c3f;
        background: #84aeeb;
      }

      .el-button.active {
        color: #041c3f;
        background-color: #84aeeb;
        outline: none;
      }
    }
    .content {
      margin-left: 1.5vw;
      flex: 1;
    }
  }
</style>
